package com.yingfei.system.service;

import com.yingfei.entity.domain.EMPL_INF;
import com.yingfei.entity.domain.EMPL_VISIT_INF;
import com.yingfei.entity.domain.MENU_INF;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.MENU_INF_DTO;
import com.yingfei.entity.vo.MENU_INF_VO;
import com.yingfei.entity.vo.RouterVO;
import com.yingfei.entity.vo.TreeSelect;

import java.util.List;
import java.util.Set;

/**
* 
* @description 针对表【MENU_INF(菜单表)】的数据库操作Service
* @createDate 2024-05-11 16:27:09
*/
public interface MENU_INFService extends IService<MENU_INF> {

    Set<String> getMenuPermission(EMPL_INF emplInf);

    List<MENU_INF_DTO> selectMenuList(MENU_INF_VO menu);

    List<TreeSelect> buildMenuTreeSelect(List<MENU_INF_DTO> menus);

    /**
     * 判断权限标识是否重复
     * @param menuInfVo
     */
    void checkMenuPerms(MENU_INF_VO menuInfVo);

    List<MENU_INF_DTO> selectMenuTreeByUserId(EMPL_INF_DTO emplInf);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVO> buildMenus(List<MENU_INF_DTO> menus);

    List<MENU_INF_DTO> searchList(MENU_INF_VO menuInfVo);

    List<EMPL_VISIT_INF> getMenuHistory(Long userId);

    void delete(Long menuId);
}
