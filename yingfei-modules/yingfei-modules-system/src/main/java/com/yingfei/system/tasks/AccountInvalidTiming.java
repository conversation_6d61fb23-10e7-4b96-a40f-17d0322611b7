package com.yingfei.system.tasks;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yingfei.common.redis.service.RedisLock;
import com.yingfei.entity.domain.EMPL_INF;
import com.yingfei.entity.enums.EMPL_INFStatusEnum;
import com.yingfei.system.service.EMPL_INFService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AccountInvalidTiming {
    @Resource
    private RedisLock redisLock;
    @Resource
    private EMPL_INFService emplInfService;


    public static final String lockKey = "invalidaAccountLock";

    /**
     * 每天0点失效未登录账号
     */
    @Transactional(rollbackFor = Exception.class)
    @Scheduled(cron = "0 0 * * * ?")
    public void invalidaAccount() {
        boolean lock = redisLock.getLock(lockKey, null, 5, TimeUnit.SECONDS);
        if (!lock) {
            log.info("账号过期已在执行中!");
            return;
        }
        try {
            LambdaUpdateWrapper<EMPL_INF> emplQueryWrapper = new LambdaUpdateWrapper<>();
            emplQueryWrapper.eq(EMPL_INF::getF_STATUS, EMPL_INFStatusEnum.ACTIVATE.getCode());
            emplQueryWrapper.lt(EMPL_INF::getF_EPTM, new Date());
            emplQueryWrapper.set(EMPL_INF::getF_STATUS, EMPL_INFStatusEnum.INVALID.getCode());
            emplQueryWrapper.set(EMPL_INF::getF_EDTM, new Date());
            emplInfService.getBaseMapper().update(null, emplQueryWrapper);
        } finally {
            redisLock.releaseLock(lockKey);
        }
    }
}
