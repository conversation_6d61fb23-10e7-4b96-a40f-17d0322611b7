package com.yingfei.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yingfei.entity.domain.MENU_INF;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yingfei.entity.dto.MENU_INF_DTO;
import com.yingfei.entity.vo.MENU_INF_VO;

import java.util.List;

/**
* 
* @description 针对表【MENU_INF(菜单表)】的数据库操作Mapper
* @createDate 2024-05-11 16:27:09
* @Entity com.yingfei.entity.domain.MENU_INF
*/
public interface MENU_INFMapper extends MPJBaseMapper<MENU_INF> {

    List<MENU_INF_DTO> selectMenuList(MENU_INF_VO menu);

    List<MENU_INF_DTO> selectMenuListByRole(MENU_INF_VO menu);
}




