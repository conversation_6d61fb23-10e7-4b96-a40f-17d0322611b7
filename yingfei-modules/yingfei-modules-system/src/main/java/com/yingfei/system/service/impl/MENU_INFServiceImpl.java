package com.yingfei.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.constant.UserConstants;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.AuthExceptionEnum;
import com.yingfei.common.core.exception.enums.SystemExceptionEnum;
import com.yingfei.common.core.utils.CollectionToMapUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.ANALYSIS_DASHBOARD_INF_DTO;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.MENU_INF_DTO;
import com.yingfei.entity.vo.MENU_INF_VO;
import com.yingfei.entity.vo.MetaVo;
import com.yingfei.entity.vo.RouterVO;
import com.yingfei.entity.vo.TreeSelect;
import com.yingfei.system.mapper.ANALYSIS_DASHBOARD_INFMapper;
import com.yingfei.system.mapper.EMPL_VISIT_INFMapper;
import com.yingfei.system.mapper.MENU_INFMapper;
import com.yingfei.system.service.MENU_INFService;
import com.yingfei.system.service.ROLE_INFService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 针对表【MENU_INF(菜单表)】的数据库操作Service实现
 * @createDate 2024-05-11 16:27:09
 */
@Service
public class MENU_INFServiceImpl extends ServiceImpl<MENU_INFMapper, MENU_INF>
        implements MENU_INFService {

    @Resource
    private ROLE_INFService roleInfService;
    @Resource
    private EMPL_VISIT_INFMapper emplVisitInfMapper;
    @Resource
    private ANALYSIS_DASHBOARD_INFMapper analysisDashboardInfMapper;

    @Override
    public Set<String> getMenuPermission(EMPL_INF emplInf) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (roleInfService.isAdmin(emplInf.getF_ROLE())) {
            perms.add("*:*:*");
        } else {
            ROLE_INF roleInf = roleInfService.getById(emplInf.getF_ROLE());
            MPJLambdaWrapper<MENU_INF> queryWrapper = new MPJLambdaWrapper<>();
            queryWrapper.select(MENU_INF::getF_PERMS)
                    .leftJoin(PERMISSION_MENU_LINK.class, PERMISSION_MENU_LINK::getF_MENU, MENU_INF::getF_MENU)
                    .eq(PERMISSION_MENU_LINK::getF_PERMISSION, roleInf.getF_PERMISSION());
            List<String> stringList = baseMapper.selectJoinList(String.class, queryWrapper);
            perms.addAll(stringList);
            MPJLambdaWrapper<MENU_INF> wrapper = new MPJLambdaWrapper<>();
            wrapper.select(MENU_INF::getF_PERMS)
                    .leftJoin(ROLE_MENU_LINK.class, ROLE_MENU_LINK::getF_MENU, MENU_INF::getF_MENU)
                    .eq(ROLE_MENU_LINK::getF_ROLE, roleInf.getF_ROLE());
            List<String> list = baseMapper.selectJoinList(String.class, wrapper);
            perms.addAll(list);
        }
        return perms;
    }

    @Override
    public List<MENU_INF_DTO> selectMenuList(MENU_INF_VO menu) {
        if (roleInfService.isAdmin(SecurityUtils.getLoginUser().getSysUser().getF_ROLE())) {
            return baseMapper.selectMenuList(menu);
        }
        menu.setF_ROLE(SecurityUtils.getLoginUser().getSysUser().getF_ROLE());
        return baseMapper.selectMenuListByRole(menu);
    }

    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<MENU_INF_DTO> menus) {
        List<MENU_INF_DTO> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public void checkMenuPerms(MENU_INF_VO menuInfVo) {
        if (Objects.equals(menuInfVo.getF_TYPE(), UserConstants.TYPE_DIR)) {
            menuInfVo.setF_PERMS("");
            return;
        }
        LambdaQueryWrapper<MENU_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MENU_INF::getF_PERMS, menuInfVo.getF_PERMS());
        List<MENU_INF> menuInfs = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(menuInfs)) return;
        if (menuInfVo.getF_MENU() != null) {
            if (menuInfs.size() > 1 || !Objects.equals(menuInfs.get(0).getF_MENU(), menuInfVo.getF_MENU())) {
                throw new BusinessException(AuthExceptionEnum.AUTH_MARK_DUPLICATION_EXCEPTION);
            }
        } else {
            if (!menuInfs.isEmpty()) {
                throw new BusinessException(AuthExceptionEnum.AUTH_MARK_DUPLICATION_EXCEPTION);
            }
        }
    }

    private static final String workdashboard = "workdashboard";

    @Override
    public List<MENU_INF_DTO> selectMenuTreeByUserId(EMPL_INF_DTO emplInf) {
        List<MENU_INF_DTO> menus = selectMenuList(new MENU_INF_VO());

        /*获取菜单对应的分析模板类型*/
        List<Long> list = menus.stream().filter(s -> s.getF_PAGE() != 0).map(MENU_INF_DTO::getF_MENU).collect(Collectors.toList());
        Map<Long, ANALYSIS_DASHBOARD_INF_DTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            MPJLambdaWrapper<ANALYSIS_DASHBOARD_INF> mpjLambdaWrapper = new MPJLambdaWrapper<>();
            mpjLambdaWrapper
                    .leftJoin(ANALYSIS_DASHBOARD_TEMPLATE_INF.class, ANALYSIS_DASHBOARD_TEMPLATE_INF::getF_ADTI, ANALYSIS_DASHBOARD_INF::getF_ADTI)
                    .selectAll(ANALYSIS_DASHBOARD_INF.class)
                    .selectAs(ANALYSIS_DASHBOARD_TEMPLATE_INF::getF_TYPE, ANALYSIS_DASHBOARD_INF_DTO::getTemplateType)
                    .in(ANALYSIS_DASHBOARD_INF::getF_MENU, list);
            List<ANALYSIS_DASHBOARD_INF_DTO> analysisDashboardInfDtos = analysisDashboardInfMapper.selectJoinList(ANALYSIS_DASHBOARD_INF_DTO.class, mpjLambdaWrapper);
            map = CollectionToMapUtils.convertMap(analysisDashboardInfDtos, ANALYSIS_DASHBOARD_INF_DTO::getF_MENU);
        }
        if (emplInf.getF_BOARD() == 1) {
            List<String> collect = menus.stream().map(MENU_INF_DTO::getF_PERMS).collect(Collectors.toList());
            if (!collect.contains(workdashboard)) {
                LambdaQueryWrapper<MENU_INF> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MENU_INF::getF_PERMS, workdashboard);
                MENU_INF menuInf = baseMapper.selectOne(queryWrapper);
                MENU_INF_DTO menuInfDto = new MENU_INF_DTO();
                BeanUtils.copyPropertiesIgnoreNull(menuInf, menuInfDto);
                menus.add(menuInfDto);
            }
        }
        List<MENU_INF_DTO> menuInfDtoList = getChildPerms(menus, 0L);
        if (MapUtils.isNotEmpty(map)) {
            Map<Long, ANALYSIS_DASHBOARD_INF_DTO> finalMap = map;
            menuInfDtoList.forEach(menuInfDto -> {
                ANALYSIS_DASHBOARD_INF_DTO analysisDashboardInfDto = finalMap.get(menuInfDto.getF_MENU());
                if (analysisDashboardInfDto != null) {
                    menuInfDto.setTemplateType(analysisDashboardInfDto.getTemplateType());
                }
            });
        }
        return menuInfDtoList;
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVO> buildMenus(List<MENU_INF_DTO> menus) {
        List<RouterVO> routers = new LinkedList<>();
        for (MENU_INF_DTO menu : menus) {
            RouterVO router = new RouterVO();
            router.setMenuId(menu.getF_MENU());
            router.setPath(menu.getF_PATH());
            router.setMeta(new MetaVo(menu.getF_NAME(), null));
            router.setChildren(new ArrayList<>());
            router.setPageType(menu.getF_PAGE());
            router.setMenuType(menu.getF_TYPE());
            router.setTemplateType(menu.getTemplateType());
            router.setExternalLink(menu.getF_EXTERNAL_LINK());
            List<MENU_INF_DTO> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && menu.getF_TYPE() == 0) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            }
            routers.add(router);
        }
        return routers;
    }

    @Override
    public List<MENU_INF_DTO> searchList(MENU_INF_VO menuInfVo) {
        return baseMapper.selectMenuList(menuInfVo);
    }

    @Override
    public List<EMPL_VISIT_INF> getMenuHistory(Long userId) {
        MPJLambdaWrapper<EMPL_VISIT_INF> mpjLambdaWrapper = new MPJLambdaWrapper<>();
        mpjLambdaWrapper.leftJoin(MENU_INF.class, MENU_INF::getF_MENU, EMPL_VISIT_INF::getF_MENU)
                .selectAll(EMPL_VISIT_INF.class)
                .selectAs(MENU_INF::getF_PAGE, EMPL_VISIT_INF::getPageType)
                .orderByDesc(EMPL_VISIT_INF::getF_CRTM);
        return emplVisitInfMapper.selectJoinList(EMPL_VISIT_INF.class, mpjLambdaWrapper);
    }

    @Override
    public void delete(Long menuId) {
        /*删除菜单时删除对应的分析页面*/
        LambdaQueryWrapper<ANALYSIS_DASHBOARD_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ANALYSIS_DASHBOARD_INF::getF_MENU,menuId);
        List<ANALYSIS_DASHBOARD_INF> analysisDashboardInfs = analysisDashboardInfMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(analysisDashboardInfs)) {
            throw new BusinessException(SystemExceptionEnum.MENU_ANALYSIS_PAGE_EXCEPTION);
        }

        baseMapper.deleteById(menuId);
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<MENU_INF_DTO> getChildPerms(List<MENU_INF_DTO> list, Long parentId) {
        List<MENU_INF_DTO> returnList = new ArrayList<>();
        for (Iterator<MENU_INF_DTO> iterator = list.iterator(); iterator.hasNext(); ) {
            MENU_INF_DTO t = (MENU_INF_DTO) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (Objects.equals(t.getF_PARENT(), parentId)) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<MENU_INF_DTO> buildMenuTree(List<MENU_INF_DTO> menus) {
        List<MENU_INF_DTO> returnList = new ArrayList<>();
        List<Long> tempList = menus.stream().map(MENU_INF_DTO::getF_MENU).collect(Collectors.toList());
        for (Iterator<MENU_INF_DTO> iterator = menus.iterator(); iterator.hasNext(); ) {
            MENU_INF_DTO menu = (MENU_INF_DTO) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getF_PARENT())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<MENU_INF_DTO> list, MENU_INF_DTO t) {
        // 得到子节点列表
        List<MENU_INF_DTO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (MENU_INF_DTO tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<MENU_INF_DTO> getChildList(List<MENU_INF_DTO> list, MENU_INF_DTO t) {
        List<MENU_INF_DTO> tlist = new ArrayList<>();
        Iterator<MENU_INF_DTO> it = list.iterator();
        while (it.hasNext()) {
            MENU_INF_DTO n = (MENU_INF_DTO) it.next();
            if (Objects.equals(n.getF_PARENT(), t.getF_MENU())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<MENU_INF_DTO> list, MENU_INF_DTO t) {
        return getChildList(list, t).size() > 0;
    }
}




