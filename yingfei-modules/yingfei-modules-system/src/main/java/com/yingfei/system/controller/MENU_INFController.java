package com.yingfei.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.domain.EMPL_VISIT_INF;
import com.yingfei.entity.domain.MENU_INF;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.MENU_INF_DTO;
import com.yingfei.entity.vo.MENU_INF_VO;
import com.yingfei.system.mapper.EMPL_VISIT_INFMapper;
import com.yingfei.system.service.MENU_INFService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "菜单信息API")
@RestController
@RequestMapping("/menu_inf")
public class MENU_INFController {

    @Resource
    private MENU_INFService menuInfService;
    @Resource
    private EMPL_VISIT_INFMapper emplVisitInfMapper;

    /**
     * 获取菜单列表
     */
    @ApiOperation("获取菜单列表")
    @RequiresPermissions("system:menu:list")
    @PostMapping("/list")
    public R<?> list(@RequestBody MENU_INF_VO menuInfVo) {
        List<MENU_INF_DTO> menus = menuInfService.selectMenuList(menuInfVo);
        return R.ok(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @ApiOperation("根据菜单编号获取详细信息")
    @GetMapping(value = "/{menuId}")
    public R<?> getInfo(@PathVariable String menuId) {
        return R.ok(menuInfService.getById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @ApiOperation("获取菜单下拉树列表")
    @PostMapping("/treeselect")
    public R<?> treeselect(@RequestBody MENU_INF_VO menuInfVo) {
        List<MENU_INF_DTO> menus = menuInfService.selectMenuList(menuInfVo);
        return R.ok(menuInfService.buildMenuTreeSelect(menus));
    }

//    /**
//     * 加载对应角色菜单列表树
//     */
//    @ApiOperation("加载对应角色菜单列表树")
//    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
//    public R<?> roleMenuTreeselect(@PathVariable("roleId") String roleId) {
//        List<MENU_INF_DTO> menus = menuInfService.selectMenuList(SecurityUtils.getUserId());
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("checkedKeys", menuInfService.selectMenuListByRoleId(roleId));
//        map.put("menus", menuInfService.buildMenuTreeSelect(menus));
//        return R.ok(map);
//    }

    /**
     * 新增菜单
     */
    @ApiOperation("新增菜单")
    @CreateUpdateBy
    @RequiresPermissions("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<?> add(@Validated @RequestBody MENU_INF_VO menuInfVo) {
        menuInfService.checkMenuPerms(menuInfVo);
        MENU_INF menuInf = new MENU_INF();
        BeanUtils.copyPropertiesIgnoreNull(menuInfVo, menuInf);
        menuInfService.save(menuInf);
        return R.ok();
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @RequiresPermissions("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public R<?> edit(@Validated @RequestBody MENU_INF_VO menuInfVo) {
        menuInfService.checkMenuPerms(menuInfVo);
        MENU_INF menuInf = new MENU_INF();
        BeanUtils.copyPropertiesIgnoreNull(menuInfVo, menuInf);
        menuInf.setF_EDUE(SecurityUtils.getUserId());
        menuInfService.updateById(menuInf);
        return R.ok();
    }

    /**
     * 删除菜单
     */
    @ApiOperation("删除菜单")
    @RequiresPermissions("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/del/{menuId}")
    public R<?> remove(@PathVariable("menuId") Long menuId) {
        menuInfService.delete(menuId);
        return R.ok();
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public R<?> getRouters() {
        EMPL_INF_DTO emplInf = SecurityUtils.getLoginUser().getSysUser();
        List<MENU_INF_DTO> menus = menuInfService.selectMenuTreeByUserId(emplInf);
        return R.ok(menuInfService.buildMenus(menus));
    }


    @PostMapping("/searchList")
    public R<?> searchList(@RequestBody MENU_INF_VO menuInfVo) {
        List<MENU_INF_DTO> menus = menuInfService.searchList(menuInfVo);
        return R.ok(menus);
    }

    /**
     * 点击页面记录点击历史
     */
    @CreateUpdateBy
    @ApiOperation("点击页面记录点击历史")
    @PostMapping("/menuClick")
    public R<?> menuClick(@RequestBody EMPL_VISIT_INF emplVisitInf) {
        Long userId = SecurityUtils.getUserId();
        emplVisitInf.setF_EMPL(userId);
        if (emplVisitInf.getF_MENU() != null) {
            LambdaQueryWrapper<EMPL_VISIT_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EMPL_VISIT_INF::getF_MENU, emplVisitInf.getF_MENU());
            emplVisitInfMapper.delete(queryWrapper);
        } else if (StringUtils.isNotEmpty(emplVisitInf.getF_NAME())) {
            LambdaQueryWrapper<EMPL_VISIT_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EMPL_VISIT_INF::getF_NAME, emplVisitInf.getF_NAME());
            emplVisitInfMapper.delete(queryWrapper);
        }
        emplVisitInfMapper.insert(emplVisitInf);
        return R.ok();
    }

    /**
     * 获取点击菜单历史记录
     */
    @ApiOperation("获取点击菜单历史记录")
    @GetMapping("/getMenuHistory")
    public R<?> getMenuHistory() {
        Long userId = SecurityUtils.getUserId();
        List<EMPL_VISIT_INF> visits = menuInfService.getMenuHistory(userId);
        return R.ok(visits);
    }

    /**
     * 分析页面绑定菜单页面类型修改
     */
    @ApiOperation("分析页面绑定菜单页面类型修改(内部调用)")
    @GetMapping("/bindAnalysisTemplate")
    public R<?> bindAnalysisTemplate(Long menuId, Integer pageType) {
        MENU_INF menuInf = menuInfService.getById(menuId);
        menuInf.setF_PAGE(pageType);
        menuInf.setF_EDTM(DateUtils.getNowDate());
        menuInfService.updateById(menuInf);
        return R.ok();
    }
}
