<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.system.mapper.MENU_INFMapper">

    <resultMap id="BaseResultMap" type="com.yingfei.entity.domain.MENU_INF">
            <id property="f_MENU" column="F_MENU" jdbcType="BIGINT"/>
            <result property="f_NAME" column="F_NAME" jdbcType="VARCHAR"/>
            <result property="f_ORDER" column="F_ORDER" jdbcType="INTEGER"/>
            <result property="f_PATH" column="F_PATH" jdbcType="VARCHAR"/>
            <result property="f_TYPE" column="F_TYPE" jdbcType="SMALLINT"/>
            <result property="f_DES" column="F_DES" jdbcType="VARCHAR"/>
            <result property="f_PARENT" column="F_PARENT" jdbcType="BIGINT"/>
            <result property="f_CODE" column="F_CODE" jdbcType="VARCHAR"/>
            <result property="f_PERMS" column="F_PERMS" jdbcType="VARCHAR"/>
            <result property="f_CRUE" column="F_CRUE" jdbcType="BIGINT"/>
            <result property="f_EDUE" column="F_EDUE" jdbcType="BIGINT"/>
            <result property="f_CRTM" column="F_CRTM" jdbcType="TIMESTAMP"/>
            <result property="f_EDTM" column="F_EDTM" jdbcType="TIMESTAMP"/>
            <result property="f_PAGE" column="F_PAGE" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        F_MENU,F_NAME,F_ORDER,
        F_PATH,F_TYPE,F_DES,
        F_PARENT,F_CODE,F_PERMS,
        F_CRUE,F_EDUE,F_CRTM,
        F_EDTM,F_PAGE,F_EXTERNAL_LINK
    </sql>

    <sql id="Base_Column_List_Join">
        menuinf.F_MENU,menuinf.F_NAME,menuinf.F_ORDER,
        menuinf.F_PATH,menuinf.F_TYPE,menuinf.F_DES,
        menuinf.F_PARENT,menuinf.F_CODE,menuinf.F_PERMS,
        menuinf.F_CRUE,menuinf.F_EDUE,menuinf.F_CRTM,
        menuinf.F_EDTM,menuinf.F_PAGE
        ,menuinf.F_EXTERNAL_LINK
    </sql>

    <select id="selectMenuList" resultType="com.yingfei.entity.dto.MENU_INF_DTO">
        select
        <include refid="Base_Column_List"/>
        from MENU_INF
        <where>
            1=1
            <if test="F_NAME != null and F_NAME != ''">
                AND F_NAME like concat('%', #{F_NAME}, '%')
            </if>
            <if test="menuIds != null and menuIds.size() != 0">
                and F_MENU in
                <foreach collection="menuIds" item="menuId" open="(" close=")" separator=",">
                    #{menuId}
                </foreach>
            </if>
        </where>
        order by F_PARENT, F_ORDER
    </select>

    <select id="selectMenuListByRole" resultType="com.yingfei.entity.dto.MENU_INF_DTO">
        select
        <include refid="Base_Column_List_Join"/>
        from MENU_INF menuinf
        inner join ROLE_MENU_LINK rolelink on menuinf.F_MENU = rolelink.F_MENU
        where rolelink.F_ROLE = #{F_ROLE}
        order by menuinf.F_PARENT, menuinf.F_ORDER
    </select>
</mapper>
