package com.yingfei.mq.controller;

import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.producer.InspectionDataCollectProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/inspectionDataCollect")
public class InspectionDataController {

    @Resource
    private InspectionDataCollectProducer inspectionDataCollectProducer;

    @PostMapping("/send")
    public R<?> send(@RequestBody InspectionDataDTO inspectionDataDTO) {
        try {

            inspectionDataCollectProducer.send(inspectionDataDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("第三方推送检验数据发送失败");
            return R.fail();
        }
        return R.ok();
    }
}
