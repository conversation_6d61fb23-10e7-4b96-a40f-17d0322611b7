package com.yingfei.mq.controller;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.domain.MESSAGE_LOG_INF;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.mapper.MESSAGE_LOG_INFMapper;
import com.yingfei.mq.producer.InspectionDataCollectProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/inspectionDataCollect")
public class InspectionDataController {

    @Resource
    private InspectionDataCollectProducer inspectionDataCollectProducer;
    @Resource
    private RedisService redisService;
    @Resource
    private MESSAGE_LOG_INFMapper messageLogInfMapper;
    @PostMapping("/send")
    public R<?> send(@RequestBody InspectionDataDTO inspectionDataDTO) {
        try {
            /*保存消息日志*/
            Long aLong = JudgeUtils.defaultIdentifierGenerator.nextId(null);
            CorrelationData correlationData = new CorrelationData(String.valueOf(aLong));
            EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);
            String jsonString = JSONObject.toJSONString(inspectionDataDTO);
            MESSAGE_LOG_INF messageLogInf = new MESSAGE_LOG_INF();
            messageLogInf.setF_NEWS(Long.valueOf(correlationData.getId()));
            messageLogInf.setF_DATA(jsonString);
            messageLogInf.setF_CRUE(emplInfDto.getF_EMPL());
            messageLogInf.setF_TYPE(1);
            messageLogInfMapper.insert(messageLogInf);
            inspectionDataCollectProducer.send(inspectionDataDTO,correlationData);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("第三份推送检验数据发送失败");
            return R.fail();
        }
        return R.ok();
    }
}
