//package com.yingfei.mq.consumer;
//
//import com.yingfei.dataCollection.service.SamplingService;
//import com.yingfei.mq.config.InspectionMqConfig;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 抽样触发消息消费者
// * 处理抽样触发消息，执行抽样任务
// */
//@Component
//@Slf4j
//public class SamplingTriggerConsumer {
//
//    @Resource
//    private SamplingService samplingService;
//
//    /**
//     * 处理抽样触发消息
//     * @param configId 抽样配置ID
//     */
//    @RabbitListener(queues = InspectionMqConfig.QUEUE_INSPECTION_SAMPLING_TRIGGER)
//    public void onSamplingTrigger(Long configId) {
//        log.info("接收到抽样触发消息，配置ID: {}", configId);
//
//        try {
//            // 执行抽样任务
//            SamplingService.SamplingResult result = samplingService.performSampling(configId);
//
//            if (result.isSuccess()) {
//                log.info("抽样任务执行成功，配置ID: {}, 抽样数据量: {}, 结果键: {}",
//                        configId, result.getData().size(), result.getResultKey());
//
//                // 这里可以添加后续处理逻辑，比如：
//                // 1. 发送抽样结果到其他系统
//                // 2. 触发SPC分析
//                // 3. 生成报告等
//
//            } else {
//                log.error("抽样任务执行失败，配置ID: {}, 错误信息: {}", configId, result.getMessage());
//            }
//
//        } catch (Exception e) {
//            log.error("处理抽样触发消息时发生异常，配置ID: {}", configId, e);
//        }
//    }
//}
