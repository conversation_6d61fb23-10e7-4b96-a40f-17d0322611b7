//package com.yingfei.mq.config;
//
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.DirectExchange;
//import org.springframework.amqp.core.Queue;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class InspectionMqConfig {
//
//    public static final String EXCHANGE_INSPECTION = "exchange.inspection";
//    public static final String QUEUE_INSPECTION_DATA_RECEIVE = "inspection.data.receive";
//    public static final String ROUTING_KEY_INSPECTION_DATA_RECEIVE = "routing.inspection.data.receive";
//
//
//    @Bean
//    public DirectExchange inspectionExchange() {
//        return new DirectExchange(EXCHANGE_INSPECTION);
//    }
//
//    @Bean
//    public Queue inspectionDataReceiveQueue() {
//        return new Queue(QUEUE_INSPECTION_DATA_RECEIVE, true);
//    }
//
//    @Bean
//    public Binding bindingDataReceive(DirectExchange inspectionExchange, Queue inspectionDataReceiveQueue) {
//        return BindingBuilder.bind(inspectionDataReceiveQueue).to(inspectionExchange).with(ROUTING_KEY_INSPECTION_DATA_RECEIVE);
//    }
//
//}
//
