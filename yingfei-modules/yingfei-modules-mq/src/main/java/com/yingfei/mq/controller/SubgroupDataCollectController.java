package com.yingfei.mq.controller;

import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.producer.SubgroupDataCollectProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/subgroupDataCollect")
public class SubgroupDataCollectController {

    @Resource
    private SubgroupDataCollectProducer subgroupDataCollectProducer;

    @PostMapping("/send")
    public R<?> send(@RequestBody List<SubgroupDataVO> subgroupDataVOList) {
        try {
            subgroupDataCollectProducer.send(subgroupDataVOList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("子组采集消息发送失败");
            return R.fail();
        }
        return R.ok();
    }

    @PostMapping("/save/{msgId}")
    public R<?> save(@RequestBody List<SubgroupDataVO> subgroupDataVOList, @PathVariable("msgId") String msgId) {
        try {
            subgroupDataCollectProducer.send(subgroupDataVOList,msgId);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("子组采集消息发送失败");
            return R.fail();
        }
        return R.ok();
    }


    @PostMapping("/batchSend")
    public R<?> batchSend(@RequestBody FileNameDataDTO fileNameDataDTO) {
        try {
            subgroupDataCollectProducer.batchSend(fileNameDataDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("子组采集消息发送失败");
            return R.fail();
        }
        return R.ok();
    }

//    @PostMapping("/save")
//    public R<?> save(@RequestBody SubgroupDataBatchAddVO subgroupDataBatchAddVO) {
//        try {
//            subgroupDataCollectProducer.save(subgroupDataBatchAddVO);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("子组采集消息发送失败");
//            return R.fail();
//        }
//        return R.ok();
//    }
}
