package com.yingfei.mq.consumer.monitor;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.Channel;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.WorkFlowConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.api.*;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.dingDing.DingDingSendMessageDTO;
import com.yingfei.entity.dto.email.EmailSendMessageDTO;
import com.yingfei.entity.dto.globalConfig.SysyemGlobalConfig;
import com.yingfei.entity.dto.qyWeChat.QyWeChatSendMessageDTO;
import com.yingfei.entity.enums.*;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.util.EmailAlarmUtil;
import com.yingfei.entity.util.TextAlarmMsgUtil;
import com.yingfei.entity.util.WarningRuleService;
import com.yingfei.entity.vo.BPM_PROCESS_INSTANCE_VO;
import com.yingfei.entity.vo.CTRL_INF_VO;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.config.InitConfig;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.mapper.*;
import com.yingfei.mq.mapper.convert.SubgroupDataConvert;
import com.yingfei.mq.producer.SubgroupBpmProducer;
import com.yingfei.mq.service.EVNT_INFService;
import com.yingfei.mq.service.SGRP_INF_AService;
import com.yingfei.mq.service.SYSTEM_NOTIFICATION_INFService;
import com.yingfei.system.api.RemoteGlobalConfigInfService;
import com.yingfei.system.api.RemoteSendService;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.w3c.dom.Text;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component
@RabbitListener(queues = {RabbitConfig.MONITOR_QUEUE_NAME})
@Slf4j
public class SubgroupDataMonitorConsumer {

    @Resource
    private PROCESSING_TEMPLATE_INFMapper processingTemplateInfMapper;
    @Resource
    private ACTIVED_RULE_TEMPLATE_INFMapper activedRuleTemplateInfMapper;
    @Resource
    private RULE_INFMapper ruleInfMapper;
    @Resource
    private WarningRuleService warningRuleService;
    @Resource
    private EVNT_INFService evntInfService;
    @Resource
    private RedisService redisService;
    @Resource
    private SubgroupMapper subgroupMapper;
    @Resource
    private SGRP_INF_AService sgrpInfAService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private NOTIFICATION_RULEMapper notificationRuleMapper;
    @Resource
    private RemoteNotificationService remoteNotificationService;
    @Resource
    private TEST_INFMapper testInfMapper;
    @Resource
    private RemoteSendService remoteSendService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private RemoteEvntService remoteEvntService;
    @Resource
    private SYSTEM_NOTIFICATION_INFService systemNotificationInfService;
    @Resource
    private RemoteSgrpInfService remoteSgrpInfService;
    @Resource
    private SubgroupBpmProducer subgroupBpmProducer;
    @Resource
    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;
    @Resource
    private RemoteCtrlService ctrlInfService;
    @Resource
    private RemoteSpecService specInfService;

    private static final String subgroupMonitor = "SubgroupMonitor:";
    private static final ExecutorService cachedThreadPool = Executors.newCachedThreadPool();

    @RabbitHandler
//    @Transactional(rollbackFor = Exception.class)
    @Async
    public void consumeMsg(String queueMessage, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                           @Header("spring_returned_message_correlation") CorrelationData correlationData,
                           Channel channel) throws IOException {
        long startTime = System.currentTimeMillis();
        try {
            log.info("收到监控消息---->");

            List<SubgroupDataVO> subgroupDataVOList = JSONArray.parseArray(queueMessage, SubgroupDataVO.class);
            /*把历史导入数据排除*/
            subgroupDataVOList = subgroupDataVOList.stream().filter(s -> s.getHistoricalData() == 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subgroupDataVOList)) {
                log.info("子组采集消息为空");
                channel.basicAck(deliveryTag, false);
                return;
            }
            //todo day的作用是判断最开始的子组时间到当前时间所差的天数 如果满足则移除，否则保留
//            Integer day = (Integer) redisService.get(RedisConstant.MONITOR_SAVE_DAY);

            SysyemGlobalConfig systemConfig = null;
            try {
                systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
            } catch (Exception e) {
                log.error("获取系统配置信息失败 ex:{}",e.getMessage(), e);
                throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
            }

            final Integer monitorSaveDay = systemConfig.getDataCacheConfig().getMonitorSaveDay();
            final Integer monitorSaveCount = systemConfig.getDataCacheConfig().getMonitorSaveCount();
//            Integer num = (Integer) redisService.get(RedisConstant.MONITOR_SAVE_NUM);
            /*获取报警规则*/
            List<RULE_INF> ruleInfs = ruleInfMapper.selectList(new LambdaQueryWrapper<RULE_INF>().eq(RULE_INF::getF_DEL, DelFlagEnum.USE.getType()));
            /*将不同测试重新组装*/
            List<SubgroupDataVO> list = new ArrayList<>();
            subgroupDataVOList.forEach(subgroupDataVO -> {
                subgroupDataVO.getSgrpValChildDtoList().forEach(sgrpValChildDto -> {
                    SubgroupDataVO groupDataVO = new SubgroupDataVO();
                    BeanUtils.copyPropertiesIgnoreNull(subgroupDataVO, groupDataVO);
                    SGRP_VAL_CHILD_DTO convert = SubgroupDataConvert.INSTANCE.convert(sgrpValChildDto);
                    convert.setTestType(sgrpValChildDto.getTestType());
                    List<SGRP_VAL_CHILD_DTO.Test> newTestList = new ArrayList<>();
                    List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValChildDto.getTestList();
                    testList.forEach(testDto -> {
                        SGRP_VAL_CHILD_DTO.Test test = SubgroupDataConvert.INSTANCE.convert(testDto);
                        newTestList.add(test);
                    });
                    convert.setTestList(newTestList);
                    groupDataVO.setSgrpValChildDto(convert);
                    groupDataVO.setCalculatedControlLimits(new ArrayList<>());
                    list.add(groupDataVO);
                });
            });

            /*按子组时间正序排列*/
            List<SubgroupDataVO> groupDataVOList =
                    list.stream().sorted(Comparator.comparing(SubgroupDataVO::getF_SGTM)).collect(Collectors.toList());
            LinkedHashMap<String, List<SubgroupDataVO>> collect = groupDataVOList.stream()
                    .collect(Collectors.groupingBy(s ->
                                    s.getF_PART() + Constants.COMMA +
                                            s.getF_REV() + Constants.COMMA
                                            + s.getF_PRCS() + Constants.COMMA
                                            + s.getSgrpValChildDto().getTestId(),
                            LinkedHashMap::new, Collectors.toList()));
            List<EVNT_INF> evntInList = new ArrayList<>();
            collect.forEach((k, v) -> {
                try {
                    long millis = System.currentTimeMillis();
                    String key = subgroupMonitor + k;
                    int l = Integer.parseInt(String.valueOf(redisService.lGetListSize(key)));
                    List<SubgroupDataVO> cacheList = redisService.getCacheList(key);
                    List<Double> MR = null;
//                    List<CTRL_INF_DTO> ctrlInfDtoList = redisService.getCacheList(RedisConstant.CTRL_KEY + k);
                    final String[] split = k.split(Constants.COMMA);
                    CTRL_INF_VO ctrlInfVo = new CTRL_INF_VO(Long.valueOf(split[0]), Long.valueOf(split[1]), Long.valueOf(split[2]), Long.valueOf(split[3]));
                    List<CTRL_INF_DTO> ctrlInfDtoList = ctrlInfService.getCtrlInfo(ctrlInfVo).getData();
                    if (CollectionUtils.isEmpty(ctrlInfDtoList)) {
                        log.error("key----->{},不存在控制限", k);
                    }

                    /*是否失效子组*/
                    boolean flag = false;
                    for (SubgroupDataVO subgroupDataVO : v) {

                        SGRP_VAL_CHILD_DTO sgrpValChildDto = subgroupDataVO.getSgrpValChildDto();
                        /*报警信息json*/
                        List<AlarmMessageDTO> alarmMessageDTOList = new ArrayList<>();

                        TEST_INF testInf = testInfMapper.selectById(sgrpValChildDto.getTestId());

                        /*获取公差限*/
                        SPEC_INF_DTO specInfDto = getSpecInfDto(subgroupDataVO);
                        /*获取控制限*/
                        CTRL_INF_DTO ctrlInfDto = getCtrlInfDto(ctrlInfDtoList, subgroupDataVO);
                        if (specInfDto == null && ctrlInfDto == null) break;

                        if (specInfDto == null) {
                            log.info("key----->{},不存在公差限", key);
                        } else {
                            /*判断公差限是否触发报警*/
                            alarmMessageDTOList.addAll(SPEC_INF_DTO.triggerAlarm(specInfDto, subgroupDataVO, testInf));
                        }

                        /*如果ctrlInfDto.getF_PSTP()为空说明有控制限没在生效日期内*/
                        if (ctrlInfDto != null && StringUtils.isNotEmpty(ctrlInfDto.getF_PSTP())) {

                            /*获取图表类型*/
                            ControlChartTypeEnum controlChartTypeEnum = ControlChartTypeEnum.getEnumByType(ctrlInfDto.getF_CHART_TYPE());
                            if (controlChartTypeEnum == null) {
                                log.info("key----->{},图表类型不匹配", k);
                                continue;
                            }
                            /*获取数据处理方式*/
                            PROCESSING_TEMPLATE_INF processingTemplateInf = processingTemplateInfMapper.selectById(ctrlInfDto.getF_PSTP());
                            /*获取报警方式模板*/
                            ACTIVED_RULE_TEMPLATE_INF activedRuleTemplateInf = activedRuleTemplateInfMapper.selectById(ctrlInfDto.getF_ARTP());


                            ControlChartSingleEnum[] groupType = controlChartTypeEnum.getGroupType();
                            /*获取缓存数据点*/
                            cacheList.add(subgroupDataVO);
                            /*第几张图表*/
                            int i = 1;
                            for (ControlChartSingleEnum chartSingleEnum : groupType) {
                                /*组装控制限计算数据*/
                                ControlLimitDTO controlLimitDto = new ControlLimitDTO();
                                controlLimitDto.setF_MEAN(ctrlInfDto.getF_MEAN());
                                controlLimitDto.setF_SP(ctrlInfDto.getF_SP());
                                controlLimitDto.setF_SW(ctrlInfDto.getF_SW());
                                controlLimitDto.setF_SPL(ctrlInfDto.getF_SPL());
                                controlLimitDto.setF_SIGMA_COUNT(processingTemplateInf.getF_SIGMA_COUNT());
                                /*获取子组均值*/
                                controlLimitDto.setNowMean(sgrpValChildDto.getAverage());
                                /*获取子组极差*/
                                controlLimitDto.setNowR(sgrpValChildDto.getRange());
                                controlLimitDto.setNowSD(sgrpValChildDto.getSd());
                                controlLimitDto.setSpecInfDto(specInfDto);
                                /*极差件内图和标准差件内图是要用子测试数量来作为样本量*/
                                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_SDW ||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_RW) {
                                    controlLimitDto.setN((double) sgrpValChildDto.getTestList().get(0).getSubTestList().size());
                                } else if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_P ||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_U||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_NP ||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_C ||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_LaneyP ||
                                        chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_LaneyU
                                ) {
                                    controlLimitDto.setN(Double.valueOf(sgrpValChildDto.getNum()));

                                } else {
                                    controlLimitDto.setN(Double.valueOf(sgrpValChildDto.getNum()));
                                }

                                /*判断是否是移动极差*/
                                if (chartSingleEnum == ControlChartSingleEnum.CHART_GROUP_TYPE_MR) {
                                    if (MR == null) {
                                        MR = new ArrayList<>();
                                        controlLimitDto.setNowMR(Double.NaN);
                                        MR.add(sgrpValChildDto.getAverage());
                                    } else {
                                        double abs = Math.abs(sgrpValChildDto.getAverage() - MR.get(MR.size() - 1));
                                        controlLimitDto.setNowMR(abs);
                                        MR.add(sgrpValChildDto.getAverage());
                                    }
                                }

                                ControlChartCalculateService controlChartCalculateService = null;
                                try {
                                    Class<?> aClass = Class.forName(chartSingleEnum.getClassPath());
                                    controlChartCalculateService = (ControlChartCalculateService) aClass.getDeclaredConstructor().newInstance();
                                    CalculatedControlLimit calculatedControlLimit = new CalculatedControlLimit();
                                    /*获取控制限CL*/
                                    calculatedControlLimit.setCL(controlChartCalculateService.controlLimitCL(controlLimitDto));
                                    controlLimitDto.setCL(calculatedControlLimit.getCL());
                                    /*获取控制限UCL*/
                                    calculatedControlLimit.setUCL(controlChartCalculateService.controlLimitUCL(controlLimitDto));
                                    /*获取控制限LCL*/
                                    calculatedControlLimit.setLCL(controlChartCalculateService.controlLimitLCL(controlLimitDto));
                                    /*标准化处理*/
                                    STANDARDIZE_TYPEEnum.dispose(controlLimitDto, calculatedControlLimit, STANDARDIZE_TYPEEnum.getType(processingTemplateInf.getF_STANDARDIZE_TYPE()), chartSingleEnum);
                                    /*计算A区,B区*/
                                    CalculatedControlLimit.calculateSection(calculatedControlLimit);
                                    Double dataPointVal = ControlChartSingleEnum.getDataPointVal(chartSingleEnum, controlLimitDto, subgroupDataVO.getSgrpValChildDto());
                                    calculatedControlLimit.setChartSingleEnum(chartSingleEnum);
                                    calculatedControlLimit.setDataPoint(dataPointVal);
                                    cacheList.get(cacheList.size() - 1).getCalculatedControlLimits().add(calculatedControlLimit);
//                                cacheList.get(cacheList.size() - 1).getControlLimitDTOList().add(controlLimitDto);

                                    /*控制限报警处理*/
                                    List<String> idList = new ArrayList<>();
                                    if (i == 1) {
                                        if (StringUtils.isEmpty(activedRuleTemplateInf.getF_CHART_ONE())) break;
                                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_ONE(), String.class);
                                    } else if (i == 2) {
                                        if (StringUtils.isEmpty(activedRuleTemplateInf.getF_CHART_TWO())) break;
                                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_TWO(), String.class);
                                    } else {
                                        if (StringUtils.isEmpty(activedRuleTemplateInf.getF_CHART_THREE()))
                                            break;
                                        idList = JSONArray.parseArray(activedRuleTemplateInf.getF_CHART_THREE(), String.class);
                                    }
                                    if (CollectionUtils.isEmpty(idList)) break;
                                    List<String> finalIdList = idList;
                                    List<RULE_INF> ruleInfList = ruleInfs.stream().filter(s -> finalIdList.contains(s.getF_ALR()+"")).collect(Collectors.toList());

                                    i++;
                                    //todo 先用之前逻辑判断报警  后面优化
                                    List<AlarmMessageDTO> warningDetails = warningRuleService.monitorJudgeWarningRule(ruleInfList, cacheList, chartSingleEnum, specInfDto, monitorSaveCount);
                                    alarmMessageDTOList.addAll(warningDetails);

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }

                        /*触发报警规则后*/
                        if (CollectionUtils.isNotEmpty(alarmMessageDTOList)) {
                            /*evnt_inf表创建一条记录*/
                            EVNT_INF evntInf = EVNT_INF_DTO.init(subgroupDataVO, JSONArray.toJSONString(alarmMessageDTOList));
                            evntInf.setF_EVNT(JudgeUtils.defaultIdentifierGenerator.nextId(null));
                            evntInList.add(evntInf);

                            /*判断报警是否有失效子组*/
                            List<AlarmMessageDTO> dtos = alarmMessageDTOList.stream().filter(s -> s.getFlag() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtos)) {
                                /*将子组失效*/
                                flag = true;
                                LambdaUpdateWrapper<SGRP_INF_A> updateWrapperA = new LambdaUpdateWrapper<>();
                                updateWrapperA.eq(SGRP_INF_A::getF_SGRP, subgroupDataVO.getF_SGRP())
                                        .set(SGRP_INF_A::getF_FLAG, YesOrNoEnum.YES.getType())
                                        .set(SGRP_INF_A::getF_EDTM, DateUtils.getNowDate());
                                sgrpInfAService.update(null, updateWrapperA);

                                LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.eq(SGRP_INF::getF_SGRP, subgroupDataVO.getF_SGRP())
                                        .set(SGRP_INF::getF_FLAG, YesOrNoEnum.YES.getType())
                                        .set(SGRP_INF::getF_EDTM, DateUtils.getNowDate());
                                subgroupMapper.update(null, updateWrapper);
                                break;
                            }
                        }
                    }

                    if (!flag) {
                        redisService.setCacheList(key, v, monitorSaveDay * 24 * 60 * 60L);
                    }
                    if ((l + v.size()) > monitorSaveCount) {
                        int i = (l + v.size()) - monitorSaveCount;
                        /*弹出对应的条数*/
                        redisService.multiRPopPipeline(key, i);
                    }
                    long millis2 = System.currentTimeMillis();
                    log.info("子组------->{},数据监控逻辑耗时---------->{}", k, millis2 - millis);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            evntInfService.saveBatch(evntInList);
            updateSgrp(subgroupDataVOList);
            if (CollectionUtils.isNotEmpty(evntInList))
                sendNotification(evntInList, collect);

            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("子组采集消息处理失败,错误信息---------->", e);
            channel.basicNack(deliveryTag, false, false);
        }
        long endTime = System.currentTimeMillis();
        log.info("数据监控逻辑耗时---------->{}", endTime - startTime);
    }

    /**
     * 修改子组报警监控状态
     *
     * @param subgroupDataVOList
     */
    private void updateSgrp(List<SubgroupDataVO> subgroupDataVOList) {
        /*修改子组报警监控状态(0:未完成 1:已完成)*/
        List<Long> sgrpList = subgroupDataVOList.stream().map(SubgroupDataVO::getF_SGRP).collect(Collectors.toList());
        if (sgrpList.size() > 1000) {
            List<List<Long>> collect = new ArrayList<>(sgrpList.stream().collect(Collectors.groupingBy(s ->
                    sgrpList.indexOf(s) / 1000)).values());
            collect.forEach(ids -> {
                LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(SGRP_INF::getF_SGRP, ids)
                        .set(SGRP_INF::getF_STATUS, YesOrNoEnum.YES.getType())
                        .set(SGRP_INF::getF_EDTM, DateUtils.getNowDate());
                subgroupMapper.update(null, updateWrapper);

                /*修改子组缓存表报警监控状态(0:未完成 1:已完成)*/
                LambdaUpdateWrapper<SGRP_INF_A> updateWrapperA = new LambdaUpdateWrapper<>();
                updateWrapperA.in(SGRP_INF_A::getF_SGRP, ids)
                        .set(SGRP_INF_A::getF_STATUS, YesOrNoEnum.YES.getType())
                        .set(SGRP_INF_A::getF_EDTM, DateUtils.getNowDate());
                sgrpInfAService.update(null, updateWrapperA);
            });
        } else {
            LambdaUpdateWrapper<SGRP_INF> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(SGRP_INF::getF_SGRP, sgrpList)
                    .set(SGRP_INF::getF_STATUS, YesOrNoEnum.YES.getType());
            subgroupMapper.update(null, updateWrapper);

            /*修改子组缓存表报警监控状态(0:未完成 1:已完成)*/
            LambdaUpdateWrapper<SGRP_INF_A> updateWrapperA = new LambdaUpdateWrapper<>();
            updateWrapperA.in(SGRP_INF_A::getF_SGRP, sgrpList)
                    .set(SGRP_INF_A::getF_STATUS, YesOrNoEnum.YES.getType())
                    .set(SGRP_INF_A::getF_EDTM, DateUtils.getNowDate());
            sgrpInfAService.update(null, updateWrapperA);
        }

    }

    /**
     * 获取对应的控制限
     *
     * @param ctrlInfDtoList
     * @param subgroupDataVO
     * @return
     */

    private CTRL_INF_DTO getCtrlInfDto(List<CTRL_INF_DTO> ctrlInfDtoList, SubgroupDataVO subgroupDataVO) {
        if (CollectionUtils.isEmpty(ctrlInfDtoList)) {
            return null;
        }
        /*根据生效时间降序*/
        ctrlInfDtoList = ctrlInfDtoList.stream().sorted(Comparator.comparing(CTRL_INF_DTO::getF_EFTM).reversed()).collect(Collectors.toList());
        CTRL_INF_DTO infDto = null;
        /*判断子组时间是否在生效时间内*/
        for (CTRL_INF_DTO ctrlInfDto : ctrlInfDtoList) {
            if (subgroupDataVO.getF_SGTM().getTime() >= ctrlInfDto.getF_EFTM().getTime()) {
                infDto = ctrlInfDto;
                break;
            }
        }
        /*未在生效日期内新建一个空对象*/
        if (infDto == null) {
            infDto = new CTRL_INF_DTO();
        }
        return infDto;
    }

    /**
     * CTRL_INF_DTO
     *
     * @param subgroupDataVO
     * @return
     */
    private SPEC_INF_DTO getSpecInfDto(SubgroupDataVO subgroupDataVO) {
        SPEC_INF_DTO specInfDto = null;
        try {
            specInfDto = specInfService.getSpecLim(new SubgroupDataDTO(subgroupDataVO.getF_PART(), subgroupDataVO.getF_REV(), subgroupDataVO.getSgrpValChildDto().getTestId(), subgroupDataVO.getF_PRCS())).getData();
        } catch (Exception e) {
            log.error("获取公差限信息失败 ex:{}", e.getMessage(), e);
            throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
        }
        return specInfDto;
//        List<SPEC_INF_DTO> specInfList =
//                redisService.getCacheList(RedisConstant.SPEC_KEY +
//                        subgroupDataVO.getF_PART() + Constants.COMMA +
//                        subgroupDataVO.getF_REV() + Constants.COMMA +
//                        subgroupDataVO.getSgrpValChildDto().getTestId());
//        SPEC_INF_VO specInfVo = new SPEC_INF_VO();
//        specInfVo.setF_PART(subgroupDataVO.getF_PART())
//                .setF_PRCS(subgroupDataVO.getF_PRCS())
//                .setF_TEST(subgroupDataVO.getSgrpValChildDto().getTestId())
//                .setF_JOB(subgroupDataVO.getF_JOB()).
//                setF_PTRV(subgroupDataVO.getF_REV());
//        return SPEC_INF_DTO.getSpecInfDto(specInfVo, specInfList);
    }

    /**
     * 发送消息
     */
    private void sendNotification(List<EVNT_INF> evntInList, LinkedHashMap<String, List<SubgroupDataVO>> subgroupDataVOMap) {
        /*获取通知配置列表*/
        List<NOTIFICATION_RULE> notificationRuleList = notificationRuleMapper.selectList(new LambdaQueryWrapper<>());
        if (CollectionUtils.isEmpty(notificationRuleList)) {
            log.info("未获取到通知配置!");
            evntInList.forEach(evntInf -> {
                evntInf.setF_STATUS(YesOrNoEnum.YES.getType());
            });
            evntInfService.updateBatchById(evntInList);
            return;
        }
        /*获取通知配置中满足条件的产品,过程,测试,标签id列表*/
        List<NotificationConditionDTO> list = new ArrayList<>();
        notificationRuleList.forEach(notificationRule -> {
            NotificationConditionDTO notificationConditionDTO = new NotificationConditionDTO();
            List<NotificationDataJsonDTO> notificationDataJsonDTOList =
                    JSONArray.parseArray(notificationRule.getF_DATA(), NotificationDataJsonDTO.class);
            /*判断是否动态*/
            notificationDataJsonDTOList.forEach(notificationDataJsonDTO -> {
                switch (PARAMETER_CHILDTypeEnum.getType(notificationDataJsonDTO.getType())) {
                    case PART_DAT:
                        if (notificationDataJsonDTO.getIsDynamic() == 1) {
                            Map<String, List<String>> dataMap = notificationDataJsonDTO.getDataMap();
                            extracted(notificationConditionDTO, dataMap);
                            notificationConditionDTO.setPartIsDynamic(true);
                            if (notificationDataJsonDTO.getIsDynamic() == 1 && MapUtils.isEmpty(notificationDataJsonDTO.getDataMap())) {
                                notificationConditionDTO.setIsPartAll(true);
                            }
                        } else {
                            if (CollectionUtils.isEmpty(notificationDataJsonDTO.getDataList())) {
                                notificationConditionDTO.setIsPartAll(true);
                            } else {
                                notificationConditionDTO.setPartList(notificationDataJsonDTO.getDataList().stream().map(Long::parseLong).collect(Collectors.toList()));
                            }
                        }
                        break;
                    case PRCS_DAT:
                        if (notificationDataJsonDTO.getIsDynamic() == 1) {
                            Map<String, List<String>> dataMap = notificationDataJsonDTO.getDataMap();
                            extracted(notificationConditionDTO, dataMap);
                            notificationConditionDTO.setPrcsIsDynamic(true);
                            if (notificationDataJsonDTO.getIsDynamic() == 1 && MapUtils.isEmpty(notificationDataJsonDTO.getDataMap())) {
                                notificationConditionDTO.setIsPrcsAll(true);
                            }
                        } else {
                            if (CollectionUtils.isEmpty(notificationDataJsonDTO.getDataList())) {
                                notificationConditionDTO.setIsPrcsAll(true);
                            } else {
                                notificationConditionDTO.setPrcsList(notificationDataJsonDTO.getDataList().stream().map(Long::parseLong).collect(Collectors.toList()));
                            }
                        }
                        break;
                    case TEST_DAT:
                        if (notificationDataJsonDTO.getIsDynamic() == 1) {
                            Map<String, List<String>> dataMap = notificationDataJsonDTO.getDataMap();
                            extracted(notificationConditionDTO, dataMap);
                            notificationConditionDTO.setTestIsDynamic(true);
                            if (notificationDataJsonDTO.getIsDynamic() == 1 && MapUtils.isEmpty(notificationDataJsonDTO.getDataMap())) {
                                notificationConditionDTO.setIsTestAll(true);
                            }
                        } else {
                            if (CollectionUtils.isEmpty(notificationDataJsonDTO.getDataList())) {
                                notificationConditionDTO.setIsTestAll(true);
                            } else {
                                notificationConditionDTO.setTestList(notificationDataJsonDTO.getDataList().stream().map(Long::parseLong).collect(Collectors.toList()));
                            }
                        }
                        break;
                    default:
                        break;
                }

            });
            notificationConditionDTO.setNotificationRule(notificationRule);
            list.add(notificationConditionDTO);
        });
        R<List<EVNT_INF_DTO>> rData = remoteEvntService.findByTagList(evntInList);
        if (rData.getCode() == Constants.FAIL) {
            log.error("-------------------报警事件获取标签信息失败-------------------");
            return;
        }
        List<EVNT_INF_DTO> evntInfDtoList = rData.getData();
        evntInfDtoList.forEach(evntInf -> {
            R<List<SubgroupDataDTO>> sgrpData = remoteSgrpInfService.findBySgrpId(evntInf.getF_SGRP(), 2);
            final List<SubgroupDataDTO> sgrpList = sgrpData.getData();
            if (!ObjectUtils.isEmpty(sgrpList)) {
                final Long plant = sgrpList.get(0).getF_PLNT();
                if (!ObjectUtils.isEmpty(sgrpList.get(0)) && !ObjectUtils.isEmpty(sgrpList.get(0).getF_PLNT())) {
                    Set<NOTIFICATION_RULE> set = new HashSet<>();
                    Set<TAG_LINK> tagLinkSet = evntInf.getTagLinkSet();
                    for (NotificationConditionDTO notificationConditionDTO : list) {
                        //报警规则 工厂校验
                        if (!Objects.equals(plant, notificationConditionDTO.getNotificationRule().getF_PLNT())) {
                            continue;
                        }
                        /*产品,过程,测试 同时满足将通知规则记录*/
                        boolean b = true;
                        /*产品*/
                        if (notificationConditionDTO.getPartIsDynamic() && !notificationConditionDTO.getIsPartAll()) {
                            if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isEmpty(notificationConditionDTO.getTagList())) {
                                /*只选了标签组*/
                                List<Long> partTagGrp = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_PART())).map(TAG_LINK::getF_TGGP).collect(Collectors.toList());
                                partTagGrp = partTagGrp.stream()
                                        .filter(notificationConditionDTO.getTagGrpList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTagGrp)) {
                                    b = false;
                                }
                            } else if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isNotEmpty(notificationConditionDTO.getTagList())) {
                                /*选了标签*/
                                List<Long> partTag = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_PART())).map(TAG_LINK::getF_TAG).collect(Collectors.toList());
                                partTag = partTag.stream()
                                        .filter(notificationConditionDTO.getTagList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTag)) {
                                    b = false;
                                }
                            }
                        } else {
                            if (!notificationConditionDTO.getIsPartAll() && !notificationConditionDTO.getPartList().contains(evntInf.getF_PART())) {
                                b = false;
                            }
                        }
                        if (!b) continue;

                        /*过程*/
                        if (notificationConditionDTO.getPrcsIsDynamic() && !notificationConditionDTO.getIsPrcsAll()) {
                            if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isEmpty(notificationConditionDTO.getTagList())) {
                                /*只选了标签组*/
                                List<Long> partTagGrp = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_PRCS())).map(TAG_LINK::getF_TGGP).collect(Collectors.toList());
                                partTagGrp = partTagGrp.stream()
                                        .filter(notificationConditionDTO.getTagGrpList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTagGrp)) {
                                    b = false;
                                }
                            } else if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isNotEmpty(notificationConditionDTO.getTagList())) {
                                /*选了标签*/
                                List<Long> partTag = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_PRCS())).map(TAG_LINK::getF_TAG).collect(Collectors.toList());
                                partTag = partTag.stream()
                                        .filter(notificationConditionDTO.getTagList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTag)) {
                                    b = false;
                                }
                            }
                        } else {
                            if (!notificationConditionDTO.getIsPrcsAll() && !notificationConditionDTO.getPrcsList().contains(evntInf.getF_PRCS())) {
                                b = false;
                            }
                        }
                        if (!b) continue;

                        /*测试*/
                        if (notificationConditionDTO.getPrcsIsDynamic() && !notificationConditionDTO.getIsTestAll()) {
                            if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isEmpty(notificationConditionDTO.getTagList())) {
                                /*只选了标签组*/
                                List<Long> partTagGrp = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_TEST())).map(TAG_LINK::getF_TGGP).collect(Collectors.toList());
                                partTagGrp = partTagGrp.stream()
                                        .filter(notificationConditionDTO.getTagGrpList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTagGrp)) {
                                    b = false;
                                }
                            } else if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList()) && CollectionUtils.isNotEmpty(notificationConditionDTO.getTagList())) {
                                /*选了标签*/
                                List<Long> partTag = tagLinkSet.stream().filter(s -> s.getF_RESOURCE().equals(evntInf.getF_TEST())).map(TAG_LINK::getF_TAG).collect(Collectors.toList());
                                partTag = partTag.stream()
                                        .filter(notificationConditionDTO.getTagList()::contains)
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(partTag)) {
                                    b = false;
                                }
                            }
                        } else {
                            if (!notificationConditionDTO.getIsTestAll() && !notificationConditionDTO.getTestList().contains(evntInf.getF_TEST())) {
                                b = false;
                            }
                        }
                        if (b) set.add(notificationConditionDTO.getNotificationRule());

                    }

                    /*根据时间降序*/
                    set = set.stream().sorted(Comparator.comparing(NOTIFICATION_RULE::getF_CRTM).reversed()).collect(Collectors.toCollection(LinkedHashSet::new));
                    if (CollectionUtils.isNotEmpty(set)) {
                        //符合条件的报警规则
                        int haveNotificationRuleSize = 0;
                        for (NOTIFICATION_RULE notificationRule : set) {
                            /*判断是否绑定了报警流程*/
                            if (StringUtils.isEmpty(notificationRule.getF_PROCESS_DEFINITION())) {
                                continue;
                            }

                            /*判断公差限和控制限*/
                            List<AlarmMessageDTO> alarmMessageDTOList = JSONArray.parseArray(evntInf.getF_DATA(), AlarmMessageDTO.class);

                            if (StringUtils.isEmpty(notificationRule.getF_ALR_DETAIL())) continue;
                            List<NotificationAlrDTO> notificationAlrDTOList = JSONArray.parseArray(notificationRule.getF_ALR_DETAIL(), NotificationAlrDTO.class);
                            List<Long> alrIds = notificationAlrDTOList.stream().map(NotificationAlrDTO::getAlrIds).flatMap(List::stream).map(Long::valueOf).collect(Collectors.toList());
                            /*判断是否满足报警描述*/
                            List<Long> collect = alarmMessageDTOList.stream().map(AlarmMessageDTO::getId).collect(Collectors.toList());
                            AtomicBoolean b = new AtomicBoolean(false);

                            alrIds.forEach(alrId -> {
                                if (collect.contains(alrId)) b.set(true);
                            });

                            if (!b.get()) {
                                /*未满足报警*/
                                continue;
                            }

                            /*判断是否是0*/
                            if (notificationRule.getF_NOTI_TYPE().equals("0")) {continue;}
                            /*十进制转二进制*/
                            String binary = Integer.toBinaryString(Integer.parseInt(notificationRule.getF_NOTI_TYPE()));
                            /*获取通知用户*/
                            EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
                            emplInfVo.setNext(Constants.NEXT);
                            emplInfVo.setF_STATUS(EMPL_INFStatusEnum.ACTIVATE.getCode());
                            emplInfVo.setDbType(InitConfig.getDriverType());
                            if (notificationRule.getF_EMPL_TYPE() == 1) {
                                emplInfVo.setRoleIds(Arrays.stream(notificationRule.getF_EMPL().split(Constants.COMMA))
                                        .filter(StringUtils::isNotEmpty).map(Long::valueOf)
                                        .collect(Collectors.toList()));
                            } else {
                                emplInfVo.setIds(Arrays.stream(notificationRule.getF_EMPL().split(Constants.COMMA))
                                        .filter(StringUtils::isNotEmpty)
                                        .map(Long::valueOf)
                                        .collect(Collectors.toList()));
                            }
                            R<List<EMPL_INF_DTO>> data = remoteUserService.getList(emplInfVo);
                            if (data.getCode() == Constants.FAIL) {continue;}
                            List<EMPL_INF_DTO> emplInfDtoList = data.getData();
                            if (CollectionUtils.isEmpty(emplInfDtoList)) {continue;}


                            /*创建系统通知*/
                            SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto = new SYSTEM_NOTIFICATION_ALARM_DTO();
                            systemNotificationAlarmDto.setAlarmTime(DateUtils.dateTimeTwo(DateUtils.getNowDate()));
                            List<SYSTEM_NOTIFICATION_ALARM_DTO.AlarmDetails> alarmDetailsList = new ArrayList<>();
                            alarmMessageDTOList.forEach(alarmMessageDTO -> {
                                SYSTEM_NOTIFICATION_ALARM_DTO.AlarmDetails alarmDetails = new SYSTEM_NOTIFICATION_ALARM_DTO.AlarmDetails();
                                alarmDetails.setChartName(alarmMessageDTO.getChart());
                                alarmDetails.setAlarmType(alarmMessageDTO.getName());
                                alarmDetails.setCompareValue(alarmMessageDTO.getCompareValue());
                                alarmDetails.setActualValue(alarmMessageDTO.getActualValue());
                                alarmDetails.setConnector(alarmMessageDTO.getConnector());
                                alarmDetailsList.add(alarmDetails);
                            });
                            systemNotificationAlarmDto.setAlarmDetailList(alarmDetailsList);
                            systemNotificationAlarmDto.setTemplateName(notificationRule.getF_NAME());
                            systemNotificationAlarmDto.setSgrpId(evntInf.getF_SGRP());
                            /*从右往左 是1对应通知所选用户*/
                            int type = NOTIFICATION_TYPEEnum.SYSTEM_MESSAGE.getType();
                            for (int i = binary.length() - 1; i >= 0; i--) {
                                if (binary.charAt(i) == '1') {
                                    switch (NOTIFICATION_TYPEEnum.getType(type)) {
                                        case SYSTEM_MESSAGE:
                                            List<SYSTEM_NOTIFICATION_INF> systemNotificationInfList = new ArrayList<>();
                                            emplInfDtoList.forEach(emplInfDto -> {
                                                SYSTEM_NOTIFICATION_INF systemNotificationInf = new SYSTEM_NOTIFICATION_INF();
                                                systemNotificationInf.setF_DATA(JSONObject.toJSONString(systemNotificationAlarmDto));
                                                systemNotificationInf.setF_TYPE(2);
                                                systemNotificationInf.setF_EMPL(emplInfDto.getF_EMPL());
                                                systemNotificationInf.setF_CRUE(emplInfDto.getF_CRUE());
                                                systemNotificationInf.setF_SYNO(null);
                                                systemNotificationInf.setF_EDUE(emplInfDto.getF_CRUE());
                                                systemNotificationInfList.add(systemNotificationInf);
                                            });
                                            systemNotificationInfService.saveBatch(systemNotificationInfList);
                                            break;
                                        case EMAIL:
                                            /*获取对应子组数据*/
                                            R<List<SubgroupDataDTO>> bySgrpId = remoteSgrpInfService.findBySgrpId(evntInf.getF_SGRP(), 2);
                                            if (bySgrpId.getCode() == Constants.FAIL) continue;
                                            List<SubgroupDataDTO> subgroupDataDTOList = bySgrpId.getData();
                                            if (CollectionUtils.isEmpty(subgroupDataDTOList)) continue;
                                            systemNotificationAlarmDto.setList(subgroupDataDTOList);

                                            EmailSendMessageDTO emailSendMessageDTO = new EmailSendMessageDTO();
                                            emailSendMessageDTO.setEmailList(emplInfDtoList.stream().map(EMPL_INF_DTO::getF_EMAIL).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
                                            emailSendMessageDTO.setTitle("报警通知");
                                            String emailContent = EmailAlarmUtil.getEmailContent(systemNotificationAlarmDto);
                                            emailSendMessageDTO.setContent(emailContent);
                                            cachedThreadPool.execute(() -> {
                                                remoteSendService.emailSend(emailSendMessageDTO);
                                            });
                                            break;
                                        case QY_WECHAT:
                                            QyWeChatSendMessageDTO qyWeChatSendMessageDTO = new QyWeChatSendMessageDTO();
                                            qyWeChatSendMessageDTO.setMsgtype("markdown");
                                            qyWeChatSendMessageDTO.setTouser(emplInfDtoList.stream().map(EMPL_INF_DTO::getF_WECHAT).filter(StringUtils::isNotEmpty).collect(Collectors.joining("|")));
                                            //JSONObject qyText = new JSONObject();
                                            JSONObject qyContent = new JSONObject();
                                          //  qyText.put("content", TextAlarmMsgUtil.getTextMsgContent(systemNotificationAlarmDto));
                                            qyContent.put("content",TextAlarmMsgUtil.getTextMsgContent(systemNotificationAlarmDto));
                                            qyWeChatSendMessageDTO.setMarkdown(qyContent);
                                            cachedThreadPool.execute(() -> {
                                                remoteSendService.qyWeChatSend(qyWeChatSendMessageDTO);
                                            });
                                            break;
                                        case DING_DING:
                                            DingDingSendMessageDTO dingDingSendMessageDTO = new DingDingSendMessageDTO();
                                            dingDingSendMessageDTO.setUserid_list(emplInfDtoList.stream().map(EMPL_INF_DTO::getF_DINGDING).filter(StringUtils::isNotEmpty).collect(Collectors.joining(Constants.COMMA)));
                                            JSONObject text = new JSONObject();
                                            JSONObject content = new JSONObject();
                                            content.put("content", TextAlarmMsgUtil.getTextMsgContent(systemNotificationAlarmDto));
                                            text.put("text", content.toJSONString());
                                            text.put("msgtype", "text");
                                            dingDingSendMessageDTO.setMsg(text);
                                            cachedThreadPool.execute(() -> {
                                                remoteSendService.dingDingSend(dingDingSendMessageDTO);
                                            });
                                            break;
                                        default:
                                            break;
                                    }
                                }
                                type++;
                            }

                            if (notificationRule.getF_STATUS() == 0) {
                                continue;
                            }
                            /*异步创建报警工作流程*/

                            /*todo 消息推送(邮件,企业微信,钉钉 可配置的第三方系统)*/

                            BPM_PROCESS_INSTANCE_VO bpmProcessInstanceVo = new BPM_PROCESS_INSTANCE_VO();
                            bpmProcessInstanceVo.setF_PROCESS_DEFINITION(notificationRule.getF_PROCESS_DEFINITION());
                            bpmProcessInstanceVo.setF_EVNT(evntInf.getF_EVNT());
                            Map<String, Object> map = new HashMap<>();
                            map.put(WorkFlowConstants.ALARMS_DESCRIBE, "报警信息");
                            map.put(WorkFlowConstants.CUSTOM_AGENT, "1");
                            if (StringUtils.isNotEmpty(notificationRule.getF_CAUSE_DATA()))
                                map.put(WorkFlowConstants.ROOT_CAUSE, notificationRule.getF_CAUSE_DATA());
                            if (StringUtils.isNotEmpty(notificationRule.getF_ACTION_DATA()))
                                map.put(WorkFlowConstants.RESPONSE_ACTION, notificationRule.getF_ACTION_DATA());
                            bpmProcessInstanceVo.setVariables(map);
                            bpmProcessInstanceVo.setF_CRUE(evntInf.getF_CRUE());
                            subgroupBpmProducer.send(bpmProcessInstanceVo);
                            haveNotificationRuleSize++;
                            /*只取第一条*/
                            break;
                        }
                        if(haveNotificationRuleSize<=0){
                            evntInf.setF_STATUS(YesOrNoEnum.YES.getType());
                        }
                    } else {
                        /*未满足报警规则 将过程事件状态改为已完成*/
                        evntInf.setF_STATUS(YesOrNoEnum.YES.getType());
                    }
                }
            }
        });

        if (CollectionUtils.isNotEmpty(evntInfDtoList)) {
            final List<Long> evntId = evntInfDtoList.stream().filter(s -> s.getF_STATUS().equals(YesOrNoEnum.YES.getType())).map(EVNT_INF_DTO::getF_EVNT).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(evntId)){return;}
            evntInfService.getBaseMapper().update(null, new LambdaUpdateWrapper<EVNT_INF>()
                    .set(EVNT_INF::getF_STATUS, YesOrNoEnum.YES.getType())
                    .set(EVNT_INF::getF_EDTM, DateUtils.getNowDate())
                    .in(EVNT_INF::getF_EVNT, evntId));
        }
    }

    private static void extracted(NotificationConditionDTO notificationConditionDTO, Map<String, List<String>> dataMap) {
        Set<Long> tagGrpList = dataMap.keySet().stream().map(Long::parseLong).collect(Collectors.toSet());
        Set<Long> tagList = dataMap.values().stream()
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream).map(Long::valueOf)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagGrpList())) {
            notificationConditionDTO.getTagGrpList().addAll(tagGrpList);
        } else {
            notificationConditionDTO.setTagGrpList(tagGrpList);
        }
        if (CollectionUtils.isNotEmpty(notificationConditionDTO.getTagList())) {
            notificationConditionDTO.getTagList().addAll(tagList);
        } else {
            notificationConditionDTO.setTagList(tagList);
        }
    }
}
