package com.yingfei.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rabbitmq.client.Channel;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.api.RemoteSamplingService;
import com.yingfei.entity.domain.RAW_INSPECTION;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.entity.enums.SamplingStrategyEnum;
import com.yingfei.entity.requestEntity.SamplingRequestEntity;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.mapper.RAW_INSPECTIONMapper;
import com.yingfei.mq.mapper.SAMPLING_TASK_CONFIGMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Component
@Slf4j
@RabbitListener(queues = RabbitConfig.QUEUE_INSPECTION_DATA_RECEIVE)
public class InspectionDataConsumer {

    @Resource
    private RAW_INSPECTIONMapper rawInspectionMapper;

    @Resource
    private SAMPLING_TASK_CONFIGMapper samplingTaskConfigMapper;
    @Resource
    private RedisService redisCache;
    @Resource
    private RemoteSamplingService remoteSamplingService;
    /**
     * 缓存过期时间1天
     */
    public final static Long EX_TIME = (long) (60*60*24);

    @RabbitHandler
    public void consumeMsg(String queueMessage, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                           @Header("spring_returned_message_correlation") CorrelationData correlationData,
                           Channel channel) throws IOException {
        try {
            log.info("接收到检验数据消息: {}", queueMessage);
            // 增加JSON解析异常处理
            final InspectionDataDTO inspectionDataDTO = JSONObject.parseObject(queueMessage, InspectionDataDTO.class);
            if (inspectionDataDTO == null) {
                log.error("消息解析失败，内容: {}", queueMessage);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            // 1. 将原始数据存入数据库
            RAW_INSPECTION rawInspection = new RAW_INSPECTION();
            final Long id = JudgeUtils.nextId();
            rawInspection.setF_ID(id);
            rawInspection.setF_SAMPLING_TASK_CONFIG_ID(inspectionDataDTO.getSamplingTaskConfigId());
            rawInspection.setF_PART_NAME(inspectionDataDTO.getPartName());
            rawInspection.setF_PRCS_NAME(inspectionDataDTO.getPrcsName());
            rawInspection.setF_PTRV_NAME(inspectionDataDTO.getPtrvName());
            rawInspection.setF_LOT_NAME(inspectionDataDTO.getLotName());
            rawInspection.setF_JOB_NAME(inspectionDataDTO.getJobName());
            rawInspection.setF_JOB_GRP_NAME(inspectionDataDTO.getJobGrpName());
            rawInspection.setF_SHIFT_NAME(inspectionDataDTO.getShiftName());
            rawInspection.setF_SHIFT_GRP_NAME(inspectionDataDTO.getShiftGrpName());
            rawInspection.setF_DESC_DATA(JSON.toJSONString(inspectionDataDTO.getDescriptors()));
            rawInspection.setF_MFPS_NAME(inspectionDataDTO.getMfpsName());
            rawInspection.setF_MFND_NAME(inspectionDataDTO.getMfndName());
            rawInspection.setF_PLAN_NAME(inspectionDataDTO.getPlanName());
            rawInspection.setF_TEST_DATA(JSON.toJSONString(inspectionDataDTO.getTestData()));
            rawInspection.setF_RECEIVE_TIME(inspectionDataDTO.getPushTime());
            rawInspectionMapper.insert(rawInspection);

            inspectionDataDTO.setId(id);
            // 2. 更新Redis缓存
            String cacheKey = RedisConstant.INSPECTION_CACHE_KEY + inspectionDataDTO.getSamplingTaskConfigId();
            String cacheBackupKey = RedisConstant.INSPECTION_CACHE_BACKUP_KEY + inspectionDataDTO.getSamplingTaskConfigId();

            // 3. 检查是否满足抽样条件 - 增加空指针检查
            LambdaQueryWrapper<SAMPLING_TASK_CONFIG> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SAMPLING_TASK_CONFIG::getF_ID, inspectionDataDTO.getSamplingTaskConfigId());
            queryWrapper.eq(SAMPLING_TASK_CONFIG::getF_DEL, DelFlagEnum.USE.getType());
            queryWrapper.eq(SAMPLING_TASK_CONFIG::getF_ENABLED, YesOrNoEnum.YES.getType());
            SAMPLING_TASK_CONFIG config = samplingTaskConfigMapper.selectOne(queryWrapper);
            if (config == null) {
                log.error("未找到抽样配置, ID: {}", inspectionDataDTO.getSamplingTaskConfigId());
                // 数据已入库，即使配置不存在仍确认消息
                channel.basicAck(deliveryTag, false);
                return;
            }

            switch (SamplingStrategyEnum.getByType(config.getF_SAMPLING_STRATEGY())) {
                case BY_PRODUCT:
                    redisCache.lSet(cacheKey+":"+inspectionDataDTO.getPartName(), inspectionDataDTO, EX_TIME);
                    redisCache.lSet(cacheBackupKey+":"+inspectionDataDTO.getPartName(), inspectionDataDTO);
                    break;
                case BY_PROCESS:
                    redisCache.lSet(cacheKey+":"+inspectionDataDTO.getPrcsName(), inspectionDataDTO, EX_TIME);
                    redisCache.lSet(cacheBackupKey+":"+inspectionDataDTO.getPrcsName(), inspectionDataDTO);
                    break;
                case BY_PRODUCT_PROCESS:
                    redisCache.lSet(cacheKey+":"+inspectionDataDTO.getPartName()+":"+inspectionDataDTO.getPrcsName(), inspectionDataDTO, EX_TIME);
                    redisCache.lSet(cacheBackupKey+":"+inspectionDataDTO.getPartName()+":"+inspectionDataDTO.getPrcsName(), inspectionDataDTO);
                    break;
                case BY_PUSH_COUNT:
                default:
                    redisCache.lSet(cacheKey, JSON.toJSONString(inspectionDataDTO), EX_TIME);
                    redisCache.lSet(cacheBackupKey, JSON.toJSONString(inspectionDataDTO));
                    break;
            }
            channel.basicAck(deliveryTag, false);
            // 4. 执行抽样任务
            asyncPerformSampling(new SamplingRequestEntity(config, rawInspection));
        } catch (Exception e) {
            log.error("消息处理失败", e);
            // 处理异常，拒绝消息并不重新入队
            channel.basicNack(deliveryTag, false, false);

        }
    }


    @Async
    public void asyncPerformSampling(SamplingRequestEntity samplingRequestEntity) {
        remoteSamplingService.performSampling(samplingRequestEntity);
    }
}

