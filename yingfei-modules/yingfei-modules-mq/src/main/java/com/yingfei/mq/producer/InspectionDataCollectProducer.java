package com.yingfei.mq.producer;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.config.RabbitConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class InspectionDataCollectProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;
    public void send(InspectionDataDTO inspectionDataDTO, CorrelationData correlationData) {
        try {
            if(ObjectUtils.isEmpty(inspectionDataDTO.getPushTime())){
                inspectionDataDTO.setPushTime(new Date());
            }
            rabbitTemplate.convertAndSend(
                    RabbitConfig.EXCHANGE_INSPECTION,
                    RabbitConfig.ROUTING_KEY_INSPECTION_DATA_RECEIVE,
                    JSONObject.toJSONString(inspectionDataDTO),
                    correlationData
            );
            log.info("检验数据已发送至消息队列");
        } catch (Exception e) {
            log.error("发送检验数据到消息队列失败", e);
        }
    }
}
