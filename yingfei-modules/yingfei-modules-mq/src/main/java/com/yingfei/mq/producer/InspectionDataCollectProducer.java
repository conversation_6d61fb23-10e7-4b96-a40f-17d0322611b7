package com.yingfei.mq.producer;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.domain.MESSAGE_LOG_INF;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.config.RabbitConfig;
import com.yingfei.mq.mapper.MESSAGE_LOG_INFMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class InspectionDataCollectProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private RedisService redisService;
    @Resource
    private MESSAGE_LOG_INFMapper messageLogInfMapper;

    public void send(InspectionDataDTO inspectionDataDTO) {
        try {
            if(ObjectUtils.isEmpty(inspectionDataDTO.getPushTime())){
                inspectionDataDTO.setPushTime(new Date());
            }
            /*保存消息日志*/
            Long aLong = JudgeUtils.defaultIdentifierGenerator.nextId(null);
            CorrelationData correlationData = new CorrelationData(String.valueOf(aLong));
            EMPL_INF_DTO emplInfDto = redisService.getCacheObject(RedisConstant.ADMIN_USER_ID);
            String jsonString = JSONObject.toJSONString(inspectionDataDTO);
            MESSAGE_LOG_INF messageLogInf = new MESSAGE_LOG_INF();
            messageLogInf.setF_NEWS(Long.valueOf(correlationData.getId()));
            messageLogInf.setF_DATA(jsonString);
            messageLogInf.setF_CRUE(emplInfDto.getF_EMPL());
            messageLogInf.setF_TYPE(4);
            messageLogInfMapper.insert(messageLogInf);

            RabbitConfig.executor.schedule(() -> {
                rabbitTemplate.convertAndSend(
                        RabbitConfig.INSPECTION_PUSH_EXCHANGE_NAME,
                        RabbitConfig.INSPECTION_PUSH_ROUTING_KEY,
                        JSONObject.toJSONString(inspectionDataDTO),
                        correlationData
                );
            }, 1, TimeUnit.SECONDS);
            log.info("检验数据已发送至消息队列");
        } catch (Exception e) {
            log.error("发送检验数据到消息队列失败 e:{} ex:{}", e,e.getMessage());
            throw new RuntimeException("SEND_INSPECTION_DATA_FAILED");
        }
    }
}
