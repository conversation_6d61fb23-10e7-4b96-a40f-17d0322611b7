package com.yingfei.report.config;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.context.SecurityContextHolder;
import com.yingfei.common.security.auth.AuthUtil;
import com.yingfei.entity.model.LoginUser;
import org.jeecg.modules.jmreport.api.JmReportTokenServiceI;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Component
class JimuReportTokenService implements JmReportTokenServiceI {

    @Override
    public String getToken(HttpServletRequest request) {
        String token = null;
            if (request != null) {
                token = request.getParameter("token");
                if (token == null) {
                    token = request.getHeader("X-Access-Token");
                } else {
                    token = request.getHeader("token");
                }
            }
        return token;
    }

    @Override
    public String getUsername(String token) {
        LoginUser loginUser = new LoginUser();
        if (com.yingfei.common.core.utils.StringUtils.isNotEmpty(token)) {
            loginUser = AuthUtil.getLoginUser(token);
            if (com.yingfei.common.core.utils.StringUtils.isNotNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return loginUser.getUsername();
    }

    @Override
    public String[] getRoles(String token) {
        return new String[]{"admin","lowdeveloper","dbadeveloper"};
    }

    @Override
    public Boolean verifyToken(String token) {
        return Boolean.TRUE;
    }

    @Override
    public Map<String, Object> getUserInfo(String token) {
        LoginUser loginUser = new LoginUser();
        if (com.yingfei.common.core.utils.StringUtils.isNotEmpty(token)) {
            loginUser = AuthUtil.getLoginUser(token);
            if (com.yingfei.common.core.utils.StringUtils.isNotNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return BeanUtil.copyProperties(loginUser, JSONObject.class);
    }

    @Override
    public HttpHeaders customApiHeader() {
        return null;
    }

    @Override
    public String[] getPermissions(String token) {
        return new String[]{"drag:datasource:testConnection","onl:drag:clear:recovery","drag:analysis:sql","drag:design:getTotalData"};
    }
}
