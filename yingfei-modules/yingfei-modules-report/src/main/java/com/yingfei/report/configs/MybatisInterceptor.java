//package com.yingfei.report.config;
//
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.executor.statement.StatementHandler;
//import org.apache.ibatis.mapping.*;
//import org.apache.ibatis.plugin.*;
//import org.apache.ibatis.session.ResultHandler;
//import org.apache.ibatis.session.RowBounds;
//import org.springframework.beans.BeanUtils;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.jdbc.core.PreparedStatementCallback;
//import org.springframework.jdbc.core.PreparedStatementCreator;
//import org.springframework.jdbc.core.RowMapper;
//import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.ReflectionUtils;
//
//import java.beans.PropertyDescriptor;
//import java.lang.reflect.Method;
//import java.sql.Statement;
//import java.util.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//import static java.util.regex.Pattern.*;
//
//
///**
// * <AUTHOR>
// * @Description
// * @Date 2021/12/14 16:56
// */
//@Intercepts({@Signature(type = NamedParameterJdbcTemplate.class, method = "queryForObject", args = {String.class, Map.class, RowMapper.class}),
//        @Signature(type = Executor.class, method = "update", args = {Statement.class}),
//        @Signature(type = Executor.class, method = "batch", args = {Statement.class})})
//@Component
//@Configuration
//public class MybatisInterceptor implements Interceptor {
//
//
//    private final static String JC_DYNAMICS = "JcDynamics";
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        //获取执行参数
//        Object[] objects = invocation.getArgs();
//        MappedStatement ms = (MappedStatement) objects[0];
//        Object objectParam = objects[1];
//        List<ParamClassInfo> paramClassInfos = convertParamList(objectParam);
//        String queryConditionSqlScene = getQueryConditionSqlScene(paramClassInfos);
//        //解析执行sql的map方法,开始自定义规则匹配逻辑
//        String mapperMethodAllName = ms.getId();
//        int lastIndex = mapperMethodAllName.lastIndexOf(".");
//        String mapperClassStr = mapperMethodAllName.substring(0, lastIndex);
//        String mapperClassMethodStr = mapperMethodAllName.substring((lastIndex + 1));
//        Class<?> mapperClass = Class.forName(mapperClassStr);
//        Method[] methods = mapperClass.getMethods();
//        for (Method method : methods) {
//            if (method.getName().equals(mapperClassMethodStr) && mapperClassMethodStr.contains(JC_DYNAMICS)) {
//                BoundSql boundSql = ms.getSqlSource().getBoundSql(objects[1]);
//                String originalSql = boundSql.getSql().toLowerCase(Locale.CHINA).replace("[\\t\\n\\r]", " ");
//                //进行自动的 条件拼接
//                String newSql = originalSql + queryConditionSqlScene;
//                BoundSql newBoundSql = new BoundSql(ms.getConfiguration(), newSql,
//                        boundSql.getParameterMappings(), boundSql.getParameterObject());
//                MappedStatement newMs = newMappedStatement(ms, new MyBoundSqlSqlSource(newBoundSql));
//                for (ParameterMapping mapping : boundSql.getParameterMappings()) {
//                    String prop = mapping.getProperty();
//                    if (boundSql.hasAdditionalParameter(prop)) {
//                        newBoundSql.setAdditionalParameter(prop, boundSql.getAdditionalParameter(prop));
//                    }
//                }
//                Object[] queryArgs = invocation.getArgs();
//                queryArgs[0] = newMs;
//                System.out.println("打印新SQL语句" + newSql);
//            }
//        }
//        //继续执行逻辑
//        return invocation.proceed();
//    }
//
//    private String getQueryConditionSqlScene(List<ParamClassInfo> paramClassInfos) {
//        StringBuilder conditionParamBuilder = new StringBuilder();
//        if (CollectionUtils.isEmpty(paramClassInfos)) {
//            return "";
//        }
//        conditionParamBuilder.append("  WHERE ");
//        int size = paramClassInfos.size();
//        for (int index = 0; index < size; index++) {
//            ParamClassInfo paramClassInfo = paramClassInfos.get(index);
//            String keyName = paramClassInfo.getKeyName();
//            //默认驼峰拆成下划线 ，比如 userName -》 user_name ,   name -> name
//            //如果是需要取别名，其实可以加上自定义注解这些，但是本篇例子是轻封装，思路给到，你们i自己玩
//            String underlineKeyName = camelToUnderline(keyName);
//            conditionParamBuilder.append(underlineKeyName);
//            Object keyValue = paramClassInfo.getKeyValue();
//            String classType = paramClassInfo.getClassType();
//            //其他类型怎么处理 ，可以按照类型区分 ，比如检测到一组开始时间，Date 拼接 between and等
////            if (classType.equals("String")){
////                conditionParamBuilder .append("=").append("\'").append(keyValue).append("\'");
////            }
//
//            conditionParamBuilder.append("=").append("\'").append(keyValue).append("\'");
//            if (index != size - 1) {
//                conditionParamBuilder.append(" AND ");
//            }
//        }
//        return conditionParamBuilder.toString();
//    }
//
//    private static List<ParamClassInfo> convertParamList(Object obj) {
//        List<ParamClassInfo> paramClassList = new ArrayList<>();
//        for (PropertyDescriptor pd : BeanUtils.getPropertyDescriptors(obj.getClass())) {
//            if (!"class".equals(pd.getName())) {
//                if (ReflectionUtils.invokeMethod(pd.getReadMethod(), obj) != null) {
//                    ParamClassInfo paramClassInfo = new ParamClassInfo();
//                    paramClassInfo.setKeyName(pd.getName());
//                    paramClassInfo.setKeyValue(ReflectionUtils.invokeMethod(pd.getReadMethod(), obj));
//                    paramClassInfo.setClassType(pd.getPropertyType().getSimpleName());
//                    paramClassList.add(paramClassInfo);
//                }
//            }
//        }
//        return paramClassList;
//    }
//
//
//    public static String camelToUnderline(String line) {
//        if (line == null || "".equals(line)) {
//            return "";
//        }
//        line = String.valueOf(line.charAt(0)).toUpperCase().concat(line.substring(1));
//        StringBuffer sb = new StringBuffer();
//        Pattern pattern = compile("[A-Z]([a-z\\d]+)?");
//        Matcher matcher = pattern.matcher(line);
//        while (matcher.find()) {
//            String word = matcher.group();
//            sb.append(word.toUpperCase());
//            sb.append(matcher.end() == line.length() ? "" : "_");
//        }
//        return sb.toString();
//    }
//
//
//    @Override
//    public Object plugin(Object o) {
//        //获取代理权
//        if (o instanceof Executor) {
//            //如果是Executor（执行增删改查操作），则拦截下来
//            return Plugin.wrap(o, this);
//        } else {
//            return o;
//        }
//    }
//
//    /**
//     * 定义一个内部辅助类，作用是包装 SQL
//     */
//    class MyBoundSqlSqlSource implements SqlSource {
//        private BoundSql boundSql;
//
//        public MyBoundSqlSqlSource(BoundSql boundSql) {
//            this.boundSql = boundSql;
//        }
//
//        @Override
//        public BoundSql getBoundSql(Object parameterObject) {
//            return boundSql;
//        }
//
//    }
//
//    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
//        MappedStatement.Builder builder = new
//                MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
//        builder.resource(ms.getResource());
//        builder.fetchSize(ms.getFetchSize());
//        builder.statementType(ms.getStatementType());
//        builder.keyGenerator(ms.getKeyGenerator());
//        if (ms.getKeyProperties() != null && ms.getKeyProperties().length > 0) {
//            builder.keyProperty(ms.getKeyProperties()[0]);
//        }
//        builder.timeout(ms.getTimeout());
//        builder.parameterMap(ms.getParameterMap());
//        builder.resultMaps(ms.getResultMaps());
//        builder.resultSetType(ms.getResultSetType());
//        builder.cache(ms.getCache());
//        builder.flushCacheRequired(ms.isFlushCacheRequired());
//        builder.useCache(ms.isUseCache());
//        return builder.build();
//    }
//
//
//    @Override
//    public void setProperties(Properties properties) {
//        //读取mybatis配置文件中属性
//    }
//
//}
