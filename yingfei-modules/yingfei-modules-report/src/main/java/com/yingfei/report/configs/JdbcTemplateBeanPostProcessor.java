//package com.yingfei.report.config;
//
//import org.springframework.beans.BeansException;
//import org.springframework.beans.factory.config.BeanPostProcessor;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//
//@Component
//public class JdbcTemplateBeanPostProcessor implements BeanPostProcessor {
//
//    @Override
//    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
//        if (bean instanceof JdbcTemplate) {
//            JdbcTemplate jdbcTemplate = (JdbcTemplate) bean;
//            DataSource dataSource = jdbcTemplate.getDataSource();
//            // 这里可以添加自定义的拦截逻辑
//            // 比如，你可以返回一个代理对象，该代理对象会拦截 execute 方法的调用
//            MyJdbcTemplate myJdbcTemplate = new MyJdbcTemplate(dataSource);
//            return myJdbcTemplate;
//        }
//        else if (bean instanceof NamedParameterJdbcTemplate){
//            NamedParameterJdbcTemplate namedParameterJdbcTemplate = (NamedParameterJdbcTemplate) bean;
//            DataSource dataSource = namedParameterJdbcTemplate.getJdbcTemplate().getDataSource();
//            MyNamedParameterJdbcTemplate myNamedParameterJdbcTemplate = new MyNamedParameterJdbcTemplate(dataSource);
//            return myNamedParameterJdbcTemplate;
//        }
//        return bean;
//    }
//
//    @Override
//    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
//        return bean;
//    }
//}
