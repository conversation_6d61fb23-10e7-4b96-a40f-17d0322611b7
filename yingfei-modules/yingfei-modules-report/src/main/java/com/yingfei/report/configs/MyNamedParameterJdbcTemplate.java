//package com.yingfei.report.config;
//
//import org.jetbrains.annotations.NotNull;
//import org.springframework.dao.DataAccessException;
//import org.springframework.jdbc.core.RowMapper;
//import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
//import org.springframework.jdbc.core.namedparam.SqlParameterSource;
//
//import javax.sql.DataSource;
//import java.util.List;
//
//public class MyNamedParameterJdbcTemplate extends NamedParameterJdbcTemplate {
//    public MyNamedParameterJdbcTemplate(DataSource dataSource) {
//        super(dataSource);
//    }
//
//    @Override
//    public <T> List<T> query(String sql, SqlParameterSource paramSource, RowMapper<T> rowMapper)
//            throws DataAccessException {
//        sql = getSql(sql);
//        return super.query(sql, paramSource, rowMapper);
//    }
//
//    @NotNull
//    private static String getSql(String sql) {
//        if (sql.contains("jimu") ||
//                sql.contains("huiyuan") ||
//                sql.contains("onl_drag") ||
//                sql.contains("rep_demo")) {
//            /*将sql全转为小写*/
//            String[] split = sql.split(" ");
//            StringBuffer sb = new StringBuffer();
//            for (String s : split) {
//                if (!s.contains(":"))
//                    s = s.toLowerCase();
//                sb.append(s).append(" ");
//            }
//            sql = sb.toString();
//        }
//        return sql;
//    }
//
//    @Override
//    public <T> T queryForObject(String sql, SqlParameterSource paramSource, RowMapper<T> rowMapper)
//            throws DataAccessException {
//        /*判断是jimu huiyuan  onl_drag rep_demo的表*/
//        /*SELECT count(0) FROM jimu_report jr LEFT JOIN jimu_report_share jrs ON jrs.report_id = jr.id WHERE 1 = 1 AND jr.TYPE IN (:typeList) AND jr.DEL_FLAG = :jimuReport.delFlag AND jr.TEMPLATE = :jimuReport.template*/
//        sql = getSql(sql);
//        return super.queryForObject(sql, paramSource, rowMapper);
//    }
//
//    @Override
//    public int update(String sql, SqlParameterSource paramSource) throws DataAccessException {
//        sql = getSql(sql);
//        return super.update(sql, paramSource);
//    }
//}
