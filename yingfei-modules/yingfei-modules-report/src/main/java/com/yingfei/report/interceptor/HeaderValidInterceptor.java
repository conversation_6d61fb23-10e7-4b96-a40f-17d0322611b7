//package com.yingfei.report.interceptor;
//
//
//import org.apache.commons.lang3.StringUtils;
//import org.jacoco.agent.rt.internal_f3994fa.core.data.SessionInfo;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//@Component
//public class HeaderValidInterceptor implements HandlerInterceptor {
//
//    @Override
//    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object object) throws Exception {
//        String token = httpServletRequest.getParameter("token");
//        String username = httpServletRequest.getParameter("userName");
//        String owner = httpServletRequest.getParameter("owner");
//        if (StringUtils.isBlank(token)) {
//            throw BizException.with(BizExceptionEnum.SYSTEM_ERROR, "无数据查看权限");
//        }
//        JwtHelper jwtHelper = new JwtHelper();
//        SessionInfo sessionInfo = jwtHelper.validateTokenAndGetSession(token);
//        sessionInfo.getOwner().equals(owner);
//        if (sessionInfo == null || !sessionInfo.getUserName().equals(username) || !sessionInfo.getOwner().equals(owner)) {
//            throw BizException.with(BizExceptionEnum.SYSTEM_ERROR, "无数据查看权限");
//        }
//        return true;
//    }
//}
