//package com.yingfei.report.config;
//
//import com.yingfei.common.core.constant.Constants;
//import com.yingfei.common.security.config.DownloadConfig;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//@Configuration
//public class CustomCorsConfiguration implements WebMvcConfigurer {
////    @Override
////    public void addCorsMappings(CorsRegistry registry) {
////        registry.addMapping("/**") // 所有接口
////                .allowCredentials(true) // 是否发送 Cookie
////                .allowedOriginPatterns("*") // 支持域
////                .allowedMethods(new String[]{"GET", "POST", "PUT", "DELETE"}) // 支持方法
////                .allowedHeaders("*")
////                .exposedHeaders("*");
////    }
//
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/**")
//                .addResourceLocations("classpath:/static/")
//                .addResourceLocations("classpath:/META-INF/resources/")
//        ;
//    }
//}
