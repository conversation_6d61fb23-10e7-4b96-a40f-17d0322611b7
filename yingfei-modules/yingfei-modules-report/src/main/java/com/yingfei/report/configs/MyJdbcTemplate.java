//package com.yingfei.report.config;
//
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson2.JSONArray;
//import org.apache.poi.ss.formula.functions.T;
//import org.springframework.jdbc.core.*;
//import org.springframework.lang.Nullable;
//
//import javax.sql.DataSource;
//import java.sql.Connection;
//import java.sql.PreparedStatement;
//import java.sql.SQLException;
//import java.util.ArrayList;
//import java.util.List;
//
//public class MyJdbcTemplate extends JdbcTemplate {
//
//    MyJdbcTemplate(DataSource dataSource) {
//        super(dataSource);
//    }
//
//    @Override
//    public <T> T query(PreparedStatementCreator psc, @Nullable final PreparedStatementSetter pss, final ResultSetExtractor<T> rse) {
//        // 在这里添加自定义的逻辑
//        // 例如，可以在执行前后添加日志记录
//        String sql = ((SqlProvider) psc).getSql();
//        if (sql.contains("jimu") ||
//                sql.contains("huiyuan") ||
//                sql.contains("onl_drag") ||
//                sql.contains("rep_demo")) {
//            /*将sql全转为小写*/
//            sql = sql.toLowerCase();
////            JSONArray parse = JSONArray.parse(psc.toString().split("parameters=")[1]);
////            Object[] array = parse.toArray();
////            PreparedStatementCreatorFactory preparedStatementCreatorFactory = new PreparedStatementCreatorFactory(sql);
////            psc = preparedStatementCreatorFactory.newPreparedStatementCreator(sql, array);
//        }
//
//        return super.query(psc, pss, rse);
//        // 或者可以在这里添加安全性检查
//    }
//}
