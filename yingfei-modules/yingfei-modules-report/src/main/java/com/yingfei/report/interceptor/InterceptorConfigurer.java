//package com.yingfei.report.interceptor;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.annotation.Resource;
//
//@Configuration
//public class InterceptorConfigurer implements WebMvcConfigurer {
//    @Resource
//    private HeaderValidInterceptor headerValidInterceptor;
//
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(headerValidInterceptor).addPathPatterns("/jmreport/shareView/**")
//                .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v3/**", "/v2/**", "/swagger-ui.html/**", "doc.html", "/error")
//                .excludePathPatterns("/error");
//
//    }
//
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**")
//                .allowedOrigins("*")
//                .allowedMethods("GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "TRACE")
//                .allowCredentials(true);
//    }
//
//}
