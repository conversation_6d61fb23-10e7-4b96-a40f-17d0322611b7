package com.yingfei.report.config;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.drag.service.IOnlDragExternalService;
import org.jeecg.modules.drag.vo.DragDictModel;
import org.jeecg.modules.drag.vo.DragLogDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class IOnlDragExternalServiceImpl implements IOnlDragExternalService {
    @Override
    public Map<String, List<DragDictModel>> getManyDictItems(List<String> codeList, List<JSONObject> tableDictList) {
        return IOnlDragExternalService.super.getManyDictItems(codeList, tableDictList);
    }

    @Override
    public List<DragDictModel> getDictItems(String dictCode) {
        return IOnlDragExternalService.super.getDictItems(dictCode);
    }

    @Override
    public void addLog(DragLogDTO dto) {
        IOnlDragExternalService.super.addLog(dto);
    }

    @Override
    public void addLog(String logMsg, int logType, int operateType) {
        IOnlDragExternalService.super.addLog(logMsg, logType, operateType);
    }

    @Override
    public List<DragDictModel> getTableDictItems(String dictTable, String dictText, String dictCode) {
        return IOnlDragExternalService.super.getTableDictItems(dictTable, dictText, dictCode);
    }

    @Override
    public List<DragDictModel> getCategoryTreeDictItems(List<String> ids) {
        return IOnlDragExternalService.super.getCategoryTreeDictItems(ids);
    }

    @Override
    public List<DragDictModel> getUserDictItems(List<String> ids) {
        return IOnlDragExternalService.super.getUserDictItems(ids);
    }

    @Override
    public List<DragDictModel> getDeptsDictItems(List<String> ids) {
        return IOnlDragExternalService.super.getDeptsDictItems(ids);
    }
}
