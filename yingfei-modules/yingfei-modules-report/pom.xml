<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yingfei</groupId>
        <artifactId>yingfei-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yingfei-modules-report</artifactId>

    <description>
        yingfei-modules-report系统模块
    </description>

    <dependencies>
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-api-dataManagement</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- SQL Server Connector -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <!-- Oracle Connector -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
        </dependency>

        <!-- PostgreSQL Connector -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- yingfei Common DataSource -->
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-domain</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- yingfei Common DataScope -->
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-common-datasource</artifactId>
        </dependency>

        <!-- yingfei Common Log -->
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-common-log</artifactId>
        </dependency>

        <!-- yingfei Common Swagger -->
        <dependency>
            <groupId>com.yingfei</groupId>
            <artifactId>yingfei-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
<!--        &lt;!&ndash;积木报表依赖&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimureport-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;积木 BI 依赖&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimubi-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; mogodb和redis支持 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimureport-nosql-starter</artifactId>-->
<!--            <version>1.6.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!-- mongo、redis和文件数据集支持包，按需引入 -->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimureport-nosql-starter</artifactId>-->
<!--            <version>2.0.0</version>-->
<!--        </dependency>-->
        <!-- 后台导出接口Echart图表支持包，按需引入 -->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimureport-echarts-starter</artifactId>-->
<!--            <version>2.1.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimubi-spring-boot-starter</artifactId>
            <version>2.1.3</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
