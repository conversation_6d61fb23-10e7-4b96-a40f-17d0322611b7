package com.yingfei.dataManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.CommonExceptionEnum;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.dataManagement.config.InitConfig;
import com.yingfei.dataManagement.mapper.CTRL_INFMapper;
import com.yingfei.dataManagement.service.*;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.entity.domain.CTRL_INF;
import com.yingfei.entity.domain.PART_INF;
import com.yingfei.entity.domain.PRCS_INF;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.ControlChartDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.enums.ParetoAnalyseTypeEnum;
import com.yingfei.entity.enums.ShortSdTermTypeEnum;
import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.vo.CTRL_INF_VO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【CTRL_INF(储存控制限表)】的数据库操作Service实现
 * @createDate 2024-05-08 16:26:53
 */
@Service
public class CTRL_INFServiceImpl extends ServiceImpl<CTRL_INFMapper, CTRL_INF>
        implements CTRL_INFService {

    @Resource
    private RedisService redisService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private SGRP_INFService sgrpInfService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private TEST_INFService testInfService;
    @Resource
    private PART_INFService partInfService;
    @Resource
    private PRCS_INFService prcsInfService;
    @Resource
    private PARAMETER_SET_INFService parameterSetInfService;
    @Resource
    private ChartCommonService chartCommonService;

    @Override
    public long getTotal(CTRL_INF_VO ctrlInfVo) {
        return baseMapper.getTotal(ctrlInfVo);
    }

    @Override
    public List<CTRL_INF_DTO> getList(CTRL_INF_VO ctrlInfVo) {
        return baseMapper.getList(ctrlInfVo);
    }

    @Override
    public void add(CTRL_INF_VO ctrlInfVo) {
        CTRL_INF ctrlInf = new CTRL_INF();
        BeanUtils.copyPropertiesIgnoreNull(ctrlInfVo, ctrlInf);
        ctrlInf.setF_CTRL(JudgeUtils.defaultIdentifierGenerator.nextId(null));
        baseMapper.insert(ctrlInf);
//        threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
//            try {
//                String key = RedisConstant.CTRL_KEY +
//                        ctrlInfVo.getF_PART() + Constants.COMMA +
//                        ctrlInfVo.getF_PTRV() + Constants.COMMA +
//                        ctrlInfVo.getF_PRCS() + Constants.COMMA +
//                        ctrlInfVo.getF_TEST();
//                CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
//                BeanUtils.copyPropertiesIgnoreNull(ctrlInf, ctrlInfDto);
//                redisService.setCacheList(key, Collections.singletonList(ctrlInfDto), null);
//            } catch (Exception e) {
//                log.error("redis添加控制限报错----->{}", e);
//                e.printStackTrace();
//            }
//        });
        CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
        BeanUtils.copyPropertiesIgnoreNull(ctrlInf, ctrlInfDto);
        addCache(ctrlInfDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        /*根据id列表修改对应的删除状态*/
        LambdaUpdateWrapper<CTRL_INF> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CTRL_INF::getF_CTRL, ids).set(CTRL_INF::getF_DEL, YesOrNoEnum.YES.getType())
                .set(CTRL_INF::getF_EDTM, new Date())
                .set(CTRL_INF::getF_EDUE, SecurityUtils.getUserId());
        baseMapper.update(null, updateWrapper);

        LambdaQueryWrapper<CTRL_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CTRL_INF::getF_CTRL, ids);
        List<CTRL_INF> ctrlInfs = baseMapper.selectList(queryWrapper);
        for (CTRL_INF inf : ctrlInfs) {
//            threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
//                extracted(inf);
//            });
            CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
            BeanUtils.copyPropertiesIgnoreNull(inf, ctrlInfDto);
            delCache(ctrlInfDto);
        }
    }



    @Override
    public void checkParam(CTRL_INF_VO ctrlInfVo) {
        // 检查必填参数
        if (ctrlInfVo.getF_PART() == null || ctrlInfVo.getF_PRCS() == null ||
                ctrlInfVo.getF_TEST() == null || ctrlInfVo.getF_PTRV() == null || ctrlInfVo.getF_EFTM() == null) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        PART_INF partInf = partInfService.getById(ctrlInfVo.getF_PART());
        TEST_INF testInf = testInfService.getById(ctrlInfVo.getF_TEST());
        PRCS_INF prcsInf = prcsInfService.getById(ctrlInfVo.getF_PRCS());

        // 获取产品、过程和测试的工厂信息
        Long partPlnt = partInf.getF_PLNT();
        Long testPlnt = testInf.getF_PLNT();
        Long prcsPlnt = prcsInf.getF_PLNT();
        // 校验工厂是否一致
        if (!Objects.equals(partPlnt, testPlnt) || !Objects.equals(partPlnt, prcsPlnt)) {
            throw new BusinessException(DataManagementExceptionEnum.PLNT_NOT_MATCH);
        }

        // 1. 根据产品,过程,测试,版本查询历史数据
        LambdaQueryWrapper<CTRL_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CTRL_INF::getF_PART, ctrlInfVo.getF_PART())
                .eq(CTRL_INF::getF_PRCS, ctrlInfVo.getF_PRCS())
                .eq(CTRL_INF::getF_TEST, ctrlInfVo.getF_TEST())
                .eq(CTRL_INF::getF_PTRV, ctrlInfVo.getF_PTRV());
        List<CTRL_INF> ctrlInfList = baseMapper.selectList(queryWrapper);

        // 2. 产品,过程,测试,版本,生效日期 唯一数据不能重复
        List<CTRL_INF> sameDataList = ctrlInfList.stream()
                .filter(inf -> inf.getF_EFTM().equals(ctrlInfVo.getF_EFTM()) && inf.getF_DEL().equals(YesOrNoEnum.NO.getType()))
                .collect(Collectors.toList());

        if (!sameDataList.isEmpty()) {
            // 新增||编辑操作 验证唯一数据
            if (ctrlInfVo.getF_CTRL() == null || sameDataList.stream().noneMatch(inf -> inf.getF_CTRL().equals(ctrlInfVo.getF_CTRL()))) {
                throw new BusinessException(DataManagementExceptionEnum.CTRL_DUPLICATION_EXCEPTION);
            }
        }

        // 3. 存在有效（del=0）历史数据图表类型需要与历史数据图表类型相同
        // 4. 新增数据 如有历史数据图表类型需得相同
        // 5. 编辑数据 如有历史数据图表类型需得相同
        Optional<CTRL_INF> effectiveHistoryData = ctrlInfList.stream()
                .filter(inf -> inf.getF_DEL().equals(YesOrNoEnum.NO.getType()))
                .findFirst();
        if(ctrlInfList.size()>1){
            if (effectiveHistoryData.isPresent()) {
                CTRL_INF historyData = effectiveHistoryData.get();
                if (!Objects.equals(historyData.getF_CHART_TYPE(), ctrlInfVo.getF_CHART_TYPE())) {
                    throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_MISMATCH_EXCEPTION);
                }
            }
        }
    }


    @Override
    public void edit(CTRL_INF ctrlInf) {
        baseMapper.updateById(ctrlInf);
        CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
        BeanUtils.copyPropertiesIgnoreNull(ctrlInf, ctrlInfDto);
        delCache(ctrlInfDto);
//        threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
//            extracted(ctrlInf);
//        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum) {
        List<CTRL_INF> list = getCondition(ids, paretoAnalyseTypeEnum);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> collect = list.stream().map(CTRL_INF::getF_CTRL).collect(Collectors.toList());
            /*根据id列表修改对应的删除状态*/
            LambdaUpdateWrapper<CTRL_INF> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(CTRL_INF::getF_CTRL, collect)
                    .set(CTRL_INF::getF_DEL, YesOrNoEnum.YES.getType())
                    .set(CTRL_INF::getF_EDTM, new Date())
                    .set(CTRL_INF::getF_EDUE, SecurityUtils.getUserId());
            baseMapper.update(null, updateWrapper);
        }
    }

    @Override
    public List<ControlChartDTO> ctrlGenerate(CTRL_INF_VO ctrlInfVo) {
        if (CollectionUtils.isEmpty(ctrlInfVo.getPartIds()) ||
                CollectionUtils.isEmpty(ctrlInfVo.getPrcsIds()) ||
                CollectionUtils.isEmpty(ctrlInfVo.getTestIds())) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        List<ControlChartDTO> list = new ArrayList<>();
        /*根据条件获取子组*/
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        subgroupDataSelectionDTO.setPartList(ctrlInfVo.getPartIds());
        subgroupDataSelectionDTO.setPtrvList(ctrlInfVo.getPtrvIds());
        subgroupDataSelectionDTO.setPrcsList(ctrlInfVo.getPrcsIds());
        subgroupDataSelectionDTO.setTestList(ctrlInfVo.getTestIds());
        subgroupDataSelectionDTO.setDbType(ctrlInfVo.getDbType());
        List<SubgroupDataDTO> subgroupDataDTOList = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(subgroupDataDTOList)) {
            return list;
        }
        List<SubgroupDataDTO> reassembly = chartCommonService.reassembly(subgroupDataDTOList);
        ctrlGenerate(list, reassembly);
        return list;
    }

    /**
     * 根据指定参数反推控制限的过程均值和过程西格玛（仅对变量生效）
     * @param controlLimitDTO
     * @param type
     * @return
     */
    @Override
    public ControlLimitDTO backstepping(ControlLimitDTO controlLimitDTO, Integer type) {
        /*判断测试是否为缺陷或者不良*/
        TEST_INF testInf = testInfService.getById(controlLimitDTO.getSpecInfDto().getF_TEST());
        HistogramUtil.backstepping(controlLimitDTO, type, testInf.getF_TYPE());
        return controlLimitDTO;
    }

    /**
     * 使用历史值计算控制限
     * @param ctrlInfVo
     */
    @Override
    public void calculateControlLimit(CTRL_INF_VO ctrlInfVo, List<SubgroupDataDTO> list) {
       if(CollectionUtils.isEmpty(list)){
           SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
           subgroupDataSelectionDTO
                   .setF_PART(ctrlInfVo.getF_PART())
                   .setF_REV(ctrlInfVo.getF_PTRV())
                   .setF_PRCS(ctrlInfVo.getF_PRCS())
                   .setF_TEST(ctrlInfVo.getF_TEST())
                   //todo 后期改成可配置的
                   .setTotalNum(30);
           subgroupDataSelectionDTO.setDbType(ctrlInfVo.getDbType());
           list = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
       }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<SubgroupDataDTO> reassembly = chartCommonService.reassembly(list);
        TEST_INF testInf = testInfService.getById(list.get(0).getF_TEST());
        Integer testType = testInf.getF_TYPE();
        if(testType.equals(1)) {
            /*类型为变量时，需要计算均值和标准*/
            DataSummaryDTO basic = DataSummaryDTO.getBasic(reassembly, 1, 0);
            ctrlInfVo.setF_MEAN(basic.getMean());
            ctrlInfVo.setF_SP(basic.getShortTermStandardDeviation());
        }
        else if(testType.equals(2) || testType.equals(3)){
            /*类型为缺陷或者不良时，需要不良率或缺陷率*/
            DataSummaryDTO basic = DataSummaryDTO.getBasic(reassembly, 1, 0);
            DataSummaryDTO.recalculateMean(basic,list);
            ctrlInfVo.setF_MEAN(basic.getMean());

            /*计算SigmaZ，前端需要判断图表类型，只有在LaneyP和LaneyU的时候才使用这个值*/
            Double sigmaZ = getSigmaZ(list, basic.getMean(),ctrlInfVo.getF_CHART_TYPE());
            ctrlInfVo.setF_SP(sigmaZ);
//            if(ctrlInfVo.getF_CHART_TYPE()!=null && (ctrlInfVo.getF_CHART_TYPE().equals(14) || ctrlInfVo.getF_CHART_TYPE().equals(15)) ) {
//                Double sigmaZ = getSigmaZ(list, basic.getMean(),ctrlInfVo.getF_CHART_TYPE());
//                ctrlInfVo.setF_SP(sigmaZ);
//            }
        }
    }
    /**
     * 计算sigmaZ 如果子组数量小于2，返回1
     * LaneyU在计算Z值时，分母为SQRT(P/N)
     * @param list 子组数据
     * @param mean 均值
     * @return sigmaZ
     */
    private Double getSigmaZ(List<SubgroupDataDTO> list,Double mean,Integer chartType) {

        if((long) list.size() <2){
            return 1d;
        }
        List<Double> zValues = new ArrayList<>();
        /*计算每个子组的缺陷或者不良率*/
        list.forEach(subgroupDataDTO -> {
            SGRP_VAL_CHILD_DTO sgrpChild= subgroupDataDTO.getSgrpValDtoList().get(0).getSgrpValChildDto();
            if(sgrpChild!=null){
                AtomicReference<Double> sum= new AtomicReference<>(0d);
                sgrpChild.getTestList().forEach( test -> {
                    sum.updateAndGet(v -> v + test.getTestVal());
                });
                /*单个子组的不良率或缺陷率*/
                Double p = sum.get()/subgroupDataDTO.getF_SGSZ();
                Double z =0d;
                if(chartType==14) {
                    /*LaneyP图SigmaZ值计算*/
                   z= (p-mean)/Math.sqrt(mean*(1-mean)/subgroupDataDTO.getF_SGSZ());
                }
                else  {
                    /*LaneyU图Sigma值计算*/
                    z= (p-mean)/Math.sqrt(mean/subgroupDataDTO.getF_SGSZ());
                }
                zValues.add(z);
            }
            });
        double movingRangeZSum=0d;
        for(int i=0;i<zValues.size()-1;i++){
            movingRangeZSum +=Math.abs(zValues.get(i)-zValues.get(i+1));
        }

        return (movingRangeZSum/(zValues.size()-1))/1.128;
    }
    /**
     * 根据范围查询历史数据并计算控制限
     * @param parameterSetInfDto 数据查询条件
     * @return 计算出的控制限集合
     */
    @Override
    public List<CTRL_INF_VO> batchCalculateCtrlLimit(PARAMETER_SET_INF_DTO parameterSetInfDto) {
        if (CollectionUtils.isEmpty(parameterSetInfDto.getParameterChildDtoList()) || parameterSetInfDto.getParameterChildDtoList().size() != 3) {
            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
        }
        /*解析所选数据*/
        Map<String, List<Long>> searchCondition = parameterSetInfService.getSearchCondition(parameterSetInfDto.getParameterChildDtoList());
        parameterSetInfDto.setMap(searchCondition);
        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
        subgroupDataSelectionDTO.setPartList(searchCondition.get(Constants.partList));
        subgroupDataSelectionDTO.setPrcsList(searchCondition.get(Constants.prcsList));
        subgroupDataSelectionDTO.setTestList(searchCondition.get(Constants.testList));
        subgroupDataSelectionDTO.setDbType(parameterSetInfDto.getDbType());
        subgroupDataSelectionDTO = SubgroupDataSelectionDTO.getData(subgroupDataSelectionDTO, parameterSetInfDto);
        List<CTRL_INF_VO> ctrlInfVoList = new ArrayList<>();
        List<SubgroupDataDTO> list = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
        if (CollectionUtils.isEmpty(list)) return ctrlInfVoList;
        List<SubgroupDataDTO> reassembly = chartCommonService.reassembly(list);

        ctrlGenerate1(ctrlInfVoList, reassembly);
        return ctrlInfVoList;
    }

    private void ctrlGenerate1(List<CTRL_INF_VO> list, List<SubgroupDataDTO> reassembly) {
        /*将part,prcs,test作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = reassembly.stream()
                .collect(Collectors.groupingBy(s ->
                                s.getF_PART() + Constants.COMMA +
                                        s.getF_REV() + Constants.COMMA +
                                        s.getF_PRCS() + Constants.COMMA +
                                        s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));
        map.forEach((k, v) -> {
            /*暂时只支持变量测试，缺陷和不良测试在绑定标准化模板的逻辑还需要考虑*/
            if(CollectionUtils.isEmpty(v) || v.get(0).getTestType()>1) return;
            CTRL_INF_VO ctrl_inf_vo = new CTRL_INF_VO();
            ctrl_inf_vo.setF_PART(v.get(0).getF_PART());
            ctrl_inf_vo.setF_PTRV(v.get(0).getF_REV());
            ctrl_inf_vo.setF_PRCS(v.get(0).getF_PRCS());
            ctrl_inf_vo.setF_TEST(v.get(0).getF_TEST());
            ctrl_inf_vo.setPartName(v.get(0).getPartName());
            ctrl_inf_vo.setPrcsName(v.get(0).getPrcsName());
            ctrl_inf_vo.setTestName(v.get(0).getTestName());
            ctrl_inf_vo.setPtrvName(v.get(0).getPtrvName());
            //查询是否有历史控制限
            CTRL_INF_DTO ctrlInfDto = queryHistoricalChartType(ctrl_inf_vo);
            if(ctrlInfDto!=null){
                /*如果存在历史控制限，计算出来的控制限使用历史控制限的图表类型和处理模板*/
                ctrl_inf_vo.setHasHistoricalLimit(1);
                ctrl_inf_vo.setF_CHART_TYPE(ctrlInfDto.getF_CHART_TYPE());
                ctrl_inf_vo.setF_PSTP(ctrlInfDto.getF_PSTP());
            }
            else {
                ctrl_inf_vo.setF_CHART_TYPE(ControlChartTypeEnum.getType(Double.parseDouble(v.get(0).getF_SGSZ().toString()),v.get(0).getSgrpValDto().getF_SBSZ()==null?0:v.get(0).getSgrpValDto().getF_SBSZ(),v.get(0).getTestType()));
            }
            calculateControlLimit(ctrl_inf_vo, v);
            list.add(ctrl_inf_vo);
        });
    }

    /**
     * 批量插入计算出的控制限至数据库
     * @param ctrlInfVoList 计算出的控制限集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<CTRL_INF_VO> ctrlInfVoList) {
        List<CTRL_INF> list = new ArrayList<>();
        ctrlInfVoList.forEach(ctrlInfVo -> {
            checkParam(ctrlInfVo);
            CTRL_INF ctrlInf = new CTRL_INF();
            BeanUtils.copyPropertiesIgnoreNull(ctrlInfVo, ctrlInf);
            list.add(ctrlInf);
        });
        this.saveBatch(list);
        threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
            try {
            for (CTRL_INF ctrlInf : list) {
                final CTRL_INF_DTO dto = new CTRL_INF_DTO();
                BeanUtil.copyProperties(ctrlInf, dto);
                addCache(dto);
            }
            } catch (Exception e) {
                log.error("redis添加控制限报错----->{}", e);
                e.printStackTrace();
            }
        });

    }

    private void extracted(CTRL_INF ctrlInf) {
        try {
            /*先删除*/
            String key = RedisConstant.CTRL_KEY +
                    ctrlInf.getF_PART() + Constants.COMMA +
                    ctrlInf.getF_PTRV() + Constants.COMMA +
                    ctrlInf.getF_PRCS() + Constants.COMMA +
                    ctrlInf.getF_TEST();
            redisService.deleteObject(key);
            CTRL_INF_VO ctrlInfVo = new CTRL_INF_VO();
            ctrlInfVo.setF_PART(ctrlInf.getF_PART());
            ctrlInfVo.setF_PRCS(ctrlInf.getF_PRCS());
            ctrlInfVo.setF_TEST(ctrlInf.getF_TEST());
            ctrlInfVo.setNext(Constants.NEXT);
            ctrlInfVo.setDbType(InitConfig.getDriverType());
            List<CTRL_INF_DTO> list = baseMapper.getList(ctrlInfVo);
            if (CollectionUtils.isNotEmpty(list)) {
                redisService.setCacheList(key, list, null);
            }
        } catch (Exception e) {
            log.error("redis添加控制限报错----->{}", e);
            e.printStackTrace();
        }
    }

    /**
     * 根据产品、过程、测试和版本查询历史图表类型
     *
     * @param ctrlInfVo 查询参数
     * @return 图表类型
     */
    @Override
    public CTRL_INF_DTO queryHistoricalChartType(CTRL_INF_VO ctrlInfVo) {
        LambdaQueryWrapper<CTRL_INF> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CTRL_INF::getF_PART, ctrlInfVo.getF_PART())
                .eq(CTRL_INF::getF_PRCS, ctrlInfVo.getF_PRCS())
                .eq(CTRL_INF::getF_TEST, ctrlInfVo.getF_TEST())
                .eq(CTRL_INF::getF_PTRV, ctrlInfVo.getF_PTRV())
                .eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType());
        List<CTRL_INF> ctrlInfList = baseMapper.selectList(queryWrapper);
        if (ctrlInfList.isEmpty()) {
            return null;
        }
        // 假设历史图表类型都是一致的，取第一条记录
        CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
        BeanUtils.copyPropertiesIgnoreNull(ctrlInfList.get(0),ctrlInfDto);
        return ctrlInfDto;
    }

    @Override
    public List<CTRL_INF> getCondition(List<Long> ids, ParetoAnalyseTypeEnum paretoAnalyseTypeEnum) {
        LambdaQueryWrapper<CTRL_INF> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<CTRL_INF> list = new ArrayList<>();
        switch (paretoAnalyseTypeEnum) {
            case PART_DAT:
                lambdaQueryWrapper.in(CTRL_INF::getF_PART, ids).eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case PRCS_DAT:
                lambdaQueryWrapper.in(CTRL_INF::getF_PRCS, ids).eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case TEST_DAT:
                lambdaQueryWrapper.in(CTRL_INF::getF_TEST, ids).eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
            case PTRV_DAT:
                lambdaQueryWrapper.in(CTRL_INF::getF_PTRV, ids).eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType());
                list = baseMapper.selectList(lambdaQueryWrapper);
                break;
        }
        return list;
    }

//    @Override
//    public List<ControlChartDTO> batchCalculateControlLimit(PARAMETER_SET_INF_DTO parameterSetInfDto) {
//        if (CollectionUtils.isEmpty(parameterSetInfDto.getParameterChildDtoList()) || parameterSetInfDto.getParameterChildDtoList().size() != 3) {
//            throw new BusinessException(CommonExceptionEnum.PARAMETER_MISSING_EXCEPTION);
//        }
//        /*解析所选数据*/
//        Map<String, List<Long>> searchCondition = parameterSetInfService.getSearchCondition(parameterSetInfDto.getParameterChildDtoList());
//        parameterSetInfDto.setMap(searchCondition);
//        SubgroupDataSelectionDTO subgroupDataSelectionDTO = new SubgroupDataSelectionDTO();
//        subgroupDataSelectionDTO.setPartList(searchCondition.get(Constants.partList));
//        subgroupDataSelectionDTO.setPrcsList(searchCondition.get(Constants.prcsList));
//        subgroupDataSelectionDTO.setTestList(searchCondition.get(Constants.testList));
//        subgroupDataSelectionDTO.setDbType(parameterSetInfDto.getDbType());
//        subgroupDataSelectionDTO = SubgroupDataSelectionDTO.getData(subgroupDataSelectionDTO, parameterSetInfDto);
//        List<ControlChartDTO> controlChartDTOList = new ArrayList<>();
//        List<SubgroupDataDTO> list = sgrpInfService.getSubgroupDataDTOList(subgroupDataSelectionDTO);
//        if (CollectionUtils.isEmpty(list)) return controlChartDTOList;
//        List<SubgroupDataDTO> reassembly = chartCommonService.reassembly(list);
//
//        ctrlGenerate(controlChartDTOList, reassembly);
//        return controlChartDTOList;
//    }

    /**
     * 根据条件生成控制限
     * 缺陷类型默认使用C图，不良类型默认使用U图
     * @param list
     * @param reassembly
     */
    private void ctrlGenerate(List<ControlChartDTO> list, List<SubgroupDataDTO> reassembly) {
        /*将part,prcs,test作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = reassembly.stream()
                .collect(Collectors.groupingBy(s ->
                                s.getF_PART() + Constants.COMMA +
                                        s.getF_REV() + Constants.COMMA +
                                        s.getF_PRCS() + Constants.COMMA +
                                        s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));
        map.forEach((k, v) -> {
            ControlChartDTO controlChartDTO = new ControlChartDTO();
            controlChartDTO.setSubgroupDataDTO(v.get(0));
            /*计算不同子组的控制限*/
            DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(v, 1, 0);
            /*获取公差限数据*/
            specInfService.getSpecLim(dataSummaryDTO, controlChartDTO.getSubgroupDataDTO());
            controlChartDTO.setDataSummary(dataSummaryDTO);
            /*判断测试是否为缺陷或者不良*/
            TEST_INF testInf = testInfService.getById(controlChartDTO.getSubgroupDataDTO().getSgrpValDtoList().get(0).getF_TEST());
            controlChartDTO.setType(ControlChartTypeEnum.getType(dataSummaryDTO.getSubGroupSize(),
                    dataSummaryDTO.getSubTestNum(), testInf.getF_TYPE()));
            /*测试类型是不良或者缺陷的时候，需要重新计算缺陷率或者不良率作为均值*/
            if(testInf.getF_TYPE().equals(2)||testInf.getF_TYPE().equals(3)){
                DataSummaryDTO.recalculateMean(dataSummaryDTO,v);

            }
            /*件内西格玛计算*/
            ControlChartTypeEnum chartTypeEnum = ControlChartTypeEnum.getEnumByType(ControlChartTypeEnum.getType(dataSummaryDTO.getSubGroupSize(),
                    dataSummaryDTO.getSubTestNum(), testInf.getF_TYPE()));
            if (chartTypeEnum != null && (chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_IX_MR_RW) ||
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_IX_MR_SDW) ||
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_R_SDW) ||
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_R_RW) ||
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_SD_RW) ||
                    chartTypeEnum.equals(ControlChartTypeEnum.CONTROL_CHART_X_SD_SDW))) {
                List<SGRP_VAL_DTO> sgrpValDtoList = v.stream().map(SubgroupDataDTO::getSgrpValDto).collect(Collectors.toList());
                Integer subTestNum = sgrpValDtoList.stream().map(SGRP_VAL_DTO::getF_SBSZ).reduce(Integer::sum).orElse(1);
                double subTestSize = BigDecimal.valueOf(subTestNum).divide(BigDecimal.valueOf(dataSummaryDTO.getSubGroupNum()), 4, RoundingMode.DOWN).doubleValue();
                controlChartDTO.setF_SW(HistogramUtil.getWithingShortTermStandardDeviation(subTestSize, sgrpValDtoList, ShortSdTermTypeEnum.AUTO.getType()));
            }

            list.add(controlChartDTO);
        });
    }


    /**
     * 查询最新生效日期的数据
      */
    @Override
    public List<CTRL_INF_DTO> getCtrlInfo(CTRL_INF_VO ctrlInf) {
        // 1. 生成基础Key
        String baseKey = RedisConstant.CTRL_KEY +
                ctrlInf.getF_PART() + Constants.COMMA +
                ctrlInf.getF_PTRV() + Constants.COMMA +
                ctrlInf.getF_PRCS() + Constants.COMMA +
                ctrlInf.getF_TEST();
        // 2. 查询生效期内的所有数据
        List<CTRL_INF_DTO> latestFromZSet = redisService.getByEffectiveDateLessOrEqual(baseKey,new Date());
        if (ObjectUtils.isEmpty(latestFromZSet)) {
            LambdaQueryWrapper<CTRL_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CTRL_INF::getF_PART, ctrlInf.getF_PART())
                    .eq(CTRL_INF::getF_PTRV, ctrlInf.getF_PTRV())
                    .eq(CTRL_INF::getF_PRCS, ctrlInf.getF_PRCS())
                    .eq(CTRL_INF::getF_TEST, ctrlInf.getF_TEST())
                    .eq(CTRL_INF::getF_DEL, DelFlagEnum.USE.getType())
                    // 添加生效日期小于当前时间的条件
                    .lt(CTRL_INF::getF_EFTM, ctrlInf.getF_EFTM())
                    // 按生效日期降序排序（最近的排在前面）
                    .orderByDesc(CTRL_INF::getF_EFTM);
            List<CTRL_INF> ctrlInfList = baseMapper.selectList(queryWrapper);
            if(CollectionUtils.isNotEmpty(ctrlInfList)){
                List<CTRL_INF_DTO> list =  new ArrayList<>();
                for (CTRL_INF ctrl : ctrlInfList) {
                    final CTRL_INF_DTO ctrlInfDto = new CTRL_INF_DTO();
                    BeanUtils.copyPropertiesIgnoreNull(ctrl, ctrlInfDto);
                    list.add(ctrlInfDto);
                    addCache(ctrlInfDto);
                }
                return list;
            }
        }
        return latestFromZSet;
    }

    public void addCache(CTRL_INF_DTO ctrlInf) {
        // 生成基础缓存键
        String baseKey = RedisConstant.CTRL_KEY +
                ctrlInf.getF_PART() + Constants.COMMA +
                ctrlInf.getF_PTRV() + Constants.COMMA +
                ctrlInf.getF_PRCS() + Constants.COMMA +
                ctrlInf.getF_TEST();

        // 获取生效日期的时间戳作为分数
        long effectiveTime = ctrlInf.getF_EFTM().getTime();
        // 调用RedisService缓存数据
        redisService.addToZSet(baseKey, ctrlInf, effectiveTime, -1, TimeUnit.HOURS);
    }

    private void delCache(CTRL_INF_DTO inf) {
        String baseKey = RedisConstant.CTRL_KEY +
                inf.getF_PART() + Constants.COMMA +
                inf.getF_PTRV() + Constants.COMMA +
                inf.getF_PRCS() + Constants.COMMA +
                inf.getF_TEST();
        redisService.deleteByKeyAndEffectiveDate(baseKey,inf.getF_EFTM());
    }
}




