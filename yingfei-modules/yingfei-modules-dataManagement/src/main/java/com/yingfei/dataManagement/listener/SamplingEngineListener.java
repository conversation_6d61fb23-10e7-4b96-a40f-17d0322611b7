//package com.yingfei.dataManagement.listener;
//
//import com.yingfei.common.redis.service.RedisCache;
//import com.yingfei.dataCollection.service.SAMPLING_TASK_CONFIGService;
//import com.yingfei.dataManagement.service.SGRP_INFService;
//import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
//import com.yingfei.entity.domain.SGRP_INF;
//import com.yingfei.mq.config.InspectionMqConfig;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//import java.util.stream.Collectors;
//
//@Component
//@Slf4j
//public class SamplingEngineListener {
//
//    @Resource
//    private SAMPLING_TASK_CONFIGService samplingTaskConfigService;
//
//    @Resource
//    private SGRP_INFService sgrpInfService;
//
//    @Resource
//    private RedisCache redisCache;
//
//    @Resource
//    private RabbitTemplate rabbitTemplate;
//
//    private static final String INSPECTION_CACHE_KEY = "inspection:cache:";
//
//    @RabbitListener(queues = InspectionMqConfig.QUEUE_INSPECTION_SAMPLING_TRIGGER)
//    public void onMessage(Long samplingTaskConfigId) {
//        log.info("接收到抽样触发消息，配置ID: {}", samplingTaskConfigId);
//
//        // 1. 获取抽样配置
//        SAMPLING_TASK_CONFIG config = samplingTaskConfigService.getById(samplingTaskConfigId);
//        if (config == null) {
//            log.error("无法找到ID为 {} 的抽样配置", samplingTaskConfigId);
//            return;
//        }
//
//        // 2. 从Redis读取缓存数据
//        String cacheKey = INSPECTION_CACHE_KEY + samplingTaskConfigId;
//        List<Map<String, Object>> cachedData = redisCache.getCacheList(cacheKey);
//        if (CollectionUtils.isEmpty(cachedData)) {
//            log.warn("在Redis中找不到配置ID {} 的缓存数据", samplingTaskConfigId);
//            return;
//        }
//
//        // 3. 执行抽样
//        List<Map<String, Object>> sampledData = doSample(cachedData, config);
//
//        // 4. 生成子组数据
//        SGRP_INF newSgrp = sgrpInfService.createSubgroupData(sampledData, config);
//
//        // 5. 清理缓存
//        redisCache.deleteObject(cacheKey);
//        log.info("配置ID {} 的抽样任务完成，已清理缓存", samplingTaskConfigId);
//
//        // 6. 发送子组生成通知
//        rabbitTemplate.convertAndSend(
//            InspectionMqConfig.EXCHANGE_INSPECTION,
//            InspectionMqConfig.ROUTING_KEY_INSPECTION_SUBGROUP_CREATED,
//            newSgrp
//        );
//        log.info("子组 {} 已生成，发送通知", newSgrp.getF_SGRP());
//    }
//
//    private List<Map<String, Object>> doSample(List<Map<String, Object>> data, SAMPLING_TASK_CONFIG config) {
//        Integer sampleCount = config.getF_SAMPLING_COUNT();
//        if (sampleCount == null || sampleCount <= 0 || sampleCount >= data.size()) {
//            return data; // 如果样本数无效或大于等于总数，则返回所有数据
//        }
//
//        // TODO: 根据 config.getF_SAMPLING_METHOD() 实现不同的抽样方式
//        // 这里暂时只实现简单的随机抽样
//        Collections.shuffle(data, new Random());
//        return data.stream().limit(sampleCount).collect(Collectors.toList());
//    }
//}
//
