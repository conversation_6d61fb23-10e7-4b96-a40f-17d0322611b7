package com.yingfei.dataManagement.listener;

import com.yingfei.dataManagement.scheduler.CamundaTaskTimeoutReminder;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Camunda任务完成监听器
 * 监听任务完成事件，清理任务提醒状态缓存
 */
@Slf4j
@Component
public class CamundaTaskCompletionListener implements TaskListener {

    @Resource
    private CamundaTaskTimeoutReminder camundaTaskTimeoutReminder;

    /**
     * 处理任务完成事件
     * 当任务完成时，清理该任务的提醒状态缓存
     */
    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            String taskId = delegateTask.getId();
            String taskName = delegateTask.getName();
            String assignee = delegateTask.getAssignee();
            String processInstanceId = delegateTask.getProcessInstanceId();

            log.info("任务已完成，准备清理提醒状态缓存: 任务ID={}, 任务名称={}, 处理人={}, 流程实例ID={}",
                    taskId, taskName, assignee, processInstanceId);

            // 调用CamundaTaskTimeoutReminder的clearTaskReminderStatus方法清理缓存
            camundaTaskTimeoutReminder.clearTaskReminderStatus(taskId);
        } catch (Exception e) {
            log.error("处理任务完成事件异常，任务ID: {}", delegateTask.getId(), e);
        }
    }
}
