package com.yingfei.dataManagement.scheduler;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.dataManagement.constant.WorkflowTimeoutConstants;
import com.yingfei.dataManagement.service.bpm.BpmTaskService;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.dto.msg.SendMessageDTO;
import com.yingfei.system.api.RemoteSendService;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Camunda任务超时提醒器
 * 定期检查任务是否超时，并按照层级发送提醒消息
 */
@Slf4j
@Component
public class CamundaTaskTimeoutReminder {

    @Resource
    private TaskService taskService;

    @Resource
    private BpmTaskService bpmTaskService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private RemoteSendService remoteSendService;

    // 使用常量类中定义的默认超时配置
    private static final int DEFAULT_FIRST_REMINDER_TIMEOUT = WorkflowTimeoutConstants.DEFAULT_FIRST_REMINDER_TIMEOUT;
    private static final int DEFAULT_SECOND_REMINDER_TIMEOUT = WorkflowTimeoutConstants.DEFAULT_SECOND_REMINDER_TIMEOUT;
    private static final int DEFAULT_THIRD_REMINDER_TIMEOUT = WorkflowTimeoutConstants.DEFAULT_THIRD_REMINDER_TIMEOUT;

    // 存储任务的提醒状态，避免重复提醒
    private final Map<String, TaskReminderStatus> taskReminderStatusMap = new ConcurrentHashMap<>();

    /**
     * 每10分钟检查一次任务超时情况
     */
//    @Scheduled(cron = "0 */10 * * * ?")
    public void checkTaskTimeout() {
        log.info("开始检查流程任务超时情况");
        try {
            // 获取所有未完成的任务
            List<Task> tasks = taskService.createTaskQuery().active().list();
            log.info("共检查到 {} 个未完成的任务", tasks.size());

            for (Task task : tasks) {
                checkAndSendReminder(task);
            }
        } catch (Exception e) {
            log.error("检查任务超时异常", e);
        }
    }
    /**
     * 清理指定任务的提醒状态缓存
     * 当任务完成时调用此方法
     */
    public void clearTaskReminderStatus(String taskId) {
        if (taskId != null && !taskId.isEmpty()) {
            taskReminderStatusMap.remove(taskId);
            log.info("清理任务提醒状态缓存: 任务ID={}", taskId);
        }
    }

    /**
     * 清理所有任务的提醒状态缓存
     * 用于系统重启或缓存过期时
     */
    public void clearAllTaskReminderStatus() {
        taskReminderStatusMap.clear();
        log.info("清理所有任务提醒状态缓存");
    }


    /**
     * 检查任务是否超时并发送提醒
     */
    private void checkAndSendReminder(Task task) {
        try {
            String taskId = task.getId();
            Date createTime = task.getCreateTime();
            long timeElapsed = DateUtil.between(createTime, new Date(), DateUnit.MINUTE);
            String assignee = task.getAssignee();
            String taskName = task.getName();
            String processInstanceId = task.getProcessInstanceId();
            String taskDefinitionKey = task.getTaskDefinitionKey();

            // 如果没有指派人，则跳过
            if (StringUtils.isEmpty(assignee)) {
                log.debug("任务 {} 未指派处理人，跳过提醒", taskId);
                return;
            }

            // 获取该任务节点的超时配置
            WorkflowTimeoutConstants.NodeTimeoutConfig timeoutConfig =
                    WorkflowTimeoutConstants.getNodeTimeoutConfig(taskDefinitionKey);
            int firstReminderTimeout = timeoutConfig.getFirstReminderTimeout();
            int secondReminderTimeout = timeoutConfig.getSecondReminderTimeout();
            int thirdReminderTimeout = timeoutConfig.getThirdReminderTimeout();

            TaskReminderStatus status = taskReminderStatusMap.getOrDefault(taskId, new TaskReminderStatus());

            // 检查是否超过第一次提醒时间
            if (timeElapsed >= firstReminderTimeout && !status.isFirstReminded()) {
                sendReminderToUser(Long.valueOf(assignee), taskName, processInstanceId,
                        String.format("任务已超过设置时长(%d分钟)，请尽快处理", firstReminderTimeout));
                status.setFirstReminded(true);
                taskReminderStatusMap.put(taskId, status);
                log.info("发送第一次提醒给用户 {} 任务: {} 节点: {}", assignee, taskName, taskDefinitionKey);
            }
            // 检查是否超过第二次提醒时间
            else if (timeElapsed >= secondReminderTimeout && !status.isSecondReminded()) {
                // 发送给用户本人
                sendReminderToUser(Long.valueOf(assignee), taskName, processInstanceId,
                        String.format("任务已严重超时(%d分钟)，请立即处理", secondReminderTimeout));
                // 发送给上级领导
                Long superiorId = getSuperiorUserId(Long.valueOf(assignee));
                if (superiorId != null) {
                    sendReminderToUser(superiorId, taskName, processInstanceId,
                            String.format("您的下属有任务严重超时(%d分钟)，请督促其尽快处理", secondReminderTimeout));
                    log.info("发送第二次提醒给上级领导 {} 用户: {} 任务: {} 节点: {}",
                            superiorId, assignee, taskName, taskDefinitionKey);
                }
                status.setSecondReminded(true);
                taskReminderStatusMap.put(taskId, status);
            }
            // 检查是否超过第三次提醒时间
            else if (timeElapsed >= thirdReminderTimeout && !status.isThirdReminded()) {
                // 发送给用户本人
                sendReminderToUser(Long.valueOf(assignee), taskName, processInstanceId,
                        String.format("任务已超长时间未处理(%d分钟)，请立即处理", thirdReminderTimeout));
                // 发送给上级领导
                Long superiorId = getSuperiorUserId(Long.valueOf(assignee));
                if (superiorId != null) {
                    sendReminderToUser(superiorId, taskName, processInstanceId,
                            String.format("您的下属任务超长时间未处理(%d分钟)，请立即督促处理", thirdReminderTimeout));
                    // 发送给上级的上级
                    Long grandSuperiorId = getSuperiorUserId(superiorId);
                    if (grandSuperiorId != null) {
                        sendReminderToUser(grandSuperiorId, taskName, processInstanceId,
                                String.format("您的下属部门有任务超长时间未处理(%d分钟)，请关注", thirdReminderTimeout));
                        log.info("发送第三次提醒给领导的领导 {} 上级: {} 用户: {} 任务: {} 节点: {}",
                                grandSuperiorId, superiorId, assignee, taskName, taskDefinitionKey);
                    }
                }
                status.setThirdReminded(true);
                taskReminderStatusMap.put(taskId, status);
            }
        } catch (Exception e) {
            log.error("处理任务超时提醒异常，任务ID: {}", task.getId(), e);
        }
    }

    /**
     * 获取用户的上级领导ID
     * 根据层级关系推导上级领导
     */
    private Long getSuperiorUserId(Long userId) {
        try {
            // 获取用户信息
            EMPL_INF_DTO userInfo = remoteUserService.info(userId).getData();
            if (userInfo == null) {
                log.error("获取用户信息失败，用户ID: {}", userId);
                return null;
            }

            // 获取用户所在层级
            Long hierId = userInfo.getF_HIER();
            if (hierId == null) {
                log.warn("用户未分配层级，用户ID: {}", userId);
                return null;
            }

            // 获取层级信息
            HIERARCHY_INF_DTO hierarchyInfo = remoteUserService.findByHierId(hierId).getData();
            if (hierarchyInfo == null) {
                log.error("获取层级信息失败，层级ID: {}", hierId);
                return null;
            }

            // 获取父层级ID
            Long parentHierId = hierarchyInfo.getF_PARENT();
            if (parentHierId == null) {
                log.warn("该层级没有上级层级，层级ID: {}", hierId);
                return null;
            }

            // 这里简化处理，实际应该根据公司的组织架构和角色关系来确定上级领导
            // 这里假设父层级中的用户都是上级领导，取第一个作为上级
            final HIERARCHY_INF_DTO parentLevelUsers = remoteUserService.findByHierId(parentHierId).getData();
            if (ObjectUtils.isNotEmpty(parentLevelUsers)) {
                // 通常应该选择父层级中职位最高或最适合的用户作为上级
                // 这里简化处理，返回第一个用户
                return parentLevelUsers.getF_CRUE();
            }
        } catch (Exception e) {
            log.error("获取上级领导失败，用户ID: {}", userId, e);
        }
        return null;
    }

    /**
     * 发送提醒消息给用户
     */
    private void sendReminderToUser(Long userId, String taskName, String processInstanceId, String message) {
        try {
            // 获取用户信息以确定消息发送方式
            EMPL_INF_DTO userInfo = remoteUserService.info(userId).getData();
            if (userInfo == null) {
                log.error("获取用户信息失败，用户ID: {}", userId);
                return;
            }

            // 组装消息内容
            String content = String.format("流程任务提醒：\n任务名称：%s\n流程实例ID：%s\n提醒内容：%s\n时间：%s",
                    taskName, processInstanceId, message, DateUtil.now());

            final SendMessageDTO sendMessageDTO = new SendMessageDTO();
            sendMessageDTO.setMsgType(Collections.singletonList(1));
            sendMessageDTO.setUserIds(Collections.singletonList(userId));
            sendMessageDTO.setRoleIds(Collections.emptyList());
            sendMessageDTO.setTitle("流程任务超时提醒");
            sendMessageDTO.setContent(content);


            // 发送消息（根据用户配置的通知方式选择合适的发送方法）
            // 这里简化处理，统一使用sendMsg方法
            remoteSendService.sendMsg(sendMessageDTO);
            log.info("发送提醒消息成功，用户ID: {}, 任务: {}", userId, taskName);
        } catch (Exception e) {
            log.error("发送提醒消息失败，用户ID: {}", userId, e);
        }
    }

    /**
     * 任务提醒状态类
     * 用于记录任务的提醒状态
     */
    private static class TaskReminderStatus {
        private boolean firstReminded = false;
        private boolean secondReminded = false;
        private boolean thirdReminded = false;

        public boolean isFirstReminded() {
            return firstReminded;
        }

        public void setFirstReminded(boolean firstReminded) {
            this.firstReminded = firstReminded;
        }

        public boolean isSecondReminded() {
            return secondReminded;
        }

        public void setSecondReminded(boolean secondReminded) {
            this.secondReminded = secondReminded;
        }

        public boolean isThirdReminded() {
            return thirdReminded;
        }

        public void setThirdReminded(boolean thirdReminded) {
            this.thirdReminded = thirdReminded;
        }
    }
}
