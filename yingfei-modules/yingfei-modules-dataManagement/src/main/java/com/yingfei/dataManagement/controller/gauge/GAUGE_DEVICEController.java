package com.yingfei.dataManagement.controller.gauge;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.gauge.GAUGE_DEVICEService;
import com.yingfei.entity.domain.GAUGE_DEVICE;
import com.yingfei.entity.dto.GAUGE_DEVICE_DTO;
import com.yingfei.entity.vo.GAUGE_DEVICE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "量具设备配置信息API")
@RestController
@RequestMapping("/gauge_device")
public class GAUGE_DEVICEController extends BaseController {

    @Resource
    private GAUGE_DEVICEService gaugeDeviceService;

    /**
     * 获取量具设备配置信息列表
     */
    @ApiOperation("获取量具设备配置信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody GAUGE_DEVICE_VO gaugeDeviceVo) {
        List<GAUGE_DEVICE_DTO> list = gaugeDeviceService.getList(gaugeDeviceVo);
        return getDataTable(list,gaugeDeviceService.getTotal(gaugeDeviceVo));
    }

    @ApiOperation("获取量具设备配置信息")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable Long id) {
        GAUGE_DEVICE_DTO gaugeDeviceDto = gaugeDeviceService.info(id);
        return R.ok(gaugeDeviceDto);
    }

    /**
     * 新增量具设备配置信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "量具设备配置管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增量具设备配置信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody GAUGE_DEVICE_VO gaugeDeviceVo) {
        gaugeDeviceService.checkParam(gaugeDeviceVo);
        gaugeDeviceService.add(gaugeDeviceVo);
        return R.ok();
    }

    /**
     * 修改量具设备配置信息
     */
    @Log(title = "量具设备配置管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改量具设备配置信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody GAUGE_DEVICE_VO gaugeDeviceVo) {
        gaugeDeviceService.checkParam(gaugeDeviceVo);
        GAUGE_DEVICE gaugeDevice = new GAUGE_DEVICE();
        BeanUtils.copyPropertiesIgnoreNull(gaugeDeviceVo, gaugeDevice);
        if (gaugeDeviceVo.getGaugeDeviceConfigDto() != null) {
            gaugeDevice.setF_CONFIG(JSONObject.toJSONString(gaugeDeviceVo.getGaugeDeviceConfigDto()));
        }
        gaugeDeviceService.updateById(gaugeDevice);
        return R.ok();
    }

    /**
     * 批量删除量具设备配置信息
     */
    @Log(title = "量具设备配置管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除量具设备配置信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        gaugeDeviceService.del(ids);
        return R.ok();
    }
}
