package com.yingfei.dataManagement.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.LanguageEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.*;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.IdGeneratorService;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.mapper.AUTH_INFMapper;
import com.yingfei.dataManagement.service.CTRL_INFService;
import com.yingfei.dataManagement.service.DICT_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.entity.domain.AUTH_INF;
import com.yingfei.entity.domain.DICT_INF;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.enums.DbLinkEnum;
import com.yingfei.entity.enums.ProductTypeEnum;
import com.yingfei.entity.vo.CTRL_INF_VO;
import com.yingfei.entity.vo.SPEC_INF_VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 项目初始化配置
 */
@Slf4j
@Configuration
public class InitConfig {

    @Resource
    private RedisService redisService;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private CTRL_INFService ctrlInfService;
    @Resource
    private DICT_INFService dictInfService;
    @Resource
    private AUTH_INFMapper authInfMapper;
    @Resource
    private IdGeneratorService idGeneratorService;
    @Value("${spring.datasource.druid.driver-class-name}")
    private String driverClassName;

    private static String dbName;

    public static Boolean AUTH_FLAG = false;

    @Value("${spring.datasource.druid.driver-class-name}")
    public void setDriverClassName(String driverClassName) {
        InitConfig.dbName = driverClassName;
    }

    @PostConstruct
    public void initAuth() {
        Boolean flag = checkAuth();
        if (flag) {
            AUTH_FLAG = true;
            dbName = driverClassName;
            log.info("授权校验成功！");
        } else {
            log.error("授权校验失败，请联系管理人员！");
            String os = CPUUtils.getOSName();
            String hardwareID;
            if (CPUUtils.LINUX_NAME.startsWith(os)) {
                hardwareID = HardwareIDUtils.getLinuxHardwareID();
                /*如果是用windows机器搭建的服务器需要用Windows方式读取*/
                if (hardwareID != null && hardwareID.equalsIgnoreCase("System Serial Number")) {
                    hardwareID = HardwareIDUtils.getWindowsHardwareID();
                }
            } else {
                //wondows系统
                hardwareID = HardwareIDUtils.getWindowsHardwareID();
            }

            if (hardwareID == null) {
                log.error("授权校验失败，机器编号不存在");
                throw new RuntimeException("AUTH_FAIL_NO_HARDWARE_ID");
            }

            if (!hardwareID.equals("BFEBFBFF000306C3") && !hardwareID.equals("BFEBFBFF00090672")) {
                log.error("权校验失败，系统服务已停止!该机器编号为:-------->" + hardwareID);
                throw new RuntimeException("AUTH_FAIL_NO_SERVICE");
            }
        }
    }

    /**
     * 全局配置初始化
     * GlobalConfiguration
     */
    @PostConstruct
    public void init() {
//        /*初始化产品版本号*/
//        Object part_rev_name = redisService.get(RedisConstant.PART_REV_NAME);
//        if (part_rev_name == null) {
//            redisService.set(RedisConstant.PART_REV_NAME, Constants.PREV_VERSION);
//        }

        /*初始化默认报警限规则*/
        Object default_alarm = redisService.get(RedisConstant.WARNING_LIMIT);
        if (default_alarm == null) {
            redisService.set(RedisConstant.WARNING_LIMIT, Constants.WARNING_LIMIT);
        }

        /*初始化默认合理限规则*/
        Object default_rational = redisService.get(RedisConstant.REASONABLE_LIMIT);
        if (default_rational == null) {
            redisService.set(RedisConstant.REASONABLE_LIMIT, Constants.REASONABLE_LIMIT);
        }

//        /*初始化缓存子组最大天数*/
//        Object sub_group_cache_max_day = redisService.get(RedisConstant.SUB_GROUP_CACHE_MAX_DAY);
//        if (sub_group_cache_max_day == null) {
//            redisService.set(RedisConstant.SUB_GROUP_CACHE_MAX_DAY, Constants.SUB_GROUP_CACHE_MAX_DAY);
//        }

//        /*初始化消息日志保存天数*/
//        Object msg_log_save_day = redisService.get(RedisConstant.MSG_LOG_SAVE_DAY);
//        if (msg_log_save_day == null) {
//            redisService.set(RedisConstant.MSG_LOG_SAVE_DAY, Constants.MSG_LOG_SAVE_DAY);
//        }

//        /*初始化数据监控缓存保存天数*/
//        Object monitor_save_day = redisService.get(RedisConstant.MONITOR_SAVE_DAY);
//        if (monitor_save_day == null) {
//            redisService.set(RedisConstant.MONITOR_SAVE_DAY, Constants.MONITOR_SAVE_DAY);
//        }

//        /*初始化数据监控缓存保存条数*/
//        Object monitor_save_num = redisService.get(RedisConstant.MONITOR_SAVE_NUM);
//        if (monitor_save_num == null) {
//            redisService.set(RedisConstant.MONITOR_SAVE_NUM, Constants.MONITOR_SAVE_NUM);
//        }


        /*初始化公差限*/
        redisService.deleteObject(RedisConstant.SPEC_KEY+"*");
        SPEC_INF_VO specInfVo = new SPEC_INF_VO();
        specInfVo.setDbType(DbLinkEnum.getType(driverClassName).getType());
        specInfVo.setNext(Constants.NEXT);
        specInfVo.setF_DEL(YesOrNoEnum.NO.getType());
        List<SPEC_INF_DTO> specInfDtoList = specInfService.getList(specInfVo);
        Map<String,SPEC_INF_DTO> specMap = new HashMap<>();
        for (SPEC_INF_DTO specInfDto : specInfDtoList) {
            specInfDto.initData();
            String key = RedisConstant.SPEC_KEY +
                    specInfDto.getF_PART() + Constants.COMMA +
                    specInfDto.getF_PTRV() + Constants.COMMA +
                    specInfDto.getF_TEST() + Constants.COLON +
                    specInfDto.getF_PRCS();
            specMap.put(key, specInfDto);
        }

        for (String key : specMap.keySet()) {
            redisService.setCacheObject(key, specMap.get(key));
        }


        //初始化控制线
        redisService.deleteObject(RedisConstant.CTRL_KEY+"*");
        CTRL_INF_VO ctrlInfVo = new CTRL_INF_VO();
        ctrlInfVo.setDbType(DbLinkEnum.getType(driverClassName).getType());
        ctrlInfVo.setNext(Constants.NEXT);
        ctrlInfVo.setF_DEL(YesOrNoEnum.NO.getType());
        List<CTRL_INF_DTO> ctrlInfDtoList = ctrlInfService.getList(ctrlInfVo);

        for (CTRL_INF_DTO ctrlInfDto : ctrlInfDtoList) {
            String key = RedisConstant.CTRL_KEY +
                    ctrlInfDto.getF_PART() + Constants.COMMA +
                    ctrlInfDto.getF_PTRV() + Constants.COMMA +
                    ctrlInfDto.getF_PRCS() + Constants.COMMA +
                    ctrlInfDto.getF_TEST();
            redisService.addToZSet(key, ctrlInfDto, ctrlInfDto.getF_EFTM().getTime(), -1, TimeUnit.HOURS);
        }


        /*初始化字典配置*/
        List<DICT_INF> dictInfList = dictInfService.list();
        if (CollectionUtils.isNotEmpty(dictInfList)) {
            Map<String, List<DICT_INF>> dictInfMap = dictInfList.stream().collect(Collectors.groupingBy(DICT_INF::getF_CODE));
            redisService.setCacheMap(RedisConstant.DICT_CACHE, dictInfMap);
        }

        /*初始化语言*/
        Object language_type = redisService.get(RedisConstant.LANGUAGE_TYPE);
        if (language_type == null) {
            redisService.set(RedisConstant.LANGUAGE_TYPE, LanguageEnum.ZH_CN.getIndex());
        }

        /*初始化子组生成id*/
        Long nextId = idGeneratorService.getNextId(RedisConstant.SGRP_TAG);
        if (nextId == null || nextId.intValue() == 1) {
            idGeneratorService.initId(RedisConstant.SGRP_TAG, 1000000000000000000L);
        }
    }


    /**
     * 检验授权信息
     *
     * @return
     */
    public Boolean checkAuth() {
        List<AUTH_INF> authInfList = authInfMapper.selectList(new LambdaQueryWrapper<>());
        if (CollectionUtils.isEmpty(authInfList)) return false;

        for (AUTH_INF authInf : authInfList) {
            if (StringUtils.isNotEmpty(authInf.getF_CODE())) {
                String code = AESUtil.decryptStr(authInf.getF_CODE(), AESUtil.defaultAesKey);
                if (code == null || code.equals("")) {
                    log.info("code不存在");
                    continue;
                }
                String[] split = code.split("@");
                /*判断机器码是否一致*/
                if (HardwareIDUtils.checkAuth(split[0])) {
                    /*判断是否短期授权*/
                    if (Integer.parseInt(split[2]) == 0) {
                        /*判断是否过期*/
                        Date startDate = DateUtils.parseDate(split[3]);
                        Date endDate = DateUtils.parseDate(split[4]);
                        if (endDate.getTime() < System.currentTimeMillis() || startDate.getTime() > System.currentTimeMillis()) {
                            log.info("时间已到期");
                            return false;
                        }
                    }
                    if (ProductTypeEnum.DATA_SERVICE.getType() == Integer.parseInt(split[5])) {
                        //数据服务将最大在线人数加入Redis
                        redisService.set(RedisConstant.DATA_MANAGEMENT_SERVICE, authInf.getF_CODE());
                    } else {
                        /*采集服务*/
                    }
                    return true;
                } else {
                    log.info("code校验失败");
                    AUTH_FLAG = false;
                }
            }
        }
        return false;
    }

    /**
     * 监听key过期事件
     **/
    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }

    public static Integer getDriverType() {
        return DbLinkEnum.getType(dbName).getType();
    }
}
