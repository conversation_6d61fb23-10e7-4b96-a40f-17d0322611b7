
package com.yingfei.dataManagement.controller.gauge;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.gauge.GAUGE_INTERFACEService;
import com.yingfei.entity.domain.GAUGE_INTERFACE;
import com.yingfei.entity.dto.GAUGE_INTERFACE_DTO;
import com.yingfei.entity.vo.GAUGE_INTERFACE_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "量具接口配置信息API")
@RestController
@RequestMapping("/gauge_interface")
public class GAUGE_INTERFACEController extends BaseController {

    @Resource
    private GAUGE_INTERFACEService gaugeInterfaceService;

    /**
     * 获取量具接口配置信息列表
     */
    @ApiOperation("获取量具接口配置信息列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody GAUGE_INTERFACE_VO gaugeFormatVo) {
        List<GAUGE_INTERFACE_DTO> list = gaugeInterfaceService.getList(gaugeFormatVo);
        return getDataTable(list,gaugeInterfaceService.getTotal(gaugeFormatVo));
    }

    /**
     * 新增量具接口配置信息
     */
    @CreateUpdateBy
    @NotResubmit
    @Log(title = "量具接口配置管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增量具接口配置信息")
    @PostMapping("/add")
    public R<?> add(@RequestBody GAUGE_INTERFACE_VO gaugeFormatVo) {
        gaugeInterfaceService.checkParam(gaugeFormatVo);
        gaugeInterfaceService.add(gaugeFormatVo);
        return R.ok();
    }

    /**
     * 修改量具接口配置信息
     */
    @Log(title = "量具接口配置管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改量具接口配置信息")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody GAUGE_INTERFACE_VO gaugeFormatVo) {
        gaugeInterfaceService.checkParam(gaugeFormatVo);
        GAUGE_INTERFACE gaugeFormat = new GAUGE_INTERFACE();
        BeanUtils.copyPropertiesIgnoreNull(gaugeFormatVo, gaugeFormat);
        gaugeInterfaceService.updateById(gaugeFormat);
        return R.ok();
    }

    /**
     * 批量删除量具接口配置信息
     */
    @Log(title = "量具接口配置管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除量具接口配置信息")
    @DeleteMapping("/remove/{ids}")
    public R<?> remove(@PathVariable List<Long> ids) {
        gaugeInterfaceService.del(ids);
        return R.ok();
    }
}
