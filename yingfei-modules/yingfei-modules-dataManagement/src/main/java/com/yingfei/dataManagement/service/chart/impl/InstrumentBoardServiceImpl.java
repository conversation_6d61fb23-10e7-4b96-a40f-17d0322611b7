package com.yingfei.dataManagement.service.chart.impl;

import com.alibaba.fastjson2.JSONObject;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yingfei.common.core.config.ThreadPoolConfig;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.dataManagement.mapper.STREAM_TREND_INFMapper;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.chart.ANALYSIS_DASHBOARD_TEMPLATE_INFService;
import com.yingfei.dataManagement.service.chart.ChartCommonService;
import com.yingfei.dataManagement.service.chart.InstrumentBoardService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.dto.chart.AnalysisChartConfigDTO;
import com.yingfei.entity.dto.chart.CapabilityMatrixConfigDTO;
import com.yingfei.entity.dto.chart.InstrumentBoardConfigDTO;
import com.yingfei.entity.dto.chart.InstrumentBoardDTO;
import com.yingfei.entity.enums.ChartTypeEnum;
import com.yingfei.entity.util.CapabilityMatrixUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InstrumentBoardServiceImpl implements InstrumentBoardService {

    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private ANALYSIS_DASHBOARD_TEMPLATE_INFService analysisDashboardTemplateInfService;
    @Resource
    private STREAM_TREND_INFMapper capabilityTrendInfMapper;
    @Resource
    private ChartCommonService chartCommonService;


    private static final ReentrantLock lock = new ReentrantLock();

    /**
     * 能力仪表盘
     */
    @Override
    public List<InstrumentBoardDTO> clusterAnalysisInfo(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        /*根据菜单获取分析模板配置*/
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.INSTRUMENT_BOARD);
        InstrumentBoardConfigDTO instrumentBoardConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), InstrumentBoardConfigDTO.class);

        /*能力仪表盘列表*/
        List<InstrumentBoardDTO> list = new ArrayList<>();
        /*获取聚合分析子组数据*/
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);

        if (subgroupDataSelectionDTO == null)
            return list;

        /*获取子组列表*/
        List<SubgroupDataDTO> subgroupDataDTOList = chartCommonService.getCacheSubgroupData(subgroupDataSelectionDTO, analysisChartConfigDTO);
        /*能力仪表盘只查询测试类型为变量的*/
        subgroupDataSelectionDTO.setTestType(YesOrNoEnum.YES.getType());
        /*通过测试数据重新组装子组*/
        List<SubgroupDataDTO> reassemblySubgroupDataDTOList = chartCommonService.reassembly(subgroupDataDTOList);
        if (subgroupDataSelectionDTO.getTestType() == 1) {
            reassemblySubgroupDataDTOList = reassemblySubgroupDataDTOList.stream().filter(s -> s.getTestType() == 1).collect(Collectors.toList());
        }
        long startTime = System.currentTimeMillis();

        /*将part,prcs,test作为key生成map*/
        Map<String, List<SubgroupDataDTO>> map = reassemblySubgroupDataDTOList.stream()
                .collect(Collectors.groupingBy(s ->
                                s.getF_PART() + Constants.COMMA +
                                        s.getF_REV() + Constants.COMMA +
                                        s.getF_PRCS() + Constants.COMMA +
                                        s.getF_TEST(),
                        LinkedHashMap::new, Collectors.toList()));


        CountDownLatch countDownLatch = new CountDownLatch(map.size());

        SubgroupDataSelectionDTO finalSubgroupDataSelectionDTO = subgroupDataSelectionDTO;
        map.forEach((k, v) -> {
            threadPoolConfig.threadPoolTaskExecutor().execute(() -> {
                try {
                    lock.lock();
                    /*将失效子组排除*/
                    List<SubgroupDataDTO> collect = v.stream().filter(s -> s.getF_FLAG() != null && s.getF_FLAG() != YesOrNoEnum.YES.getType())
                            .sorted(Comparator.comparing(SubgroupDataDTO::getF_SGTM).thenComparing(SubgroupDataDTO::getF_SGRP))
                            .collect(Collectors.toList());
                    /*能力仪表盘*/
                    InstrumentBoardDTO instrumentBoardDTO = getInstrumentBoardDTO(collect, finalSubgroupDataSelectionDTO, analysisChartConfigDTO);
                    if (instrumentBoardDTO != null) {
                        instrumentBoardDTO.setStartTime(collect.get(0).getF_SGTM());
                        instrumentBoardDTO.setEndTime(collect.get(collect.size() - 1).getF_SGTM());
                        list.add(instrumentBoardDTO);
                    }
                } finally {
                    log.info("剩余数量 : {}", countDownLatch.getCount());
                    countDownLatch.countDown();
                    lock.unlock();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        List<InstrumentBoardDTO> instrumentBoardDTOList;
        if (instrumentBoardConfigDTO.getShowType() == 1) {
            if (instrumentBoardConfigDTO.getSortType() == 1) {
                /*按cpk降序*/
                instrumentBoardDTOList = list.stream().sorted(Comparator
                                .comparing(InstrumentBoardDTO::getCpkRatio, Comparator.nullsLast(Double::compareTo)))
                        .collect(Collectors.toList());
            } else {
                instrumentBoardDTOList = list.stream().sorted(Comparator
                                .comparing(InstrumentBoardDTO::getCpkRatio, Comparator.nullsLast(Double::compareTo)).reversed())
                        .collect(Collectors.toList());
            }
        } else {
            if (instrumentBoardConfigDTO.getSortType() == 1) {
                instrumentBoardDTOList = list.stream().sorted(Comparator
                                .comparing(InstrumentBoardDTO::getPpk, Comparator.nullsLast(Double::compareTo)))
                        .collect(Collectors.toList());
            } else {
                instrumentBoardDTOList = list.stream().sorted(Comparator
                                .comparing(InstrumentBoardDTO::getPp, Comparator.nullsLast(Double::compareTo)).reversed())
                        .collect(Collectors.toList());
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("聚合分析耗时:{}秒", (endTime - startTime) / 1000);
        log.info("总数量 : {}  ,实际数量 : {}", map.size(), list.size());
        return instrumentBoardDTOList;

    }


    /**
     * 计算不同的part、pacs、test的仪表盘数据
     *
     * @param subgroupDataSelectionDTOList 列表
     * @return
     */
    private InstrumentBoardDTO getInstrumentBoardDTO(List<SubgroupDataDTO> subgroupDataSelectionDTOList, SubgroupDataSelectionDTO subgroupDataSelectionDTO, AnalysisChartConfigDTO analysisChartConfigDTO) {
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTOList)) {
            return null;
        }
        SubgroupDataDTO subgroupDataDTO = subgroupDataSelectionDTOList.get(0);
        InstrumentBoardDTO instrumentBoardDTO = new InstrumentBoardDTO(subgroupDataSelectionDTOList.get(0));

        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.getBasic(subgroupDataSelectionDTOList, analysisChartConfigDTO.getType(), 0);

        /*获取公差限数据  目标cp和目标cpk*/
        SPEC_INF_DTO specLim = specInfService.getSpecLim(dataSummaryDTO, subgroupDataDTO);
        if (specLim == null) {
            instrumentBoardDTO.setDataSummaryDTO(dataSummaryDTO);
            return instrumentBoardDTO;
        }

        /*获取目标和超公差数据*/
        DataSummaryDTO.getBasicTwo(dataSummaryDTO);

        /*获取过程潜力指数*/
        DataSummaryDTO.getBasicThree(dataSummaryDTO);

        /*获取过程能力指数*/
        DataSummaryDTO.getBasicFour(dataSummaryDTO);

        if (dataSummaryDTO.getCp() == null || specLim.getF_CP() == null || specLim.getF_CP() == 0d) {
            instrumentBoardDTO.setCpRatio(0d);
        } else {
            instrumentBoardDTO.setCpRatio(dataSummaryDTO.getCp() / specLim.getF_CP());
        }
        if (dataSummaryDTO.getCpk() == null || specLim.getF_CPK() == null || specLim.getF_CPK() == 0d) {
            instrumentBoardDTO.setCpkRatio(0d);
        } else {
            instrumentBoardDTO.setCpkRatio(dataSummaryDTO.getCpk() / specLim.getF_CPK());
        }
        instrumentBoardDTO.setDataSummaryDTO(dataSummaryDTO);
        instrumentBoardDTO.setPp(dataSummaryDTO.getPp());
        instrumentBoardDTO.setPpk(dataSummaryDTO.getPpk());
        return instrumentBoardDTO;
    }

    /**
     * 能力矩阵计算
     *
     * @param dataSummaryDTO
     */
    @Override
    public CapabilityMatrixPotentialExpectDTO competencyMatrix(DataSummaryDTO dataSummaryDTO, CapabilityMatrixConfigDTO capabilityMatrixConfigDTO) {
        CapabilityMatrixPotentialExpectDTO capabilityMatrixPotentialExpectDTO = new CapabilityMatrixPotentialExpectDTO();
        //Potential
        CapabilityMatrixPotentialExpectDTO.PotentialBean potentialBean = new CapabilityMatrixPotentialExpectDTO.PotentialBean();
        potentialBean.setZ_USL(CapabilityMatrixUtil.getZ_USL(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
        potentialBean.setZ_LSL(CapabilityMatrixUtil.getZ_LSL(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
        potentialBean.setFraction_GreaterThan_USL(CapabilityMatrixUtil.getFractionGreaterThanUSL(potentialBean.getZ_USL()));
        potentialBean.setFraction_LessThan_LSL(CapabilityMatrixUtil.getFractionLessThanLSL(potentialBean.getZ_LSL()));
        potentialBean.setWeighted_Fraction_LessThan_LSL(CapabilityMatrixUtil.getWeightedFractionLessThanLSL(potentialBean.getFraction_LessThan_LSL(), dataSummaryDTO.getSubGroupNum()));
        potentialBean.setWeighted_Fraction_GreaterThan_USL(CapabilityMatrixUtil.getWeightedFractionGreaterThanUSL(potentialBean.getFraction_GreaterThan_USL(), dataSummaryDTO.getSubGroupNum()));
        potentialBean.setPDPM(CapabilityMatrixUtil.getPDPM(potentialBean.getFraction_GreaterThan_USL(), potentialBean.getFraction_LessThan_LSL()));
        potentialBean.setYield(CapabilityMatrixUtil.getYield(potentialBean.getFraction_GreaterThan_USL(), potentialBean.getFraction_LessThan_LSL()));
        potentialBean.setCpk(dataSummaryDTO.getCpk());
        capabilityMatrixPotentialExpectDTO.setPotential(potentialBean);

        //Potential(Centered Process)
        CapabilityMatrixPotentialExpectDTO.PotentialCenteredProcessBean centeredProcessBean = new CapabilityMatrixPotentialExpectDTO.PotentialCenteredProcessBean();

        centeredProcessBean.setSpec_Z(CapabilityMatrixUtil.getSpecZ(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(), dataSummaryDTO.getTargetValue(), dataSummaryDTO.getShortTermStandardDeviation()));

        centeredProcessBean.setFraction_OOS(CapabilityMatrixUtil.getFractionOOS(dataSummaryDTO.getTargetValue(), dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(), dataSummaryDTO.getLongTermStandardDeviation()));
        centeredProcessBean.setWeighted_Fraction_OOS(CapabilityMatrixUtil.getWeightedFractionOOS(centeredProcessBean.getFraction_OOS()));
        centeredProcessBean.setPDPM(CapabilityMatrixUtil.getPDPM(centeredProcessBean.getFraction_OOS()));
        centeredProcessBean.setYield(CapabilityMatrixUtil.getYield(centeredProcessBean.getFraction_OOS()));
        centeredProcessBean.setCp(dataSummaryDTO.getCp());
        capabilityMatrixPotentialExpectDTO.setPotential_Centered_Process(centeredProcessBean);

        //Expect
        CapabilityMatrixPotentialExpectDTO.ExpectBean expectBean = new CapabilityMatrixPotentialExpectDTO.ExpectBean();
        expectBean.setUSL_Z(CapabilityMatrixUtil.getUSL_Z(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));
        expectBean.setLSL_Z(CapabilityMatrixUtil.getLSL_Z(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));
        expectBean.setFraction_LessThan_LSL(CapabilityMatrixUtil.getFraction_LessThan_LSL(expectBean.getLSL_Z()));
        expectBean.setFraction_GreaterThan_USL(CapabilityMatrixUtil.getFraction_GreaterThan_USL(expectBean.getUSL_Z()));
        expectBean.setWeightedFraction_GreaterThan_USL(CapabilityMatrixUtil.getWeightedFraction_GreaterThan_USL(expectBean.getFraction_GreaterThan_USL(), dataSummaryDTO.getSubGroupNum()));
        expectBean.setWeightedFraction_LessThan_LSL(CapabilityMatrixUtil.getWeightedFraction_LessThan_LSL(expectBean.getFraction_LessThan_LSL(), dataSummaryDTO.getSubGroupNum()));
        expectBean.setDPM(CapabilityMatrixUtil.getDPM(expectBean.getFraction_LessThan_LSL(), expectBean.getFraction_GreaterThan_USL()));
        expectBean.setYield(CapabilityMatrixUtil.getExpect_Yield(expectBean.getFraction_LessThan_LSL(), expectBean.getFraction_GreaterThan_USL()));
        expectBean.setPpk(dataSummaryDTO.getPpk());
        capabilityMatrixPotentialExpectDTO.setExpect(expectBean);

        /**/
        CapabilityMatrixPotentialExpectDTO.GradingBean gradingBean = new CapabilityMatrixPotentialExpectDTO.GradingBean();
        if (expectBean.getYield() == null || potentialBean.getYield() == null) {
            gradingBean.setStreamPerformance(0d);
        } else {
            if (potentialBean.getYield() == 0d) {
                gradingBean.setStreamPerformance(0d);
            } else {
                gradingBean.setStreamPerformance((expectBean.getYield() /
                        potentialBean.getYield()) * 100);
            }
        }
        if (gradingBean.getStreamPerformance() > 100d) gradingBean.setStreamPerformance(100d);
        /*计算1,2,3等级*/
        if (gradingBean.getStreamPerformance() > capabilityMatrixConfigDTO.getLevelEnd()) {
            gradingBean.setPerformanceGrade(1);
        } else if (gradingBean.getStreamPerformance() > capabilityMatrixConfigDTO.getLevelStart()) {
            gradingBean.setPerformanceGrade(2);
        } else {
            gradingBean.setPerformanceGrade(3);
        }

        if (centeredProcessBean.getYield() == null) {
            gradingBean.setStreamPotential(0d);
        } else {
            gradingBean.setStreamPotential(centeredProcessBean.getYield());
        }
        /*计算A,B,C等级*/
        if (gradingBean.getStreamPotential() > capabilityMatrixConfigDTO.getLevelMax()) {
            gradingBean.setPotentialGrade(Constants.A);
        } else if (gradingBean.getStreamPotential() > capabilityMatrixConfigDTO.getLevelMin()) {
            gradingBean.setPotentialGrade(Constants.B);
        } else {
            gradingBean.setPotentialGrade(Constants.C);
        }
        /*拼接*/
        gradingBean.setTotalGrade(gradingBean.getPotentialGrade() + gradingBean.getPerformanceGrade());

        gradingBean.setExpectYield(expectBean.getYield());
        capabilityMatrixPotentialExpectDTO.setGradingBean(gradingBean);
        return capabilityMatrixPotentialExpectDTO;
    }

    @Override
    public List<STREAM_TREND_INF_DTO> getTrend(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.CAPABILITY_TREND);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
//        RealTimeCapabilityTrendConfigDTO realTimeCapabilityTrendConfigDTO;
//        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
//            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), RealTimeCapabilityTrendConfigDTO.class);
//        } else {
//            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), RealTimeCapabilityTrendConfigDTO.class);
//        }

        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);
        if (subgroupDataSelectionDTO == null) return new ArrayList<>();
        if (subgroupDataSelectionDTO.getF_PART() == null || subgroupDataSelectionDTO.getF_PRCS() == null || subgroupDataSelectionDTO.getF_TEST() == null) {
            return new ArrayList<>();
        }
        return getStreamTrendInfDtos(subgroupDataSelectionDTO);
    }
    private List<STREAM_TREND_INF_DTO> getStreamTrendInfDtos(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        MPJLambdaWrapper<STREAM_TREND_INF> mpjBaseMapper = new MPJLambdaWrapper<>();
        mpjBaseMapper.leftJoin(PART_INF.class, PART_INF::getF_PART, STREAM_TREND_INF::getF_PART);
        mpjBaseMapper.leftJoin(PART_REV.class, PART_REV::getF_PTRV, STREAM_TREND_INF::getF_PTRV);
        mpjBaseMapper.leftJoin(PRCS_INF.class, PRCS_INF::getF_PRCS, STREAM_TREND_INF::getF_PRCS);
        mpjBaseMapper.leftJoin(TEST_INF.class, TEST_INF::getF_TEST, STREAM_TREND_INF::getF_TEST);
        mpjBaseMapper.selectAll(STREAM_TREND_INF.class)
                .selectAs(PART_INF::getF_NAME, STREAM_TREND_INF_DTO::getPartName)
                .selectAs(PART_REV::getF_NAME, STREAM_TREND_INF_DTO::getPtrvName)
                .selectAs(PRCS_INF::getF_NAME, STREAM_TREND_INF_DTO::getPrcsName)
                .selectAs(TEST_INF::getF_NAME, STREAM_TREND_INF_DTO::getTestName);
        mpjBaseMapper.eq(STREAM_TREND_INF::getF_PART, subgroupDataSelectionDTO.getF_PART())
                .eq(STREAM_TREND_INF::getF_TYPE,subgroupDataSelectionDTO.getTrendType())
                .eq(STREAM_TREND_INF::getF_PART, subgroupDataSelectionDTO.getF_PART())
                .eq(STREAM_TREND_INF::getF_PTRV, subgroupDataSelectionDTO.getF_REV())
                .eq(STREAM_TREND_INF::getF_PRCS, subgroupDataSelectionDTO.getF_PRCS())
                .eq(STREAM_TREND_INF::getF_TEST, subgroupDataSelectionDTO.getF_TEST());
        if (subgroupDataSelectionDTO.getStartTime() != null) {
            mpjBaseMapper.ge(STREAM_TREND_INF::getF_START, subgroupDataSelectionDTO.getStartTime());
        }
        if (subgroupDataSelectionDTO.getEndTime() != null) {
            mpjBaseMapper.le(STREAM_TREND_INF::getF_END, subgroupDataSelectionDTO.getEndTime());
        }
        mpjBaseMapper.orderByAsc(STREAM_TREND_INF::getF_CRTM).last("offset 0 rows\n" +
                "fetch next 14 rows only");

        final List<STREAM_TREND_INF_DTO> streamTrendInfDtos = capabilityTrendInfMapper.selectJoinList(STREAM_TREND_INF_DTO.class, mpjBaseMapper);
        //如果仅一个数据点，且数据点对应的参数值为空时，直接显示无数据
        if(streamTrendInfDtos.size() == 1){
            //cp || cpk 为空
            if(ObjectUtils.isEmpty(streamTrendInfDtos.get(0).getF_CP()) || ObjectUtils.isEmpty(streamTrendInfDtos.get(0).getF_CPK())){
                return Collections.emptyList();
            }
        }
        return streamTrendInfDtos;
    }
//    private List<STREAM_TREND_INF> getStreamTrendInfs(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
//        LambdaQueryWrapper<STREAM_TREND_INF> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(STREAM_TREND_INF::getF_PART, subgroupDataSelectionDTO.getF_PART())
//                .eq(STREAM_TREND_INF::getF_PRCS, subgroupDataSelectionDTO.getF_PRCS())
//                .eq(STREAM_TREND_INF::getF_TEST, subgroupDataSelectionDTO.getF_TEST());
//        if (subgroupDataSelectionDTO.getStartTime() != null) {
//            queryWrapper.ge(STREAM_TREND_INF::getF_START, subgroupDataSelectionDTO.getStartTime());
//        }
//        if (subgroupDataSelectionDTO.getEndTime() != null) {
//            queryWrapper.le(STREAM_TREND_INF::getF_END, subgroupDataSelectionDTO.getEndTime());
//        }
//        queryWrapper.orderByAsc(STREAM_TREND_INF::getF_CRTM).last("offset 0 rows\n" +
//                "fetch next 14 rows only");
//        return capabilityTrendInfMapper.selectList(queryWrapper);
//    }


    @Override
    public List<List<STREAM_TREND_INF_DTO>> getTrendList(SubgroupDataSelectionDTO subgroupDataSelectionDTO) {
        AnalysisChartConfigDTO analysisChartConfigDTO = analysisDashboardTemplateInfService.findByAnalysisChartConfig(subgroupDataSelectionDTO, ChartTypeEnum.CAPABILITY_TREND);
        if (analysisChartConfigDTO == null) {
            throw new BusinessException(DataManagementExceptionEnum.CHART_TYPE_NOT_EXISTS);
        }
//        RealTimeCapabilityTrendConfigDTO realTimeCapabilityTrendConfigDTO;
//        if (subgroupDataSelectionDTO.getBoxPlotsConfigDTO() != null) {
//            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(subgroupDataSelectionDTO.getBoxPlotsConfigDTO()), RealTimeCapabilityTrendConfigDTO.class);
//        } else {
//            realTimeCapabilityTrendConfigDTO = JSONObject.parseObject(JSONObject.toJSONString(analysisChartConfigDTO.getChartConfig()), RealTimeCapabilityTrendConfigDTO.class);
//        }
        subgroupDataSelectionDTO = chartCommonService.getCacheCondition(subgroupDataSelectionDTO, analysisChartConfigDTO);

        List<List<STREAM_TREND_INF_DTO>> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPartList()) || CollectionUtils.isEmpty(subgroupDataSelectionDTO.getPrcsList()) || CollectionUtils.isEmpty(subgroupDataSelectionDTO.getTestList())) {
            return new ArrayList<>();
        }
        final List<Map<String, Long>> queryMapList = chartCommonService.generateCombinations(
                subgroupDataSelectionDTO.getPartList(),
                subgroupDataSelectionDTO.getPtrvList(),
                subgroupDataSelectionDTO.getPrcsList(),
                subgroupDataSelectionDTO.getTestList());

        List<Future<List<STREAM_TREND_INF_DTO>>> futures = new ArrayList<>();

        for (Map<String, Long> map : queryMapList) {
            // 进行空值检查
            if (map == null) {
                continue;
            }
            Long fpart = map.get(Constants.part);
            Long fptrv = map.get(Constants.ptrv);
            Long fprcs = map.get(Constants.prcs);
            Long ftest = map.get(Constants.test);

            // 提交任务到线程池
            // 创建 subgroupDataSelectionDTO 的副本
            SubgroupDataSelectionDTO copyDto = new SubgroupDataSelectionDTO();
            // 复制原对象的属性
            copyDto.setStartTime(subgroupDataSelectionDTO.getStartTime());
            copyDto.setEndTime(subgroupDataSelectionDTO.getEndTime());
            // 设置新的参数
            copyDto.setF_PART(fpart).setF_REV(fptrv).setF_PRCS(fprcs).setF_TEST(ftest);
            futures.add(threadPoolConfig.threadPoolTaskExecutor().submit(() -> getStreamTrendInfDtos(copyDto)));
        }
        // 获取任务结果
        for (Future<List<STREAM_TREND_INF_DTO>> future : futures) {
            try {
                List<STREAM_TREND_INF_DTO> dto = future.get();
                if (dto != null && !dto.isEmpty()) {
                    list.add(dto);
                }
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程处理出错", e);
            }
        }
        return list;
    }

}
