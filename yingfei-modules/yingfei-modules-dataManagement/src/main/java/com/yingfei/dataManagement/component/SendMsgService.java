//package com.yingfei.dataManagement.component;
//
//import com.alibaba.fastjson2.JSONObject;
//import com.yingfei.dataManagement.service.SYSTEM_NOTIFICATION_INFService;
//import com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF;
//import com.yingfei.entity.dto.EMPL_INF_DTO;
//import com.yingfei.entity.dto.SYSTEM_NOTIFICATION_ALARM_DTO;
//import com.yingfei.entity.dto.dingDing.DingDingSendMessageDTO;
//import com.yingfei.entity.dto.email.EmailSendMessageDTO;
//import com.yingfei.entity.dto.qyWeChat.QyWeChatSendMessageDTO;
//import com.yingfei.entity.enums.NOTIFICATION_TYPEEnum;
//import com.yingfei.entity.vo.EMPL_INF_VO;
//import com.yingfei.system.api.RemoteSendService;
//import com.yingfei.system.api.RemoteUserService;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@Component
//public class SendMsgService {
//    @Resource
//    private RemoteSendService remoteSendService;
//    @Resource
//    private RemoteUserService remoteUserService;
//    @Resource
//    private SYSTEM_NOTIFICATION_INFService systemNotificationInfService;
//
//    /**
//     * 发送消息
//     * @param notificationTypeEnum 消息类型
//     * @param userIds 用户id列表
//     * @param title 标题
//     * @param content 内容
//     * @param systemNotificationAlarmDto 系统通知告警dto
//     */
//    public void sendMsg(NOTIFICATION_TYPEEnum notificationTypeEnum, List<Long> userIds, String title, String content, SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto) {
//        final EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
//        emplInfVo.setIds(userIds);
//        final List<EMPL_INF_DTO> emplList = remoteUserService.getList(emplInfVo).getData();
//        switch (notificationTypeEnum) {
//            case EMAIL:
//                sendEmailMsg(emplList, title, content);
//                break;
//            case DING_DING:
//                sendDingDingMsg(emplList, content);
//                break;
//            case QY_WECHAT:
//                sendQyWeChatMsg(emplList, content);
//                break;
//            case SYSTEM_MESSAGE:
//            default:
//                sendSystemMsg(emplList, systemNotificationAlarmDto);
//                break;
//        }
//    }
//
//    /**
//     * 发送系统消息
//     * @param emplList 员工列表
//     * @param systemNotificationAlarmDto 系统通知告警dto
//     */
//    public void sendSystemMsg(List<EMPL_INF_DTO> emplList, SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto) {
//        List<SYSTEM_NOTIFICATION_INF> systemNotificationInfList = new ArrayList<>();
//        for (EMPL_INF_DTO emplInfDto : emplList) {
//            SYSTEM_NOTIFICATION_INF systemNotificationInf = new SYSTEM_NOTIFICATION_INF();
//            systemNotificationInf.setF_DATA(JSONObject.toJSONString(systemNotificationAlarmDto));
//            systemNotificationInf.setF_TYPE(2);
//            systemNotificationInf.setF_EMPL(emplInfDto.getF_EMPL());
//            systemNotificationInf.setF_CRUE(emplInfDto.getF_CRUE());
//            systemNotificationInf.setF_SYNO(null);
//            systemNotificationInf.setF_EDUE(emplInfDto.getF_CRUE());
//            systemNotificationInfList.add(systemNotificationInf);
//        }
//        systemNotificationInfService.saveBatch(systemNotificationInfList);
//    }
//
//    /**
//     * 发送邮件消息
//     * @param emplList 员工列表
//     * @param title 标题
//     * @param content 内容
//     */
//    public void sendEmailMsg(List<EMPL_INF_DTO> emplList, String title, String content) {
//        for (EMPL_INF_DTO emplInfDto : emplList) {
//            EmailSendMessageDTO emailSendMessageDTO = new EmailSendMessageDTO();
//            emailSendMessageDTO.setEmailList(Collections.singletonList(emplInfDto.getF_EMAIL()));
//            emailSendMessageDTO.setTitle(title);
//            emailSendMessageDTO.setContent(content);
//            remoteSendService.emailSend(emailSendMessageDTO);
//        }
//    }
//
//    /**
//     * 发送钉钉消息
//     * @param emplList 员工列表
//     * @param content 内容
//     */
//    public void sendDingDingMsg(List<EMPL_INF_DTO> emplList, String content) {
//        for (EMPL_INF_DTO emplInfDto : emplList) {
//            DingDingSendMessageDTO dingDingSendMessageDTO = new DingDingSendMessageDTO();
//            dingDingSendMessageDTO.setUserid_list(emplInfDto.getF_DINGDING());
//            JSONObject ddText = new JSONObject();
//            JSONObject ddContent = new JSONObject();
//            ddContent.put("content", content);
//            ddText.put("text", ddContent.toJSONString());
//            ddText.put("msgtype", "text");
//            dingDingSendMessageDTO.setMsg(ddText);
//            remoteSendService.dingDingSend(dingDingSendMessageDTO);
//        }
//    }
//
//    /**
//     * 发送企业微信消息
//     * @param emplList 员工列表
//     * @param content 内容
//     */
//    public void sendQyWeChatMsg(List<EMPL_INF_DTO> emplList, String content) {
//        for (EMPL_INF_DTO emplInfDto : emplList) {
//            QyWeChatSendMessageDTO qyWeChatSendMessageDTO = new QyWeChatSendMessageDTO();
//            qyWeChatSendMessageDTO.setTouser(emplInfDto.getF_WECHAT());
//            JSONObject qyText = new JSONObject();
//            JSONObject qyContent = new JSONObject();
//            qyContent.put("content", content);
//            qyText.put("text", qyContent.toJSONString());
//            qyWeChatSendMessageDTO.setText(qyText);
//            remoteSendService.qyWeChatSend(qyWeChatSendMessageDTO);
//        }
//    }
//
//}
