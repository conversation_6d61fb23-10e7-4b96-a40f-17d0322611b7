package com.yingfei.dataManagement.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流节点超时配置常量
 * 定义不同流程节点的超时提醒时间配置
 */
public class WorkflowTimeoutConstants {
    // 默认超时时间配置（分钟）
    public static final int DEFAULT_FIRST_REMINDER_TIMEOUT = 60;  // 第一次提醒：60分钟
    public static final int DEFAULT_SECOND_REMINDER_TIMEOUT = 120; // 第二次提醒（给上级）：120分钟
    public static final int DEFAULT_THIRD_REMINDER_TIMEOUT = 240;  // 第三次提醒（给领导的领导）：240分钟

    // 任务节点ID映射超时配置
    private static final Map<String, NodeTimeoutConfig> NODE_TIMEOUT_CONFIGS = new HashMap<>();

    static {
        // 初始化不同流程节点的超时配置
        // 异常原因审批节点超时配置
        NODE_TIMEOUT_CONFIGS.put("EXCEPTION_CAUSE_APPROVAL", new NodeTimeoutConfig(30, 60, 120));
        // 响应措施审批节点超时配置
        NODE_TIMEOUT_CONFIGS.put("RESPONSE_ACTION_APPROVAL", new NodeTimeoutConfig(45, 90, 180));
        // 根本原因分析审批节点超时配置
        NODE_TIMEOUT_CONFIGS.put("ROOT_CAUSE_ANALYSIS_APPROVAL", new NodeTimeoutConfig(60, 120, 240));
        // 纠正措施审批节点超时配置
        NODE_TIMEOUT_CONFIGS.put("CORRECTIVE_ACTION_APPROVAL", new NodeTimeoutConfig(60, 120, 240));
        // 验证效果确认节点超时配置
        NODE_TIMEOUT_CONFIGS.put("VERIFICATION_CONFIRMATION", new NodeTimeoutConfig(45, 90, 180));
        // 问题关闭确认节点超时配置
        NODE_TIMEOUT_CONFIGS.put("PROBLEM_CLOSURE", new NodeTimeoutConfig(30, 60, 120));
    }

    /**
     * 获取节点的超时配置
     * 如果节点没有特定配置，则返回默认配置
     */
    public static NodeTimeoutConfig getNodeTimeoutConfig(String nodeId) {
        if (nodeId == null || nodeId.isEmpty()) {
            return getDefaultConfig();
        }
        return NODE_TIMEOUT_CONFIGS.getOrDefault(nodeId, getDefaultConfig());
    }

    /**
     * 获取默认的超时配置
     */
    public static NodeTimeoutConfig getDefaultConfig() {
        return new NodeTimeoutConfig(DEFAULT_FIRST_REMINDER_TIMEOUT,
                DEFAULT_SECOND_REMINDER_TIMEOUT,
                DEFAULT_THIRD_REMINDER_TIMEOUT);
    }

    /**
     * 添加或更新节点的超时配置
     */
    public static void putNodeTimeoutConfig(String nodeId, NodeTimeoutConfig config) {
        if (nodeId != null && !nodeId.isEmpty() && config != null) {
            NODE_TIMEOUT_CONFIGS.put(nodeId, config);
        }
    }

    /**
     * 节点超时配置类
     */
    public static class NodeTimeoutConfig {
        private final int firstReminderTimeout;   // 第一次提醒超时时间（分钟）
        private final int secondReminderTimeout;  // 第二次提醒（给上级）超时时间（分钟）
        private final int thirdReminderTimeout;   // 第三次提醒（给领导的领导）超时时间（分钟）

        public NodeTimeoutConfig(int firstReminderTimeout, int secondReminderTimeout, int thirdReminderTimeout) {
            this.firstReminderTimeout = firstReminderTimeout;
            this.secondReminderTimeout = secondReminderTimeout;
            this.thirdReminderTimeout = thirdReminderTimeout;
        }

        public int getFirstReminderTimeout() {
            return firstReminderTimeout;
        }

        public int getSecondReminderTimeout() {
            return secondReminderTimeout;
        }

        public int getThirdReminderTimeout() {
            return thirdReminderTimeout;
        }
    }
}
