<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yingfei.dataManagement.mapper.BpmProcessInstanceExtMapper">



    <sql id="Base_Column_List">
        bpmprocessinstance.F_BPIE,bpmprocessinstance.F_NAME,bpmprocessinstance.F_START_USER,
        bpmprocessinstance.F_PROCESS_DEFINITION,bpmprocessinstance.F_PROCESS_INSTANCE,bpmprocessinstance.F_CATEGORY,
        bpmprocessinstance.F_STATUS,bpmprocessinstance.F_RESULT,bpmprocessinstance.F_END_TIME,
        bpmprocessinstance.F_FROM_VAL,bpmprocessinstance.F_DEL,bpmprocessinstance.F_CRUE,
        bpmprocessinstance.F_EDUE,bpmprocessinstance.F_CRTM,bpmprocessinstance.F_EDTM
    </sql>

    <sql id="baseSql">
        from BPM_PROCESS_INSTANCE bpmprocessinstance
        <where>
            1=1
            <if test="F_START_USER != null">
                and bpmprocessinstance.F_START_USER = #{F_START_USER}
            </if>
            <if test="F_BPIE != null">
                and bpmprocessinstance.F_BPIE = #{F_BPIE}
            </if>
            <if test="F_NAME != null and F_NAME != ''">
                and bpmprocessinstance.F_NAME = #{F_NAME}
            </if>
            <if test="F_PROCESS_DEFINITION != null">
                and bpmprocessinstance.F_PROCESS_DEFINITION = #{F_PROCESS_DEFINITION}
            </if>
            <if test="F_PROCESS_INSTANCE != null">
                and bpmprocessinstance.F_PROCESS_INSTANCE = #{F_PROCESS_INSTANCE}
            </if>
            <if test="F_CATEGORY != null">
                and bpmprocessinstance.F_CATEGORY = #{F_CATEGORY}
            </if>
            <if test="F_STATUS != null">
                and bpmprocessinstance.F_STATUS = #{F_STATUS}
            </if>
            <if test="F_RESULT != null">
                and bpmprocessinstance.F_RESULT = #{F_RESULT}
            </if>
            <if test="F_END_TIME != null">
                and bpmprocessinstance.F_END_TIME = #{F_END_TIME}
            </if>
        </where>
    </sql>

    <select id="getTotal" resultType="java.lang.Long">
        select count(bpmprocessinstance.F_BPIE)
        <include refid="baseSql"/>
    </select>

    <select id="getList" resultType="com.yingfei.entity.dto.BPM_PROCESS_INSTANCE_DTO">
        select
        <include refid="Base_Column_List"/>
        <include refid="baseSql"/>
        order by bpmprocessinstance.F_CRTM desc
        <choose>
            <when test="dbType == 1">
                offset #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 2">
                OFFSET #{offset} ROWS FETCH NEXT #{next} ROWS ONLY
            </when>
            <when test="dbType == 3">
                LIMIT #{next} OFFSET #{offset}
            </when>
        </choose>
    </select>
</mapper>
