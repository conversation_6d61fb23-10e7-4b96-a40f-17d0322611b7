//package com.yingfei.dataCollection.service.external.impl;
//
//import com.yingfei.common.redis.service.RedisService;
//import com.yingfei.dataCollection.service.external.ExternalImportDataService;
//import com.yingfei.dataManagement.service.SAMPLING_TASK_CONFIGService;
//import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
//import com.yingfei.entity.dto.InspectionDataDTO;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//
//@Service
//public class ExternalImportDataServiceImpl implements ExternalImportDataService {
//
//    @Resource
//    private RedisService redisCache;
//
//    @Resource
//    private SAMPLING_TASK_CONFIGService samplingTaskConfigService;
//
//    public final String EXTERNAL_IMPORT_DATA_KEY = "external:import:data:";
//    public final Long EXTERNAL_IMPORT_DATA_EXPIRE = (long) (60*24);
//
//    @Override
//    public void processInspectionData(InspectionDataDTO inspectionDataDTO) {
//                        Long samplingTaskConfigId = inspectionDataDTO.getSamplingTaskConfigId();
//        String redisKey = EXTERNAL_IMPORT_DATA_KEY + samplingTaskConfigId;
//
//        // 1. 将推送的数据存入Redis缓存
//        redisCache.lSet(redisKey, inspectionDataDTO,EXTERNAL_IMPORT_DATA_EXPIRE);
//
//        // 2. 根据配置ID获取抽样任务配置
//        SAMPLING_TASK_CONFIG samplingTaskConfig = samplingTaskConfigService.getById(samplingTaskConfigId);
//        if (samplingTaskConfig == null) {
//            return;
//        }
//
//        // 3. 根据抽样策略和方式进行抽样
//        List<Map<String, Object>> cachedData = redisCache.getCacheList(redisKey);
//        List<Map<String, Object>> sampledData = doSample(cachedData, samplingTaskConfig);
//
//        // 4. 将抽样后的数据生成子组检验数据并入库
////        sgrpInfService.createSubgroupData(sampledData, samplingTaskConfig);
//
//        // 5. 清理已处理的缓存数据
//        redisCache.deleteObject(redisKey);
//    }
//
//
//
//    private List<Map<String, Object>> doSample(List<Map<String, Object>> data, SAMPLING_TASK_CONFIG config) {
//        // TODO: 实现具体的抽样逻辑
//        return data;
//    }
//
//}
//
