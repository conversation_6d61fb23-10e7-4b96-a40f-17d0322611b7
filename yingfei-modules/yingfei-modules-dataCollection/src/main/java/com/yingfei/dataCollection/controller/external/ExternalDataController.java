package com.yingfei.dataCollection.controller.external;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.api.RemoteMqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "外部数据接口API")
@Slf4j
@RestController
@RequestMapping("/external/data")
public class ExternalDataController {
    @Resource
    private RemoteMqService remoteMqService;


    @ApiOperation("推送检验数据")
    @PostMapping("/push")
    public R<?> push(@RequestBody InspectionDataDTO inspectionDataDTO) {
        validData(inspectionDataDTO);
    return remoteMqService.send(inspectionDataDTO);
    }
    /**
     * 校验数据
     * @param inspectionDataDTO
     */
    public void validData(@Validated InspectionDataDTO inspectionDataDTO) {
        if(ObjectUtils.isEmpty(inspectionDataDTO.getSamplingTaskConfigId())){
            throw new BaseException("samplingTaskConfigId is not null");
        }

        if(ObjectUtils.isEmpty(inspectionDataDTO.getPlantName())){
            throw new BaseException("plantName is not null");
        }

        if(ObjectUtils.isEmpty(inspectionDataDTO.getMfpsName())){
            throw new BaseException("mfpsName is not null");
        }
        if(ObjectUtils.isEmpty(inspectionDataDTO.getMfndName())){
            throw new BaseException("mfndName is not null");
        }
        if(ObjectUtils.isEmpty(inspectionDataDTO.getPlanName())){
            throw new BaseException("planName is not null");
        }
        if (ObjectUtils.isEmpty(inspectionDataDTO.getChildPlanName())){
            throw new BaseException("childPlanName is not null");
        }
        if (ObjectUtils.isEmpty(inspectionDataDTO.getPartName())){
            throw new BaseException("partName is not null");
        }
        if (ObjectUtils.isEmpty(inspectionDataDTO.getPrcsName())){
            throw new BaseException("prcsName is not null");
        }
        if (ObjectUtils.isEmpty(inspectionDataDTO.getTestData())){
            throw new BaseException("testData is not null");
        }
        for (InspectionDataDTO.Test testDatum : inspectionDataDTO.getTestData()) {
            if (ObjectUtils.isEmpty(testDatum.getTestName())){
                throw new BaseException("testData.testName is not null");
            }
            if (ObjectUtils.isEmpty(testDatum.getTestType())){
                throw new BaseException("testType is not null");
            }
            if (ObjectUtils.isEmpty(testDatum.getTestVal())){
                throw new BaseException("testValue is not null");
            }
            for (InspectionDataDTO.TestVal testVal : testDatum.getTestVal()) {
                if (ObjectUtils.isEmpty(testVal.getSubTestData()) && ObjectUtils.isEmpty(testVal.getVal())){
                    throw new BaseException("testValue.val or testValue.subTestData is not null");
                }
                if(ObjectUtils.isEmpty(testVal.getVal()) && ObjectUtils.isNotEmpty(testVal.getSubTestData())){
                    for (InspectionDataDTO.SubTest subTestDatum : testVal.getSubTestData()) {
                        if (ObjectUtils.isEmpty(subTestDatum.getSubTestVal())){
                            throw new BaseException("testValue.subTestData.subTestVal is not null");
                        }
                    }
                }
            }
        }
    }
}

