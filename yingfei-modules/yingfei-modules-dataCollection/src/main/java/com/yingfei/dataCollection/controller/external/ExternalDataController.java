package com.yingfei.dataCollection.controller.external;

import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.mq.api.RemoteMqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "外部数据接口API")
@Slf4j
@RestController
@RequestMapping("/external/data")
public class ExternalDataController {
    @Resource
    private RemoteMqService remoteMqService;


    @ApiOperation("推送检验数据")
    @PostMapping("/push")
    public R<?> push(@RequestBody InspectionDataDTO inspectionDataDTO) {
    return remoteMqService.send(inspectionDataDTO);
    }
}

