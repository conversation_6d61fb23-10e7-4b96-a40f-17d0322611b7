package com.yingfei.dataCollection.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.exception.BusinessException;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.exception.enums.DataManagementExceptionEnum;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.JudgeUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataCollection.mapper.*;
import com.yingfei.dataCollection.service.DataImportService;
import com.yingfei.dataCollection.service.SamplingService;
import com.yingfei.entity.domain.*;
import com.yingfei.entity.dto.INSPECTION_PLAN_CHILD_DTO;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.msg.SendMessageDTO;
import com.yingfei.entity.enums.HIERARCHY_INFTypeEnum;
import com.yingfei.entity.enums.SamplingMethodEnum;
import com.yingfei.entity.enums.SamplingStrategyEnum;
import com.yingfei.entity.enums.SamplingTypeEnum;
import com.yingfei.entity.requestEntity.NoticeConfigVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.system.api.RemoteSendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SamplingServiceImpl implements SamplingService {

    @Resource
    private RedisService redisService;
    @Resource
    private DataImportService dataImportService;
    @Resource
    private MANUFACTURING_PROCESS_INFMapper manufacturingProcessInfMapper;
    @Resource
    private HIERARCHY_INFMapper hierarchyInfMapper;
    @Resource
    private MANUFACTURING_NODE_INFMapper manufacturingNodeInfMapper;
    @Resource
    private INSPECTION_PLAN_INFMapper inspectionPlanInfMapper;
    @Resource
    private RAW_INSPECTIONMapper rawInspectionMapper;
    @Resource
    private SAMPLING_TASK_CONFIGMapper samplingTaskConfigMapper;
    @Resource
    private RemoteSendService remoteSendService;
    /**
     * 执行抽样任务
     *
     * @param config        抽样配置
     * @param rawInspection 原始检验数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void performSampling(SAMPLING_TASK_CONFIG config, RAW_INSPECTION rawInspection) {
        log.info("开始执行抽样任务，配置: {}", config);

        try {
            // 1. 获取抽样配置
            if (config == null) {
                log.error("抽样配置不存在，配置: {}", config);
                throw new BaseException("SAMPLING_CONFIG_NOT_EXIST");
            }

            // 2. 验证配置
            if (!validateConfig(config)) {
                log.error("抽样配置验证失败，配置: {}", config);
                throw new BaseException("SAMPLING_CONFIG_VALIDATE_FAILED");
            }

            // 3. 从Redis获取缓存数据
            final List<InspectionDataDTO> cachedData = getInspectionDataByRedis(config, rawInspection);

            // 4. 检查是否满足抽样条件
            if (cachedData.isEmpty()) {
                log.info("缓存数据为空，配置: {}", config);
                return;
            }

            // 5. 执行抽样
            List<InspectionDataDTO> selectedData = selectDataBySamplingType(config, cachedData);

            // 6. 保存抽样结果
            if (CollectionUtils.isNotEmpty(selectedData)) {
                //子组数据异常通知配置
                String msgId = JudgeUtils.nextId() + "";
                if(CollectionUtils.isNotEmpty(config.getNoticeTypeList())) {
                    final NoticeConfigVO noticeConfigVO = new NoticeConfigVO();
                    noticeConfigVO.setCollectionType(3);
                    noticeConfigVO.setNoticeUser(config.getNoticeUserList());
                    noticeConfigVO.setMoticeRule(config.getNoticeRoleList());
                    noticeConfigVO.setNoticeType(config.getNoticeTypeList());
                    redisService.set(RedisConstant.INSPECTION_MESSAGE_KEY + msgId, noticeConfigVO, RedisConstant.INSPECTION_MESSAGE_EXPIRE);
                }
                //添加子组数据
                dataImportService.batchAdd(convertToSubgroupDataVOList(selectedData,config), msgId);

                //7. 清除Redis缓存
                clearInspectionDataByRedis(config, rawInspection);
                clearInspectionDataBackupByRedis(config, rawInspection);
                //8. 更新原始检验数据状态(标记数据抽样状态)
                LambdaUpdateWrapper<RAW_INSPECTION> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(RAW_INSPECTION::getF_SAMPLING_TASK_CONFIG_ID, config.getF_ID());
                updateWrapper.in(RAW_INSPECTION::getF_ID, selectedData.stream().map(InspectionDataDTO::getId).collect(Collectors.toList()));
                updateWrapper.set(RAW_INSPECTION::getF_SAMPLING_TIME, new Date());
                updateWrapper.set(RAW_INSPECTION::getF_STATUS, YesOrNoEnum.YES.getType());
                rawInspectionMapper.update(null, updateWrapper);
            }
            log.info("抽样任务执行完成，配置: {}", config);
        } catch (Exception e) {
            e.printStackTrace();
            if (CollectionUtils.isNotEmpty(config.getNoticeTypeList())) {
                final String content = rawInspection.buildMsg(e.getMessage());
                final SendMessageDTO insertSubgroupDataCollectException = new SendMessageDTO(config.getNoticeTypeList(), config.getNoticeUserList(), config.getNoticeRoleList(), I18nUtils.getMessage("SAMPLING_TASK_FAIL"), content);
                remoteSendService.sendMsg(insertSubgroupDataCollectException);
            }
            log.error("抽样任务执行失败，配置: {}", config, e);
            throw new BaseException("SAMPLING_TASK_FAIL");
        }
    }


    @Override
    public void expiredPerformSampling(String cacheKey) {

        final String[] split = cacheKey.split(":");
        final String configId = split[split.length - 1];
        String backUpKey = cacheKey.replace(RedisConstant.INSPECTION_CACHE_KEY,RedisConstant.INSPECTION_CACHE_BACKUP_KEY);

        SAMPLING_TASK_CONFIG config = samplingTaskConfigMapper.selectById(configId);
        List<InspectionDataDTO> cachedData =  cachedData = redisService.lGet(backUpKey, 0, -1);
        List<InspectionDataDTO> selectedData =  new ArrayList<>();
        switch (SamplingTypeEnum.getByType(config.getF_SAMPLING_TYPE())) {
            case TIME_BASED:
                selectedData = selectDataByMethod(config, cachedData);
                break;
            case PRODUCTION_BEAT:
                selectedData = selectDataByMethod(config, cachedData);
                break;
            default:
                break;
        }
        // 6. 保存抽样结果
        if (CollectionUtils.isNotEmpty(selectedData)) {
            //子组数据异常通知配置
            String msgId = JudgeUtils.nextId() + "";
            final NoticeConfigVO noticeConfigVO = new NoticeConfigVO();
            noticeConfigVO.setCollectionType(3);
            noticeConfigVO.setNoticeUser(config.getNoticeUserList());
            noticeConfigVO.setMoticeRule(config.getNoticeRoleList());
            noticeConfigVO.setNoticeType(config.getNoticeTypeList());
            redisService.set(RedisConstant.INSPECTION_MESSAGE_KEY + msgId, noticeConfigVO, RedisConstant.INSPECTION_MESSAGE_EXPIRE);
            //添加子组数据
            dataImportService.batchAdd(convertToSubgroupDataVOList(selectedData,config), msgId);

            //7. 清除Redis缓存
            redisService.deleteByKey(backUpKey);
            //8. 更新原始检验数据状态(标记数据抽样状态)
            LambdaUpdateWrapper<RAW_INSPECTION> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RAW_INSPECTION::getF_SAMPLING_TASK_CONFIG_ID, config.getF_ID());
            updateWrapper.in(RAW_INSPECTION::getF_ID, selectedData.stream().map(InspectionDataDTO::getId).collect(Collectors.toList()));
            updateWrapper.set(RAW_INSPECTION::getF_SAMPLING_TIME, new Date());
            updateWrapper.set(RAW_INSPECTION::getF_STATUS, YesOrNoEnum.YES.getType());
            rawInspectionMapper.update(null, updateWrapper);
        }

    }


    private List<SubgroupDataVO> convertToSubgroupDataVOList(List<InspectionDataDTO> selectedData,SAMPLING_TASK_CONFIG config) {
        List<SubgroupDataVO> subgroupDataVOList = new ArrayList<>();
        for (InspectionDataDTO inspectionDataDTO : selectedData) {
            final SubgroupDataVO subgroupDataVO = new SubgroupDataVO();
            if(config.getF_ALARM() == YesOrNoEnum.NO.getType()){
                subgroupDataVO.setHistoricalData(YesOrNoEnum.YES.getType());
            }
            //工厂
            LambdaQueryWrapper<HIERARCHY_INF> hierarchyInfQueryWrapper = new LambdaQueryWrapper<>();
            hierarchyInfQueryWrapper.eq(HIERARCHY_INF::getF_NAME, inspectionDataDTO.getPlantName());
            hierarchyInfQueryWrapper.eq(HIERARCHY_INF::getF_TYPE, HIERARCHY_INFTypeEnum.FACTORY.getType());
            HIERARCHY_INF hierarchyInf = hierarchyInfMapper.selectOne(hierarchyInfQueryWrapper);
            if (ObjectUtils.isEmpty(hierarchyInf)) {
                throw new BusinessException(DataManagementExceptionEnum.FACTORY_NOT_EXISTS);
            }
            subgroupDataVO.setF_PLNT(hierarchyInf.getF_HIER());


            //工艺流程
            LambdaQueryWrapper<MANUFACTURING_PROCESS_INF> mfpsQueryWrapper = new LambdaQueryWrapper<>();
            mfpsQueryWrapper.eq(MANUFACTURING_PROCESS_INF::getF_NAME, inspectionDataDTO.getMfpsName())
                    .eq(MANUFACTURING_PROCESS_INF::getF_DEL, DelFlagEnum.USE.getType());
            MANUFACTURING_PROCESS_INF manufacturingProcessInf = manufacturingProcessInfMapper.selectOne(mfpsQueryWrapper);
            if (manufacturingProcessInf != null) {
                /*判断工艺流程的工厂与所选工厂是否一致*/
                if (!manufacturingProcessInf.getF_PLNT().equals(hierarchyInf.getF_HIER())) {
                    throw new BusinessException(DataManagementExceptionEnum.MFPS_PLNT_INCONFORMITY);
                }
                subgroupDataVO.setF_MFPS(manufacturingProcessInf.getF_MFPS());
            } else {
                throw new BusinessException(DataManagementExceptionEnum.MFPS_NOT_EXISTS);
            }

            //工艺节点
            LambdaQueryWrapper<MANUFACTURING_NODE_INF> mfndQueryWrapper = new LambdaQueryWrapper<>();
            mfndQueryWrapper.eq(MANUFACTURING_NODE_INF::getF_NAME, inspectionDataDTO.getMfndName())
                    .eq(MANUFACTURING_NODE_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                    .eq(MANUFACTURING_NODE_INF::getF_DEL, DelFlagEnum.USE.getType());
            MANUFACTURING_NODE_INF manufacturingNodeInf = manufacturingNodeInfMapper.selectOne(mfndQueryWrapper);
            if (manufacturingNodeInf != null) {
                subgroupDataVO.setF_MFND(manufacturingNodeInf.getF_MFND());
            } else {
                throw new BusinessException(DataManagementExceptionEnum.MFND_NOT_EXISTS);
            }

            //检验计划
            LambdaQueryWrapper<INSPECTION_PLAN_INF> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(INSPECTION_PLAN_INF::getF_NAME, inspectionDataDTO.getPlanName())
                    .eq(INSPECTION_PLAN_INF::getF_MFPS, subgroupDataVO.getF_MFPS())
                    .eq(INSPECTION_PLAN_INF::getF_MFND, subgroupDataVO.getF_MFND())
                    .eq(INSPECTION_PLAN_INF::getF_DEL, DelFlagEnum.USE.getType());
            INSPECTION_PLAN_INF inspectionPlanInf = inspectionPlanInfMapper.selectOne(queryWrapper);
            if (inspectionPlanInf != null) {
                subgroupDataVO.setF_INSP_PLAN(inspectionPlanInf.getF_PLAN());
            } else {
                throw new BusinessException(DataManagementExceptionEnum.PLAN_NOT_EXISTS);
            }

            //子计划
            Map<String, List<INSPECTION_PLAN_CHILD_DTO>> map =
                    JSONObject.parseObject(inspectionPlanInf.getF_CHILD())
                            .entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry ->
                                    JSONArray.parseArray(String.valueOf(entry.getValue()), INSPECTION_PLAN_CHILD_DTO.class)));
            List<INSPECTION_PLAN_CHILD_DTO> inspectionPlanChildDtoList = map.get(inspectionDataDTO.getChildPlanName());
            if (inspectionPlanChildDtoList == null) {
                throw new BusinessException(DataManagementExceptionEnum.CHILD_NOT_EXISTS);
            }
            subgroupDataVO.setChildId(inspectionPlanChildDtoList.get(0).getChildId());

            //产品名称
            if (ObjectUtils.isEmpty(inspectionDataDTO.getPartName())) {
                throw new BusinessException(DataManagementExceptionEnum.PART_IS_NOT_NULL);
            }
            subgroupDataVO.setPartName(inspectionDataDTO.getPartName());
            //产品版本
//            if (ObjectUtils.isEmpty(inspectionDataDTO.getPtrvName())) {
//                throw new BusinessException(DataManagementExceptionEnum.PTRV_IS_NOT_NULL);
//            }
            subgroupDataVO.setPtrvName(inspectionDataDTO.getPtrvName());
            //过程
            if (ObjectUtils.isEmpty(inspectionDataDTO.getPrcsName())) {
                throw new BusinessException(DataManagementExceptionEnum.PRCS_IS_NOT_NULL);
            }
            subgroupDataVO.setPrcsName(inspectionDataDTO.getPrcsName());
            //班次
            subgroupDataVO.setShiftName(inspectionDataDTO.getShiftName());
            //班次组
            subgroupDataVO.setShiftGrpName(inspectionDataDTO.getShiftGrpName());
            //工单
            subgroupDataVO.setJobName(inspectionDataDTO.getJobName());
            //工单组
            subgroupDataVO.setJobGrpName(inspectionDataDTO.getJobGrpName());
            //批次
            subgroupDataVO.setLotName(inspectionDataDTO.getLotName());
            //描述符
            final List<InspectionDataDTO.Descriptor> descriptors = inspectionDataDTO.getDescriptors();
            List<SGRP_DSC> sgrpDscList = new ArrayList<>();
            for (InspectionDataDTO.Descriptor descriptor : descriptors) {
                if (StringUtils.isNotBlank(descriptor.getDescName()) && StringUtils.isNotBlank(descriptor.getDescGrpName())) {
                    SGRP_DSC sgrpDsc = new SGRP_DSC();
                    sgrpDsc.setDescName(descriptor.getDescName());
                    sgrpDsc.setDescGrpName(descriptor.getDescGrpName());
                    sgrpDscList.add(sgrpDsc);
                }
            }
            subgroupDataVO.setSgrpDscList(sgrpDscList);
            //子组时间
            subgroupDataVO.setF_SGTM(inspectionDataDTO.getPushTime());

            //测试
            if (ObjectUtils.isEmpty(inspectionDataDTO.getTestData())) {
                throw new BusinessException(DataManagementExceptionEnum.TEST_IS_NOT_NULL);
            }
            final List<InspectionDataDTO.Test> testData = inspectionDataDTO.getTestData();
            List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = new ArrayList<>();
            for (int i = 0; i < testData.size(); i++) {
                SGRP_VAL_CHILD_DTO sgrpValChildDto = new SGRP_VAL_CHILD_DTO();
                sgrpValChildDto.setTestName(testData.get(i).getTestName());
                sgrpValChildDto.setTestType(testData.get(i).getTestType());
                List<SGRP_VAL_CHILD_DTO.Test> testList = new ArrayList<>();
                final InspectionDataDTO.Test testDatum = testData.get(i);
                for (int i1 = 0; i1 < testDatum.getTestVal().size(); i1++) {
                    final SGRP_VAL_CHILD_DTO.Test test = new SGRP_VAL_CHILD_DTO.Test();
                    final InspectionDataDTO.TestVal testVal = testDatum.getTestVal().get(i1);
                    test.setTestVal(testVal.getVal());
                    test.setTestNo(i1);
                    if (StringUtils.isNotBlank(testDatum.getDefectGroupName())) {
                        test.setDefectGrpName(testDatum.getDefectGroupName());
                    }
                    if (StringUtils.isNotBlank(testDatum.getDefectName())) {
                        test.setDefectName(testDatum.getDefectName());
                    }
                    if (testVal.getImg() != null) {
                        test.setImg(testVal.getImg());
                    }
                    if (CollectionUtils.isNotEmpty(testVal.getSubTestData())) {
                        List<SGRP_VAL_CHILD_DTO.SubTest> subTestList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(testVal.getSubTestData())) {
                            for (int i2 = 0; i2 < testVal.getSubTestData().size(); i2++) {
                                final InspectionDataDTO.SubTest subTestData = testVal.getSubTestData().get(i2);
                                final SGRP_VAL_CHILD_DTO.SubTest subTest = new SGRP_VAL_CHILD_DTO.SubTest();
                                subTest.setSubTestNo(i2);
                                subTest.setSubTestValue(subTestData.getSubTestVal());
                                subTest.setSubImg(subTestData.getSubImg());
                                subTestList.add(subTest);
                            }
                        }
                        test.setSubTestList(subTestList);
                    }
                    testList.add(test);

                }
                sgrpValChildDto.setTestList(testList);
                sgrpValChildDtoList.add(sgrpValChildDto);
            }
            subgroupDataVO.setF_SGSZ(testData.size());
            subgroupDataVO.setSgrpValChildDtoList(sgrpValChildDtoList);
            subgroupDataVOList.add(subgroupDataVO);
        }

        return subgroupDataVOList;
    }

    /**
     * 从缓存中获取检验数据
     *
     * @param config
     * @param rawInspection
     * @return
     */
    private List<InspectionDataDTO> getInspectionDataByRedis(SAMPLING_TASK_CONFIG config, RAW_INSPECTION rawInspection) {
        String cacheKey = RedisConstant.INSPECTION_CACHE_KEY + config.getF_ID();
        List<InspectionDataDTO> cachedData = new ArrayList<>();
        switch (SamplingStrategyEnum.getByType(config.getF_SAMPLING_STRATEGY())) {
            case BY_PRODUCT:
                cachedData = redisService.lGet(cacheKey + ":" + rawInspection.getF_PART_NAME(), 0, -1);
                break;
            case BY_PROCESS:
                cachedData = redisService.lGet(cacheKey + ":" + rawInspection.getF_PRCS_NAME(), 0, -1);
                break;
            case BY_PRODUCT_PROCESS:
                cachedData = redisService.lGet(cacheKey + ":" + rawInspection.getF_PART_NAME() + ":" + rawInspection.getF_PRCS_NAME(), 0, -1);
                break;
            case BY_PUSH_COUNT:
            default:
                cachedData = redisService.lGet(cacheKey, 0, -1);
                break;
        }
        return cachedData;
    }

    /**
     * 清理备份缓存中的检验数据
     *
     * @param config
     * @param rawInspection
     */
    private void clearInspectionDataByRedis(SAMPLING_TASK_CONFIG config, RAW_INSPECTION rawInspection) {
        String cacheKey = RedisConstant.INSPECTION_CACHE_KEY + config.getF_ID();
        switch (SamplingStrategyEnum.getByType(config.getF_SAMPLING_STRATEGY())) {
            case BY_PRODUCT:
                redisService.deleteByKey(cacheKey + ":" + rawInspection.getF_PART_NAME());
                break;
            case BY_PROCESS:
                redisService.deleteByKey(cacheKey + ":" + rawInspection.getF_PRCS_NAME());
                break;
            case BY_PRODUCT_PROCESS:
                redisService.deleteByKey(cacheKey + ":" + rawInspection.getF_PART_NAME() + ":" + rawInspection.getF_PRCS_NAME());
                break;
            case BY_PUSH_COUNT:
            default:
                redisService.deleteByKey(cacheKey);
                break;
        }
    }
    /**
     * 清理缓存中的检验数据
     *
     * @param config
     * @param rawInspection
     */
    private void clearInspectionDataBackupByRedis(SAMPLING_TASK_CONFIG config, RAW_INSPECTION rawInspection) {
        String cacheBackupKey = RedisConstant.INSPECTION_CACHE_BACKUP_KEY + config.getF_ID();
        switch (SamplingStrategyEnum.getByType(config.getF_SAMPLING_STRATEGY())) {
            case BY_PRODUCT:
                redisService.deleteByKey(cacheBackupKey + ":" + rawInspection.getF_PART_NAME());
                break;
            case BY_PROCESS:
                redisService.deleteByKey(cacheBackupKey + ":" + rawInspection.getF_PRCS_NAME());
                break;
            case BY_PRODUCT_PROCESS:
                redisService.deleteByKey(cacheBackupKey + ":" + rawInspection.getF_PART_NAME() + ":" + rawInspection.getF_PRCS_NAME());
                break;
            case BY_PUSH_COUNT:
            default:
                redisService.deleteByKey(cacheBackupKey);
                break;
        }
    }

    /**
     * 根据抽样类型选择数据
     *
     * @param config   抽样配置
     * @param dataList 原始数据列表
     * @return 抽样结果
     */
    private List<InspectionDataDTO> selectDataBySamplingType(SAMPLING_TASK_CONFIG config, List<InspectionDataDTO> dataList) {
        List<InspectionDataDTO> inspectionDataDTOS = new ArrayList<>();
        switch (SamplingTypeEnum.getByType(config.getF_SAMPLING_TYPE())) {
            case TIME_BASED:
                if (isIntervalGreaterOrEqual10Minutes(dataList, config.getF_TIME_INTERVAL())) {
                    inspectionDataDTOS = selectDataByMethod(config, dataList);
                }
                break;
            case PRODUCTION_BEAT:
                if (dataList.size() >= config.getF_PRODUCTION_BEAT_COUNT()) {
                    inspectionDataDTOS = selectDataByMethod(config, dataList);
                }
                break;
            default:
                break;
        }
        return inspectionDataDTOS;
    }


    /**
     * 根据抽样方法选择数据
     *
     * @param config   抽样配置
     * @param dataList 原始数据列表
     * @return 抽样结果
     */
    private List<InspectionDataDTO> selectDataByMethod(SAMPLING_TASK_CONFIG config, List<InspectionDataDTO> dataList) {
        if (dataList.isEmpty()) {
            return dataList;
        }
        final Integer samplingCount = config.getF_SAMPLING_COUNT();
        SamplingMethodEnum method = SamplingMethodEnum.getByType(config.getF_SAMPLING_METHOD());

        switch (method) {
            case TIME_ASC:
                return dataList.stream()
                        .sorted(Comparator.comparing(InspectionDataDTO::getPushTime, Comparator.nullsLast(Comparator.naturalOrder())))
                        .limit(samplingCount)
                        .collect(Collectors.toList());

            case TIME_DESC:
                return dataList.stream()
                        .sorted(Comparator.comparing(InspectionDataDTO::getPushTime, Comparator.nullsFirst(Comparator.reverseOrder())))
                        .limit(samplingCount)
                        .collect(Collectors.toList());

            case RANDOM:
            default:
                return randomSample(dataList, samplingCount);
        }
    }

    /**
     * 检查数据是否满足时间间隔条件
     */
    public boolean isIntervalGreaterOrEqual10Minutes(List<InspectionDataDTO> dataList, Integer interval) {
        // 处理空集合或只有一条数据的情况
        if (dataList == null || dataList.size() < 2) {
            log.info("数据量不足2条，无法计算时间间隔");
            return false;
        }

        // 提取所有非空的pushTime
        List<Date> pushTimes = dataList.stream()
                .map(InspectionDataDTO::getPushTime)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 如果有效时间不足2个，无法计算间隔
        if (pushTimes.size() < 2) {
            log.info("有效时间不足2个，无法计算时间间隔");
            return false;
        }

        // 找到最小时间和最大时间
        Optional<Date> minTime = pushTimes.stream().min(Comparator.naturalOrder());
        Optional<Date> maxTime = pushTimes.stream().max(Comparator.naturalOrder());

        // 计算时间差（毫秒）
        long timeDiffMs = maxTime.get().getTime() - minTime.get().getTime();

        // 判断是否大于等于10分钟
        return timeDiffMs >= interval*1000*60;
    }

    /**
     * 随机抽样
     */
    private List<InspectionDataDTO> randomSample(List<InspectionDataDTO> dataList, int samplingCount) {
        List<InspectionDataDTO> shuffledList = new ArrayList<>(dataList);
        Collections.shuffle(shuffledList, new Random());
        return shuffledList.subList(0, Math.min(samplingCount, shuffledList.size()));
    }

    /**
     * 验证抽样配置
     */
    public boolean validateConfig(SAMPLING_TASK_CONFIG config) {
        if (config == null) {
            log.error("抽样配置为空");
            return false;
        }

        if (config.getF_SAMPLING_COUNT() == null || config.getF_SAMPLING_COUNT() <= 0) {
            log.error("抽样数量配置无效: {}", config.getF_SAMPLING_COUNT());
            return false;
        }

        if (config.getF_SAMPLING_STRATEGY() == null || config.getF_SAMPLING_STRATEGY() < 1 || config.getF_SAMPLING_STRATEGY() > 6) {
            log.error("抽样策略配置无效: {}", config.getF_SAMPLING_STRATEGY());
            return false;
        }

        if (config.getF_SAMPLING_METHOD() == null || config.getF_SAMPLING_METHOD() < 1 || config.getF_SAMPLING_METHOD() > 3) {
            log.error("抽样方法配置无效: {}", config.getF_SAMPLING_METHOD());
            return false;
        }

        return true;
    }


}
