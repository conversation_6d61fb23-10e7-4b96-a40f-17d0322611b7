package com.yingfei.dataCollection.controller;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataCollection.service.SamplingService;
import com.yingfei.entity.requestEntity.SamplingRequestEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "检验数据采集抽样API")
@RestController
@RequestMapping("/sampling")
public class SamplingController {
    @Resource
    private SamplingService samplingService;

    @ApiOperation("执行抽样任务")
    @PostMapping("/performSampling")
    public R<?> performSampling(@RequestBody SamplingRequestEntity samplingRequestEntity){
        samplingService.performSampling(samplingRequestEntity.getConfig(), samplingRequestEntity.getRawInspection());
        return R.ok();
    }
}
