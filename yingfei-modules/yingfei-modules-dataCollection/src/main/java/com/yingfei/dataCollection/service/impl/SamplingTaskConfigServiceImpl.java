package com.yingfei.dataCollection.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataCollection.mapper.SAMPLING_TASK_CONFIGMapper;
import com.yingfei.dataCollection.service.SAMPLING_TASK_CONFIGService;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import com.yingfei.entity.dto.SAMPLING_TASK_CONFIG_DTO;
import com.yingfei.entity.enums.SamplingMethodEnum;
import com.yingfei.entity.enums.SamplingStrategyEnum;
import com.yingfei.entity.enums.SamplingTypeEnum;
import com.yingfei.entity.vo.SAMPLING_TASK_CONFIG_VO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SamplingTaskConfigServiceImpl extends ServiceImpl<SAMPLING_TASK_CONFIGMapper, SAMPLING_TASK_CONFIG>
        implements SAMPLING_TASK_CONFIGService {

    @Override
    public TableDataInfo<SAMPLING_TASK_CONFIG_DTO> getListPage(SAMPLING_TASK_CONFIG_VO vo) {
        Page<SAMPLING_TASK_CONFIG> page = SAMPLING_TASK_CONFIG_VO.convertToPage(vo.getOffset(),vo.getNext());
        LambdaQueryWrapper<SAMPLING_TASK_CONFIG> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(vo.getF_NAME()), SAMPLING_TASK_CONFIG::getF_NAME, vo.getF_NAME());
        page = baseMapper.selectPage(page, queryWrapper);
        List<SAMPLING_TASK_CONFIG> list = page.getRecords();
        List<SAMPLING_TASK_CONFIG_DTO> dtoList = BeanUtil.copyToList(list, SAMPLING_TASK_CONFIG_DTO.class);
        dtoList.forEach(this::setOtherInfo);
        return new TableDataInfo<>(dtoList, page.getTotal());
    }


    @Override
    public SAMPLING_TASK_CONFIG_DTO getInfo(Long id) {
        SAMPLING_TASK_CONFIG entity = this.getById(id);
        SAMPLING_TASK_CONFIG_DTO dto = BeanUtil.copyProperties(entity, SAMPLING_TASK_CONFIG_DTO.class);
        setOtherInfo(dto);
        return dto;
    }

    @Override
    public void add(SAMPLING_TASK_CONFIG_VO vo) {
        checkParam(vo);
        SAMPLING_TASK_CONFIG entity = BeanUtil.copyProperties(vo, SAMPLING_TASK_CONFIG.class);
        this.save(entity);
    }

    @Override
    public void edit(SAMPLING_TASK_CONFIG_VO vo) {
        checkParam(vo);
        SAMPLING_TASK_CONFIG entity = BeanUtil.copyProperties(vo, SAMPLING_TASK_CONFIG.class);
        this.updateById(entity);
    }

    @Override
    public void del(SAMPLING_TASK_CONFIG_VO vo) {
        vo.getIds().forEach(id -> {
            SAMPLING_TASK_CONFIG entity = this.getById(id);
            entity.setF_DEL(DelFlagEnum.DELETE.getType());
            this.updateById(entity);
        });
    }

    public void checkParam(SAMPLING_TASK_CONFIG_VO vo) {
        if (StringUtils.isEmpty(vo.getF_NAME())) {
                        throw new BaseException("配置名称不能为空");
        }
    }

    private void setOtherInfo(SAMPLING_TASK_CONFIG_DTO dto) {
        if (dto.getF_SAMPLING_TYPE() != null) {
            SamplingTypeEnum samplingTypeEnum = SamplingTypeEnum.getByType(dto.getF_SAMPLING_TYPE());
            if (samplingTypeEnum != null) {
                dto.setSamplingTypeName(samplingTypeEnum.getDescription());
            }
        }
        if (dto.getF_SAMPLING_STRATEGY() != null) {
            SamplingStrategyEnum samplingStrategyEnum = SamplingStrategyEnum.getByType(dto.getF_SAMPLING_STRATEGY());
            if (samplingStrategyEnum != null) {
                dto.setSamplingStrategyName(samplingStrategyEnum.getDescription());
            }
        }
        if (dto.getF_SAMPLING_METHOD() != null) {
            SamplingMethodEnum samplingMethodEnum = SamplingMethodEnum.getByType(dto.getF_SAMPLING_METHOD());
            if (samplingMethodEnum != null) {
                dto.setSamplingMethodName(samplingMethodEnum.getDescription());
            }
        }
    }
}
