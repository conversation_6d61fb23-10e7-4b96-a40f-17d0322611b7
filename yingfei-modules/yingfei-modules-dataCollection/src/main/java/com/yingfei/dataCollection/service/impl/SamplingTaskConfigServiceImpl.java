package com.yingfei.dataCollection.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.DelFlagEnum;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringToListUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.dataCollection.mapper.SAMPLING_TASK_CONFIGMapper;
import com.yingfei.dataCollection.service.SAMPLING_TASK_CONFIGService;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.ROLE_INF_DTO;
import com.yingfei.entity.dto.SAMPLING_TASK_CONFIG_DTO;
import com.yingfei.entity.enums.NOTIFICATION_TYPEEnum;
import com.yingfei.entity.enums.SamplingMethodEnum;
import com.yingfei.entity.enums.SamplingStrategyEnum;
import com.yingfei.entity.enums.SamplingTypeEnum;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.entity.vo.ROLE_INF_VO;
import com.yingfei.entity.vo.SAMPLING_TASK_CONFIG_VO;
import com.yingfei.system.api.RemoteRoleService;
import com.yingfei.system.api.RemoteUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SamplingTaskConfigServiceImpl extends ServiceImpl<SAMPLING_TASK_CONFIGMapper, SAMPLING_TASK_CONFIG>
        implements SAMPLING_TASK_CONFIGService {
    @Resource
    RemoteUserService remoteUserService;
    @Resource
    RemoteRoleService remoteRoleService;

    @Override
    public TableDataInfo<SAMPLING_TASK_CONFIG_DTO> getListPage(SAMPLING_TASK_CONFIG_VO vo) {
        Page<SAMPLING_TASK_CONFIG> page = SAMPLING_TASK_CONFIG_VO.convertToPage(vo.getOffset(),vo.getNext());
        LambdaQueryWrapper<SAMPLING_TASK_CONFIG> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(vo.getF_NAME()), SAMPLING_TASK_CONFIG::getF_NAME, vo.getF_NAME());
        page = baseMapper.selectPage(page, queryWrapper);
        List<SAMPLING_TASK_CONFIG> list = page.getRecords();
        List<SAMPLING_TASK_CONFIG_DTO> dtoList = BeanUtil.copyToList(list, SAMPLING_TASK_CONFIG_DTO.class);
        List<Long> emplList = new ArrayList<>();
        for (SAMPLING_TASK_CONFIG_DTO samplingTaskConfigDto : dtoList) {
            if(StringUtils.isNotEmpty(samplingTaskConfigDto.getF_NOTICE_TYPE())){
                samplingTaskConfigDto.setNoticeTypeList(StringToListUtils.convertToInteger(samplingTaskConfigDto.getF_NOTICE_TYPE(),","));
            }
            if(StringUtils.isNotEmpty(samplingTaskConfigDto.getF_NOTICE_USER())){
                samplingTaskConfigDto.setNoticeUserList(StringToListUtils.convertToLong(samplingTaskConfigDto.getF_NOTICE_USER(),","));
                emplList.addAll(samplingTaskConfigDto.getNoticeUserList());
            }
            if(StringUtils.isNotEmpty(samplingTaskConfigDto.getF_NOTICE_ROLE())){
                samplingTaskConfigDto.setNoticeRoleList(StringToListUtils.convertToLong(samplingTaskConfigDto.getF_NOTICE_ROLE(),","));
            }
        }
        emplList.addAll(list.stream().map(SAMPLING_TASK_CONFIG::getF_CRUE).collect(Collectors.toList()));
        emplList.addAll(list.stream().map(SAMPLING_TASK_CONFIG::getF_EDUE).collect(Collectors.toList()));
        /*获取创建人名称*/
        Map<Long, String> emplInfDtoCreateMap =getEmplMap(emplList);
        final List<List<Long>> collect1 = dtoList.stream().map(SAMPLING_TASK_CONFIG_DTO::getNoticeRoleList).filter(CollectionUtils::isNotEmpty).collect(Collectors.toList());
        List<Long> roleList = new ArrayList<>();
        for (List<Long> longs : collect1) {
            roleList.addAll(longs);
        }
        final Map<Long, String> roleMap = getRoleMap(roleList);
        dtoList.forEach(dto -> {
            if(emplInfDtoCreateMap != null){
                dto.setCreateName(emplInfDtoCreateMap.get(dto.getF_CRUE()));
                dto.setUpdateName(emplInfDtoCreateMap.get(dto.getF_EDUE()));
            List<String> noticeUserNames = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(dto.getNoticeUserList())){
                for (Long emplId : dto.getNoticeUserList()) {
                    noticeUserNames.add(emplInfDtoCreateMap.get(emplId));
                }
                dto.setNoticeUserNames(noticeUserNames);
            }
            }
            List<String> noticeRoleNames = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(dto.getNoticeRoleList()) && roleMap != null) {
                for (Long roleId : dto.getNoticeRoleList()) {
                    noticeRoleNames.add(roleMap.get(roleId));
                }
                dto.setNoticeRoleNames(noticeRoleNames);
            }
        });

        dtoList.forEach(this::setOtherInfo);
        return new TableDataInfo<>(dtoList, page.getTotal());
    }

    public Map<Long, String> getEmplMap(List<Long> emplIdList){
        if (CollectionUtils.isEmpty(emplIdList)){
            return Collections.emptyMap();
        }
        final EMPL_INF_VO emplInfVo = new EMPL_INF_VO();
        emplInfVo.setIds(emplIdList);
        final List<EMPL_INF_DTO> emplInfs = remoteUserService.getList(emplInfVo).getData();

        if(!CollectionUtils.isEmpty(emplInfs)){
            Map<Long, String> emplMap = emplInfs.stream()
                    .collect(Collectors.toMap(
                            EMPL_INF_DTO::getF_EMPL,
                            EMPL_INF_DTO::getF_NAME,
                            (existing, replacement) -> replacement
                    ));
            return emplMap;
        }
        return null;
    }
    public Map<Long, String> getRoleMap(List<Long> roleIdList){
        if (CollectionUtils.isEmpty(roleIdList)){
            return Collections.emptyMap();
        }
        final ROLE_INF_VO roleInfVo = new ROLE_INF_VO();
        roleInfVo.setRoleIds(roleIdList);
        final R<List<ROLE_INF_DTO>> roleInfs = remoteRoleService.selectByRoleIds(roleInfVo);
        if (roleInfs.getCode() == Constants.SUCCESS && CollectionUtils.isNotEmpty(roleInfs.getData())) {
            Map<Long, String> roleMap = roleInfs.getData().stream()
                    .collect(Collectors.toMap(
                            ROLE_INF_DTO::getF_ROLE,
                            ROLE_INF_DTO::getF_NAME,
                            (existing, replacement) -> replacement
                    ));
            return roleMap;
        }
        return null;
    }

    @Override
    public SAMPLING_TASK_CONFIG_DTO getInfo(Long id) {
        SAMPLING_TASK_CONFIG entity = this.getById(id);
        SAMPLING_TASK_CONFIG_DTO dto = BeanUtil.copyProperties(entity, SAMPLING_TASK_CONFIG_DTO.class);
        setOtherInfo(dto);
        return dto;
    }

    @Override
    public void add(SAMPLING_TASK_CONFIG_VO vo) {
        checkParam(vo);
        SAMPLING_TASK_CONFIG entity = BeanUtil.copyProperties(vo, SAMPLING_TASK_CONFIG.class);
        entity.setF_NOTICE_TYPE(StringUtils.join(vo.getNoticeTypeList(), ","));
        entity.setF_NOTICE_USER(StringUtils.join(vo.getNoticeUserList(), ","));
        entity.setF_NOTICE_ROLE(StringUtils.join(vo.getNoticeRoleList(), ","));
        this.save(entity);
    }

    @Override
    public void edit(SAMPLING_TASK_CONFIG_VO vo) {
        checkParam(vo);
        SAMPLING_TASK_CONFIG entity = BeanUtil.copyProperties(vo, SAMPLING_TASK_CONFIG.class);
        entity.setF_NOTICE_TYPE(StringUtils.join(vo.getNoticeTypeList(), ","));
        entity.setF_NOTICE_USER(StringUtils.join(vo.getNoticeUserList(), ","));
        entity.setF_NOTICE_ROLE(StringUtils.join(vo.getNoticeRoleList(), ","));
        this.updateById(entity);
    }

    @Override
    public void del(SAMPLING_TASK_CONFIG_VO vo) {
        vo.getIds().forEach(id -> {
            SAMPLING_TASK_CONFIG entity = this.getById(id);
            entity.setF_DEL(DelFlagEnum.DELETE.getType());
            this.updateById(entity);
        });
    }

    public void checkParam(SAMPLING_TASK_CONFIG_VO vo) {
        if (StringUtils.isEmpty(vo.getF_NAME())) {
                        throw new BaseException("SAMPLE_CONFIG_NAME_NOT_NULL");
        }
        if (ObjectUtils.isEmpty(vo.getF_SAMPLING_TYPE())) {
            throw new BaseException("SAMPLING_TYPE_NOT_NULL");
        }
        if (ObjectUtils.isEmpty(vo.getF_SAMPLING_STRATEGY())) {
            throw new BaseException("SAMPLING_STRATEGY_NOT_NULL");
        }
        if (ObjectUtils.isEmpty(vo.getF_SAMPLING_METHOD())) {
            throw new BaseException("SAMPLING_METHOD_NOT_NULL");
        }
        if (ObjectUtils.isEmpty(vo.getF_PLNT())) {
            throw new BaseException("PLANT_NOT_NULL");
        }

        if(SamplingTypeEnum.TIME_BASED.getType().equals(vo.getF_SAMPLING_TYPE())) {
            if (ObjectUtils.isEmpty(vo.getF_TIME_INTERVAL())) {
                throw new BaseException("TIME_INTERVAL_NOT_NULL");
            }
        }
        if(SamplingTypeEnum.PRODUCTION_BEAT.getType().equals(vo.getF_SAMPLING_TYPE())) {
            if (ObjectUtils.isEmpty(vo.getF_PRODUCTION_BEAT_COUNT())) {
                throw new BaseException("PRODUCTION_BEAT_COUNT_NOT_NULL");
            }
        }
        if(ObjectUtils.isEmpty(vo.getF_EX_TIME())){
                throw new BaseException("EX_TIME_NOT_NULL");
        }
        if(ObjectUtils.isEmpty(vo.getNoticeUserList()) && ObjectUtils.isEmpty(vo.getNoticeRoleList())){
            throw new BaseException("NOTICE_USER_AND_ROLE_CANNOT_BE_EMPTY");
        }


    }

    private void setOtherInfo(SAMPLING_TASK_CONFIG_DTO dto) {
        if (dto.getF_SAMPLING_TYPE() != null) {
            SamplingTypeEnum samplingTypeEnum = SamplingTypeEnum.getByType(dto.getF_SAMPLING_TYPE());
            if (samplingTypeEnum != null) {
                dto.setSamplingTypeName(samplingTypeEnum.getDescription());
            }
        }
        if (dto.getF_SAMPLING_STRATEGY() != null) {
            SamplingStrategyEnum samplingStrategyEnum = SamplingStrategyEnum.getByType(dto.getF_SAMPLING_STRATEGY());
            if (samplingStrategyEnum != null) {
                dto.setSamplingStrategyName(samplingStrategyEnum.getDescription());
            }
        }
        if (dto.getF_SAMPLING_METHOD() != null) {
            SamplingMethodEnum samplingMethodEnum = SamplingMethodEnum.getByType(dto.getF_SAMPLING_METHOD());
            if (samplingMethodEnum != null) {
                dto.setSamplingMethodName(samplingMethodEnum.getDescription());
            }
        }
        if (StringUtils.isNotEmpty(dto.getNoticeTypeList())) {
            List<String> noticeTypeNames = new ArrayList<>();
            for (Integer i : dto.getNoticeTypeList()) {
                NOTIFICATION_TYPEEnum notificationTypeEnum = NOTIFICATION_TYPEEnum.getType(i);
                if (notificationTypeEnum != null) {
                    noticeTypeNames.add(I18nUtils.getMessage(notificationTypeEnum.toString()));
                }
            }
            dto.setNoticeTypeNames(noticeTypeNames);
        }

    }
}
