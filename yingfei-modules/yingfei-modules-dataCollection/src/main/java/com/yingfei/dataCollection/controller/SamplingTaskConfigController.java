package com.yingfei.dataCollection.controller;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.log.annotation.Log;
import com.yingfei.common.log.enums.BusinessType;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.common.security.annotation.RequiresPermissions;
import com.yingfei.dataCollection.service.SAMPLING_TASK_CONFIGService;
import com.yingfei.entity.dto.SAMPLING_TASK_CONFIG_DTO;
import com.yingfei.entity.vo.SAMPLING_TASK_CONFIG_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "抽样任务配置API")
@Slf4j
@RestController
@RequestMapping("/sampling_task_config")
public class SamplingTaskConfigController extends BaseController {

    @Resource
    private SAMPLING_TASK_CONFIGService samplingTaskConfigService;

    /**
     * 获取抽样任务配置列表
     */
    @ApiOperation("获取抽样任务配置列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody SAMPLING_TASK_CONFIG_VO vo) {
        return samplingTaskConfigService.getListPage(vo);
    }

    /**
     * 获取抽样任务配置信息
     */
    @ApiOperation("获取抽样任务配置信息")
    @PostMapping("/info/{id}")
    public R<?> info(@PathVariable("id") Long id) {
        SAMPLING_TASK_CONFIG_DTO info = samplingTaskConfigService.getInfo(id);
        return R.ok(info);
    }

    /**
     * 新增抽样任务配置
     */
    @CreateUpdateBy
    @NotResubmit
    @RequiresPermissions("dataManagement:samplingTaskConfig:add")
    @Log(title = "抽样任务配置管理", businessType = BusinessType.INSERT)
    @ApiOperation("新增抽样任务配置")
    @PostMapping("/add")
    public R<?> add(@RequestBody SAMPLING_TASK_CONFIG_VO vo) {
        samplingTaskConfigService.add(vo);
        return R.ok();
    }

    /**
     * 修改抽样任务配置
     */
    @RequiresPermissions("dataManagement:samplingTaskConfig:edit")
    @Log(title = "抽样任务配置管理", businessType = BusinessType.UPDATE)
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改抽样任务配置")
    @PostMapping("/edit")
    public R<?> edit(@RequestBody SAMPLING_TASK_CONFIG_VO vo) {
        samplingTaskConfigService.edit(vo);
        return R.ok();
    }

    /**
     * 批量删除抽样任务配置
     */
    @RequiresPermissions("dataManagement:samplingTaskConfig:remove")
    @Log(title = "抽样任务配置管理", businessType = BusinessType.DELETE)
    @ApiOperation("删除抽样任务配置")
    @DeleteMapping("/remove")
    public R<?> remove(@RequestBody SAMPLING_TASK_CONFIG_VO vo) {
        samplingTaskConfigService.del(vo);
        return R.ok();
    }
}
