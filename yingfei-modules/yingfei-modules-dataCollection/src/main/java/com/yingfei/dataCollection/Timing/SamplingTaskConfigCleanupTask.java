package com.yingfei.dataCollection.Timing;

import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.redis.service.RedisLock;
import com.yingfei.dataCollection.service.SAMPLING_TASK_CONFIGService;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 抽样任务配置清理定时任务
 * 每天凌晨4点执行，删除超过指定天数的抽样任务配置数据
 */
@SuppressWarnings("AlibabaRemoveCommentedCode")
@Component
@Slf4j
public class SamplingTaskConfigCleanupTask {

    @Resource
    private RedisLock redisLock;

    @Resource
    private SAMPLING_TASK_CONFIGService samplingTaskConfigService;

//    @Resource
//    private RemoteGlobalConfigInfService remoteGlobalConfigInfService;

    /**
     * 清理间隔(天)
     */
    @Value("${samplingData.clearInterval:7}")
    private Integer clearInterval;
    /**
     * 是否开启清理任务
     */
    @Value("${samplingData.isOpen:false}")
    private Boolean isOpen;
    /**
     * 抽样任务配置清理锁
     */
    public static final String SAMPLING_TASK_CONFIG_CLEANUP_LOCK = "spc:dataCollection:samplingClearLock";
    /**
     * 每天凌晨4点执行抽样任务配置清理任务
     * cron表达式：秒 分 时 日 月 周
     * 0 0 4 * * ? 表示每天凌晨4点执行
     */
    @Transactional(rollbackFor = Exception.class)
    @Scheduled(cron = "0 0 4 * * ?")
    public void cleanupSamplingTaskConfig() {
        if (!isOpen) {
            log.info("抽样任务配置清理任务已关闭，跳过本次执行");
            return;
        }
        boolean lock = redisLock.getLock(SAMPLING_TASK_CONFIG_CLEANUP_LOCK, null, 300, TimeUnit.SECONDS);
        if (!lock) {
            log.info("抽样任务配置清理任务已在其他实例执行中，跳过本次执行");
            return;
        }

        try {
            log.info("开始执行抽样任务配置清理任务");
//            SysyemGlobalConfig systemConfig = null;
//            try {
//                systemConfig = remoteGlobalConfigInfService.getSystemConfig().getData();
//            } catch (Exception e) {
//                log.error("获取系统配置信息失败 ex:{}", e.getMessage(), e);
//                throw new BusinessException(CommonExceptionEnum.FEIGN_ERROR);
//            }
//
//            // 从系统配置中获取抽样任务配置保留天数
//            final Integer samplingTaskConfigSaveDay = systemConfig.getBasicConfig().getMsgLogSaveDay();
//            log.info("抽样任务配置保留天数配置：{} 天", samplingTaskConfigSaveDay);
            log.info("抽样任务配置保留天数配置：{} 天", clearInterval);

            // 计算删除的截止时间（当前时间减去保留天数）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
//            calendar.add(Calendar.DAY_OF_MONTH, -samplingTaskConfigSaveDay);
            calendar.add(Calendar.DAY_OF_MONTH, -clearInterval);
            Date beforeDate = calendar.getTime();

            log.info("将删除 {} 之前的抽样任务配置数据", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, beforeDate));

            // 执行删除操作
            samplingTaskConfigService.lambdaUpdate()
                    .le(SAMPLING_TASK_CONFIG::getF_CRTM, beforeDate)
                    .remove();

            log.info("抽样任务配置清理任务执行完成");
        } catch (Exception e) {
            log.error("抽样任务配置清理任务执行失败", e);
            throw e;
        } finally {
            // 释放分布式锁
            try {
                redisLock.releaseLock(SAMPLING_TASK_CONFIG_CLEANUP_LOCK);
                log.debug("抽样任务配置清理任务锁已释放");
            } catch (Exception e) {
                log.warn("释放抽样任务配置清理任务锁时发生异常", e);
            }
        }
    }
}
