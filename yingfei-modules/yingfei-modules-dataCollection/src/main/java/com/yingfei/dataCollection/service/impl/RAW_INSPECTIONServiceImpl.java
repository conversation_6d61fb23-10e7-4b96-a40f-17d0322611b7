package com.yingfei.dataCollection.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yingfei.dataCollection.mapper.RAW_INSPECTIONMapper;
import com.yingfei.dataCollection.service.RAW_INSPECTIONService;
import com.yingfei.entity.domain.RAW_INSPECTION;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【t_raw_inspection(原始检验数据表)】的数据库操作Service实现
 * @createDate 2024-07-15 10:00:00
 */
@Service
public class RAW_INSPECTIONServiceImpl extends ServiceImpl<RAW_INSPECTIONMapper, RAW_INSPECTION>
        implements RAW_INSPECTIONService {
}

