package com.yingfei.dataCollection.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import com.yingfei.entity.dto.SAMPLING_TASK_CONFIG_DTO;
import com.yingfei.entity.vo.SAMPLING_TASK_CONFIG_VO;

/**
 * <AUTHOR>
 * @description 针对表【SAMPLING_TASK_CONFIG(抽样任务配置表)】的数据库操作Service
 * @createDate 2024-07-15 10:00:00
 */
public interface SAMPLING_TASK_CONFIGService extends IService<SAMPLING_TASK_CONFIG>{

    SAMPLING_TASK_CONFIG_DTO getInfo(Long id);

    void add(SAMPLING_TASK_CONFIG_VO vo);

    void edit(SAMPLING_TASK_CONFIG_VO vo);

    void del(SAMPLING_TASK_CONFIG_VO vo);
    TableDataInfo<SAMPLING_TASK_CONFIG_DTO> getListPage(SAMPLING_TASK_CONFIG_VO vo);
}
