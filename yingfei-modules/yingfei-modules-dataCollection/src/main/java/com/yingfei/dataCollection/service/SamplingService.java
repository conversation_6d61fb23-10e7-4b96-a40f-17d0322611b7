package com.yingfei.dataCollection.service;

import com.yingfei.entity.domain.RAW_INSPECTION;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;

/**
 * 抽样服务类
 * 负责处理检验数据的抽样逻辑
 */

public interface SamplingService {
    /**
     * 执行抽样任务
     *
     * @param config        抽样配置
     * @param rawInspection 原始检验数据
     */
     void performSampling(SAMPLING_TASK_CONFIG config, RAW_INSPECTION rawInspection);
    public void  expiredPerformSampling(String key);
}
