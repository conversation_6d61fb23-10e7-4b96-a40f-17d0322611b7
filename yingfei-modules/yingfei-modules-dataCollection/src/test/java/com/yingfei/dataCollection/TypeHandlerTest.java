package com.yingfei.dataCollection;

import com.yingfei.dataCollection.mapper.SAMPLING_TASK_CONFIGMapper;
import com.yingfei.entity.domain.SAMPLING_TASK_CONFIG;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * TypeHandler 测试类
 * 用于验证 LongListTypeHandler 和 IntegerListTypeHandler 是否正常工作
 */
@SpringBootTest
@Slf4j
public class TypeHandlerTest {

    @Resource
    private SAMPLING_TASK_CONFIGMapper samplingTaskConfigMapper;

    /**
     * 测试 LongListTypeHandler 的插入和查询
     */
    @Test
    public void testLongListTypeHandler() {
        // 创建测试数据
        SAMPLING_TASK_CONFIG config = new SAMPLING_TASK_CONFIG();
        config.setF_NAME("TypeHandler测试配置");
        config.setF_DESCRIPTION("测试LongListTypeHandler");
        config.setF_SAMPLING_TYPE(1);
        config.setF_SAMPLING_COUNT(10);
        config.setF_SAMPLING_STRATEGY(1);
        config.setF_SAMPLING_METHOD(1);
        config.setF_ENABLED(1);
        
        // 设置 List<Long> 字段
        List<Long> noticeUsers = Arrays.asList(1001L, 1002L, 1003L);
        List<Long> noticeRoles = Arrays.asList(2001L, 2002L);
        config.setF_NOTICE_USER(noticeUsers);
        config.setF_MOTICE_RULE(noticeRoles);
        
        // 设置 List<Integer> 字段
        List<Integer> noticeTypes = Arrays.asList(1, 2, 3);
        config.setF_NOTICE_TYPE(noticeTypes);

        try {
            // 插入数据
            int result = samplingTaskConfigMapper.insert(config);
            log.info("插入结果: {}, 生成的ID: {}", result, config.getF_ID());

            // 查询数据验证
            SAMPLING_TASK_CONFIG queryResult = samplingTaskConfigMapper.selectById(config.getF_ID());
            if (queryResult != null) {
                log.info("查询到的通知用户: {}", queryResult.getF_NOTICE_USER());
                log.info("查询到的通知角色: {}", queryResult.getF_MOTICE_RULE());
                log.info("查询到的通知类型: {}", queryResult.getF_NOTICE_TYPE());
                
                // 验证数据是否正确
                assert queryResult.getF_NOTICE_USER().equals(noticeUsers) : "通知用户数据不匹配";
                assert queryResult.getF_MOTICE_RULE().equals(noticeRoles) : "通知角色数据不匹配";
                assert queryResult.getF_NOTICE_TYPE().equals(noticeTypes) : "通知类型数据不匹配";
                
                log.info("TypeHandler 测试通过！");
            } else {
                log.error("查询结果为空");
            }

        } catch (Exception e) {
            log.error("TypeHandler 测试失败", e);
            throw e;
        }
    }

    /**
     * 测试空值和 null 值处理
     */
    @Test
    public void testNullAndEmptyValues() {
        SAMPLING_TASK_CONFIG config = new SAMPLING_TASK_CONFIG();
        config.setF_NAME("空值测试配置");
        config.setF_DESCRIPTION("测试空值和null值处理");
        config.setF_SAMPLING_TYPE(1);
        config.setF_SAMPLING_COUNT(5);
        config.setF_SAMPLING_STRATEGY(1);
        config.setF_SAMPLING_METHOD(1);
        config.setF_ENABLED(1);
        
        // 设置 null 值
        config.setF_NOTICE_USER(null);
        config.setF_MOTICE_RULE(Arrays.asList()); // 空列表
        config.setF_NOTICE_TYPE(null);

        try {
            int result = samplingTaskConfigMapper.insert(config);
            log.info("空值测试插入结果: {}", result);

            SAMPLING_TASK_CONFIG queryResult = samplingTaskConfigMapper.selectById(config.getF_ID());
            if (queryResult != null) {
                log.info("查询到的通知用户(null): {}", queryResult.getF_NOTICE_USER());
                log.info("查询到的通知角色(empty): {}", queryResult.getF_MOTICE_RULE());
                log.info("查询到的通知类型(null): {}", queryResult.getF_NOTICE_TYPE());
                
                log.info("空值测试通过！");
            }

        } catch (Exception e) {
            log.error("空值测试失败", e);
            throw e;
        }
    }
}
