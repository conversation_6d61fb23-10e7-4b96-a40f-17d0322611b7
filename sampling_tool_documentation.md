# 抽样工具使用文档

## 概述

基于 `SAMPLING_TASK_CONFIG` 配置的智能抽样工具，支持多种抽样策略、方法和触发条件。

## 核心组件

### 1. SamplingTool (抽样工具类)
- **位置**: `yingfei-modules-dataCollection/src/main/java/com/yingfei/dataCollection/utils/SamplingTool.java`
- **功能**: 实现核心抽样算法逻辑

### 2. SamplingService (抽样服务类)
- **位置**: `yingfei-modules-dataCollection/src/main/java/com/yingfei/dataCollection/service/SamplingService.java`
- **功能**: 业务逻辑封装，Redis缓存管理

### 3. SamplingTriggerConsumer (抽样触发消费者)
- **位置**: `yingfei-modules-mq/src/main/java/com/yingfei/mq/consumer/SamplingTriggerConsumer.java`
- **功能**: 处理MQ触发消息

### 4. SamplingController (抽样控制器)
- **位置**: `yingfei-modules-dataCollection/src/main/java/com/yingfei/dataCollection/controller/SamplingController.java`
- **功能**: 提供REST API接口

## 配置参数说明

### 抽样类型 (F_SAMPLING_TYPE)
- `1` - 时间抽样：基于时间间隔触发
- `2` - 生产节拍：基于生产数量触发

### 抽样策略 (F_SAMPLING_STRATEGY)
- `1` - 基于产品：只抽样指定产品的数据
- `2` - 基于过程：只抽样指定过程的数据
- `3` - 基于测试：只抽样指定测试的数据
- `4` - 基于产品过程测试：同时匹配产品、过程、测试
- `5` - 基于产品测试：匹配产品和测试
- `6` - 基于推送数量：不过滤，按推送数量抽样

### 抽样方法 (F_SAMPLING_METHOD)
- `1` - 随机抽样：随机选择样本
- `2` - 时间升序：按时间正序选择最早的样本
- `3` - 时间倒序：按时间倒序选择最新的样本

## API接口

### 1. 手动触发抽样
```http
POST /sampling/trigger/{configId}
```
**参数**: 
- `configId`: 抽样配置ID

**响应**:
```json
{
  "code": 200,
  "msg": "抽样执行成功",
  "data": {
    "sampledCount": 10,
    "resultKey": "sampling:result:123:1640995200000"
  }
}
```

### 2. 获取抽样结果
```http
GET /sampling/result/{resultKey}
```
**参数**:
- `resultKey`: 抽样结果键

**响应**:
```json
{
  "code": 200,
  "data": [
    {
      "samplingTaskConfigId": 123,
      "partName": "产品A",
      "testName": "测试1",
      "data": [...],
      "pushTime": "2021-12-31T12:00:00"
    }
  ]
}
```

### 3. 获取抽样统计信息
```http
GET /sampling/stats/{configId}
```
**响应**:
```json
{
  "code": 200,
  "data": {
    "configId": 123,
    "configName": "产品A抽样配置",
    "currentCount": 8,
    "targetCount": 10,
    "cacheSize": 8,
    "enabled": true,
    "readyForSampling": false
  }
}
```

### 4. 检查抽样状态
```http
GET /sampling/status/{configId}
```
**响应**:
```json
{
  "code": 200,
  "msg": "状态查询成功",
  "data": {
    "configId": 123,
    "configName": "产品A抽样配置",
    "enabled": true,
    "currentCount": 8,
    "targetCount": 10,
    "cacheSize": 8,
    "readyForSampling": false,
    "progress": 80.0
  }
}
```

## 工作流程

### 1. 数据接收阶段
1. `InspectionDataConsumer` 接收检验数据
2. 将完整的 `InspectionDataDTO` 存储到Redis缓存
3. 增加计数器
4. 检查是否达到抽样条件

### 2. 抽样触发阶段
1. 当计数器达到 `F_SAMPLING_COUNT` 时触发抽样
2. 发送MQ消息到抽样队列
3. `SamplingTriggerConsumer` 接收触发消息

### 3. 抽样执行阶段
1. `SamplingService` 获取抽样配置
2. 从Redis获取缓存数据
3. 根据策略过滤数据
4. 根据方法选择样本
5. 保存抽样结果
6. 清理原始缓存

## 使用示例

### 配置示例
```sql
INSERT INTO SAMPLING_TASK_CONFIG (
    F_NAME, F_DESCRIPTION, F_SAMPLING_TYPE, F_SAMPLING_COUNT,
    F_SAMPLING_STRATEGY, F_SAMPLING_METHOD, F_PART_ID, F_ENABLED
) VALUES (
    '产品A随机抽样', '对产品A进行随机抽样，每10个数据抽样一次',
    1, 10, 1, 1, 100001, 1
);
```

### 编程示例
```java
// 手动触发抽样
@Autowired
private SamplingService samplingService;

public void triggerSampling(Long configId) {
    SamplingService.SamplingResult result = samplingService.performSampling(configId);
    if (result.isSuccess()) {
        System.out.println("抽样成功，数据量: " + result.getData().size());
        System.out.println("结果键: " + result.getResultKey());
    } else {
        System.out.println("抽样失败: " + result.getMessage());
    }
}

// 获取统计信息
public void checkStats(Long configId) {
    SamplingService.SamplingStats stats = samplingService.getSamplingStats(configId);
    if (stats != null) {
        System.out.println("当前进度: " + stats.getCurrentCount() + "/" + stats.getTargetCount());
        System.out.println("是否就绪: " + stats.isReadyForSampling());
    }
}
```

## 注意事项

1. **数据缓存**: 抽样结果在Redis中缓存1小时，请及时获取
2. **配置验证**: 确保抽样配置参数正确，否则抽样会失败
3. **性能考虑**: 大量数据时建议使用时间排序而非随机抽样
4. **并发处理**: 系统支持多个配置同时进行抽样
5. **错误处理**: 所有异常都会被记录到日志中

## 扩展功能

### 待实现功能
1. 时间间隔触发 (F_TIME_INTERVAL)
2. 生产节拍触发 (F_PRODUCTION_BEAT_COUNT)
3. 抽样结果持久化
4. 抽样历史记录
5. 抽样质量评估

### 自定义扩展
可以通过继承 `SamplingTool` 类实现自定义抽样算法：

```java
@Component
public class CustomSamplingTool extends SamplingTool {
    
    @Override
    public List<InspectionDataDTO> performSampling(
        SAMPLING_TASK_CONFIG config, 
        List<InspectionDataDTO> dataList) {
        // 自定义抽样逻辑
        return customSamplingLogic(config, dataList);
    }
}
```
