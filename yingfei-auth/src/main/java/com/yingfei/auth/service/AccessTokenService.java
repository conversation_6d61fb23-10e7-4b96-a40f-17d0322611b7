package com.yingfei.auth.service;

import com.yingfei.auth.form.AccessTokenBody;
import com.yingfei.auth.form.RefreshTokenBody;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AccessTokenService {
    /**
     * 创建令牌
     * @param accessTokenBody
     * @return
     */
    Map<String, Object> createToken(AccessTokenBody accessTokenBody);

    /**
     * 刷新令牌
     * @param refreshTokenBody
     * @return
     */
    Map<String, Object> refreshToken(RefreshTokenBody refreshTokenBody);
}
