package com.yingfei.auth.service.impl;

import com.yingfei.auth.form.AccessTokenBody;
import com.yingfei.auth.form.RefreshTokenBody;
import com.yingfei.auth.service.AccessTokenService;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.JwtUtils;
import com.yingfei.common.core.utils.uuid.IdUtils;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.enums.EMPL_INFStatusEnum;
import com.yingfei.system.api.RemoteUserService;
import io.jsonwebtoken.Claims;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class AccessTokenServiceImpl implements AccessTokenService {

    @Value("${third-party-token.secret}")
    private String secret;

    @Value("${third-party-token.expireTime}")
    private Long expireTime;

    @Resource
    RemoteUserService remoteUserService;
    @Resource
    RedisService redisService;
    static String ACCESS_TOKEN_KEY = "AccessToken:";

    /**
     * 创建令牌
     *
     * @param accessTokenBody
     * @return
     */
    @Override
    public Map<String, Object> createToken(AccessTokenBody accessTokenBody) {
        // 密钥验证
        if (!secret.equals(accessTokenBody.getSecret())) {
            throw new BaseException("secret is error");
        }
        if(redisService.get(ACCESS_TOKEN_KEY+accessTokenBody.getUserId())!=null){
            return (Map<String, Object>) redisService.get(ACCESS_TOKEN_KEY+accessTokenBody.getUserId());
        }
        EMPL_INF_DTO emplInfDTO = new EMPL_INF_DTO();
        try {
            R<EMPL_INF_DTO> info = remoteUserService.info(accessTokenBody.getUserId());
            if (!Constants.SUCCESS.equals(info.getCode())) {
                throw new BaseException("Exception to obtaining user information");
            }
            emplInfDTO = info.getData();
            if (ObjectUtils.isEmpty(emplInfDTO)) {
                throw new BaseException("user not found");
            }
            if (EMPL_INFStatusEnum.ACTIVATE.getCode() != emplInfDTO.getF_STATUS()) {
                throw new BaseException("user is invalid");
            }
        } catch (Exception e) {
            throw new BaseException("Exception to obtaining user information");
        }
        // 创建JWT
        Map<String, Object> claimsMap = new HashMap<String, Object>(4);
        claimsMap.put(SecurityConstants.USER_KEY, IdUtils.fastUUID());
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, emplInfDTO.getF_EMPL());
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, emplInfDTO.getF_NAME());
        claimsMap.put(SecurityConstants.ACCOUNT, emplInfDTO.getF_CODE());
        String token = JwtUtils.createToken(claimsMap, secret, expireTime);
        Map<String, Object> rspMap = new HashMap<String, Object>(2);
        rspMap.put("accessToken", token);
        rspMap.put("expiresIn", expireTime);
        redisService.set(ACCESS_TOKEN_KEY+accessTokenBody.getUserId(), rspMap, expireTime/2);
        return rspMap;
    }

    /**
     * 刷新令牌
     *
     * @param refreshTokenBody
     * @return
     */
    @Override
    public Map<String, Object> refreshToken(RefreshTokenBody refreshTokenBody) {
        // 密钥验证
        if (!secret.equals(refreshTokenBody.getSecret())) {
            throw new BaseException("secret is error");
        }
        try {
            Claims claims = JwtUtils.parseToken(refreshTokenBody.getAccessToken(), secret);
            // 重新创建JWT，实现刷新
            String refreshedToken = JwtUtils.createToken(claims, secret, expireTime);
            Map<String, Object> rspMap = new HashMap<String, Object>(2);
            rspMap.put("accessToken", refreshedToken);
            rspMap.put("expiresIn", expireTime);
            return rspMap;
        } catch (Exception e) {
            throw new BaseException("The accessToken is invalid or expired");
        }
    }
}
