package com.yingfei.auth.controller;

import com.yingfei.auth.form.AccessTokenBody;
import com.yingfei.auth.form.RefreshTokenBody;
import com.yingfei.auth.service.AccessTokenService;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@Slf4j
@Api(tags = "授权：访问令牌管理")
@RestController
@RequestMapping("/access-token")
public class AccessTokenController {
    @Resource
    AccessTokenService accessTokenService;

    @ApiOperation("获取访问令牌")
    @PostMapping("/token")
    public R<?> getToken(@RequestBody AccessTokenBody accessTokenBody) {
        // 员工ID验证
        if (ObjectUtils.isEmpty(accessTokenBody.getUserId())) {
            return R.fail("userId is not null");
        }
        // 员工ID验证
        if (StringUtils.isEmpty(accessTokenBody.getSecret())) {
            return R.fail("secret is not null");
        }
        return R.ok(accessTokenService.createToken(accessTokenBody));

    }

    @ApiOperation("访问令牌续期")
    @PostMapping("/refresh")
    public R<?> refreshToken(@RequestBody RefreshTokenBody refreshTokenBody) {
        if (StringUtils.isEmpty(refreshTokenBody.getAccessToken())) {
            return R.fail("accessToken is not null");
        }
        return R.ok(accessTokenService.refreshToken(refreshTokenBody));

    }
}

