-- 检查 SAMPLING_TASK_CONFIG 表结构
-- 确保使用 TypeHandler 的字段是文本类型

-- 查看表结构
DESCRIBE SAMPLING_TASK_CONFIG;

-- 或者使用 SHOW COLUMNS
SHOW COLUMNS FROM SAMPLING_TASK_CONFIG;

-- 检查特定字段的数据类型
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SAMPLING_TASK_CONFIG' 
AND COLUMN_NAME IN ('F_NOTICE_USER', 'F_MOTICE_RULE', 'F_NOTICE_TYPE');

-- 如果字段不存在，需要添加这些字段
-- ALTER TABLE SAMPLING_TASK_CONFIG ADD COLUMN F_NOTICE_USER VARCHAR(500) COMMENT '异常通知用户';
-- ALTER TABLE SAMPLING_TASK_CONFIG ADD COLUMN F_MOTICE_RULE VARCHAR(500) COMMENT '异常通知角色';  
-- ALTER TABLE SAMPLING_TASK_CONFIG ADD COLUMN F_NOTICE_TYPE VARCHAR(200) COMMENT '异常通知类型';

-- 测试数据插入和查询
-- INSERT INTO SAMPLING_TASK_CONFIG (F_NAME, F_NOTICE_USER, F_MOTICE_RULE, F_NOTICE_TYPE) 
-- VALUES ('测试配置', '1001,1002,1003', '2001,2002', '1,2,3');

-- SELECT F_NOTICE_USER, F_MOTICE_RULE, F_NOTICE_TYPE FROM SAMPLING_TASK_CONFIG WHERE F_NAME = '测试配置';
