# MyBatis TypeHandler 配置示例
# 将以下配置添加到各模块的 application.yml 或 bootstrap.yml 中

mybatis-plus:
  configuration:
    # 注册自定义 TypeHandler
    type-handlers-package: com.yingfei.common.core.handler
  # 或者直接指定 TypeHandler 类
  type-handlers:
    - com.yingfei.common.core.handler.LongListTypeHandler
    - com.yingfei.common.core.handler.IntegerListTypeHandler

# 如果使用原生 MyBatis 配置
mybatis:
  configuration:
    type-handlers-package: com.yingfei.common.core.handler
  type-handlers:
    - com.yingfei.common.core.handler.LongListTypeHandler
    - com.yingfei.common.core.handler.IntegerListTypeHandler
