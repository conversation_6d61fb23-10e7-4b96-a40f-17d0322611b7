package com.yingfei.mq.api.factory;

import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.api.RemoteMqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 消息中间件服务降级处理
 *
 *
 */
@Component
public class RemoteMqFallbackFactory implements FallbackFactory<RemoteMqService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteMqFallbackFactory.class);

    @Override
    public RemoteMqService create(Throwable throwable) {
        log.error("消息中间件服务调用失败:{}", throwable.getMessage());
        return new RemoteMqService() {
            @Override
            public R<?> send(@RequestBody List<SubgroupDataVO> subgroupDataVOList) {
                return null;
            }

            @Override
            public R<?> save(@RequestBody List<SubgroupDataVO> subgroupDataVOList, @PathVariable("msgId") String msgId) {
                return null;
            }

            /**
             * 检验数据添加
             * @param inspectionDataDTO
             * @return 结果
             */
            @Override
            public R<?> send(@RequestBody InspectionDataDTO inspectionDataDTO) {
                return null;
            }

            @Override
            public R<?> batchSend(@RequestBody FileNameDataDTO fileNameDataDTO) {
                return null;
            }
        };
    }
}
