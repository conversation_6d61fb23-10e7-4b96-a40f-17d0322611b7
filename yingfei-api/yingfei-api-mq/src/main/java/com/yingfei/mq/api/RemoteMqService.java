package com.yingfei.mq.api;

import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;

import com.yingfei.entity.dto.InspectionDataDTO;
import com.yingfei.entity.dto.dataImport.FileNameDataDTO;
import com.yingfei.entity.vo.PRCS_INF_VO;
import com.yingfei.entity.vo.SubgroupDataBatchAddVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import com.yingfei.mq.api.factory.RemoteMqFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;


/**
 * 消息中间件服务
 */
@FeignClient(contextId = "remoteMqService", value = ServiceNameConstants.MQ_SERVICE, fallbackFactory = RemoteMqFallbackFactory.class)
public interface RemoteMqService {
    /**
     * 子组添加
     *
     * @return 结果
     */
    @PostMapping("/subgroupDataCollect/send")
    public R<?> send(@RequestBody List<SubgroupDataVO> subgroupDataVOList);

    @PostMapping("/subgroupDataCollect/save?msgId={msgId}")
    public R<?> save(@RequestBody List<SubgroupDataVO> subgroupDataVOList, @PathVariable("msgId") String msgId);
    /**
     * 检验数据添加
     *
     * @return 结果
     */
    @PostMapping("/inspectionDataCollect/send")
    public R<?> send(@RequestBody InspectionDataDTO inspectionDataDTO);

    /**
     * 子组批量添加
     *
     * @return 结果
     */
    @PostMapping("/subgroupDataCollect/batchSend")
    public R<?> batchSend(@RequestBody FileNameDataDTO fileNameDataDTO);

}
