package com.yingfei.system.api;

import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.dingDing.DingDingSendMessageDTO;
import com.yingfei.entity.dto.email.EmailSendMessageDTO;
import com.yingfei.entity.dto.msg.SendMessageDTO;
import com.yingfei.entity.dto.qyWeChat.QyWeChatSendMessageDTO;
import com.yingfei.system.api.factory.RemoteFileFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 消息发送服务
 */
@FeignClient(contextId = "remoteSendService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteFileFallbackFactory.class)
public interface RemoteSendService {
    /**
     * 企业微信发送
     *
     * @return 结果
     */
    @PostMapping("/sendMessage/qyWeChatSend")
    public R<?> qyWeChatSend(@RequestBody QyWeChatSendMessageDTO sendMessageDTO);

    /**
     * 钉钉发送
     *
     * @return 结果
     */
    @PostMapping("/sendMessage/dingDingSend")
    public R<?> dingDingSend(@RequestBody DingDingSendMessageDTO dingDingSendMessageDTO);

    /**
     * 邮件发送
     *
     * @return 结果
     */
    @PostMapping("/sendMessage/emailSend")
    public R<?> emailSend(@RequestBody EmailSendMessageDTO emailSendMessageDTO);

    /**
     * 发送消息
     *
     * @return 结果
     */
    @PostMapping("/sendMessage/sendMsg")
    public R<?> sendMsg(@RequestBody SendMessageDTO sendMessageDTO);
}
