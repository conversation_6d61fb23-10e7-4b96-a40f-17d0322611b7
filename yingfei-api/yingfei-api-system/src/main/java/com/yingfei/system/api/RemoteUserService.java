package com.yingfei.system.api;

import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.entity.dto.AdminUserRespDTO;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.model.LoginUser;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.system.api.factory.RemoteUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务
 *
 *
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService {

    /**
     * 获取用户登录信息
     *
     * @param account 账号
     * @param source      请求来源
     * @return 结果
     */
    @GetMapping("/empl_inf/info/account")
    R<LoginUser> getLoginUserInfo(@RequestParam("account") String account, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 根据用户id列表获取数据
     */
    @PostMapping("/empl_inf/getUserMap")
    R<Map<Long,AdminUserRespDTO>> getUserMap(@RequestBody EMPL_INF_VO emplInfVo);

    @GetMapping("/empl_inf/getUser")
    R<AdminUserRespDTO> getUser(@RequestParam("userId") Long userId);

    /**
     * 获取条件对应的用户id
     */
    @GetMapping("/empl_inf/findByIds")
    R<Set<Long>> findByIds(@RequestParam("condition") String condition,@RequestParam("type") Integer type);

    @GetMapping("/empl_inf/info/{id}")
    R<EMPL_INF_DTO> info(@PathVariable("id") Long id);

    /**
     * 根据层级id获取层级结构
     */
    @GetMapping("/hierarchy_inf/findById")
    R<HIERARCHY_INF_DTO> findByHierId(@RequestParam("id") Long id);

    /**
     * 根据条件获取用户列表
     */
    @PostMapping("/empl_inf/getList")
    R<List<EMPL_INF_DTO>> getList(@RequestBody EMPL_INF_VO emplInfVo);
}
