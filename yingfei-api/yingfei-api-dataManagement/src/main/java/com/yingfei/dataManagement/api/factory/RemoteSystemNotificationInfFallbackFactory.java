package com.yingfei.dataManagement.api.factory;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.RemoteSystemNotificationInfService;
import com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class RemoteSystemNotificationInfFallbackFactory implements FallbackFactory<RemoteSystemNotificationInfService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteSystemNotificationInfFallbackFactory.class);

    @Override
    public RemoteSystemNotificationInfService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteSystemNotificationInfService() {
            @Override
            public R<?> saveBatch(List<SYSTEM_NOTIFICATION_INF> systemNotificationINFs) {
                return R.fail();
            }
        };
    }
}

