package com.yingfei.dataManagement.api;

import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.factory.RemoteTestFallbackFactory;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.vo.CTRL_INF_VO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 测试服务
 */
@FeignClient(contextId = "remoteCtrlService", value = ServiceNameConstants.DATAMANAGEMENT_SERVICE, fallbackFactory = RemoteTestFallbackFactory.class)
public interface RemoteCtrlService {
    @ApiOperation("根据条件获取控制限信息")
    @PostMapping("/ctrl_inf/getCtrlInfo")
    public R<List<CTRL_INF_DTO>> getCtrlInfo(@RequestBody CTRL_INF_VO ctrlInf);
}
