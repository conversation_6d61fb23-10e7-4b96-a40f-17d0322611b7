package com.yingfei.dataManagement.api.factory;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.RemoteCtrlService;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.vo.CTRL_INF_VO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 控制限服务
 */

@Component
public class RemoteCtrlFallbackFactory implements FallbackFactory<RemoteCtrlService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteCtrlFallbackFactory.class);

    @Override
    public RemoteCtrlService create(Throwable throwable) {
        log.error("过程服务调用失败:{}", throwable.getMessage());
        return new RemoteCtrlService() {
            @Override
            public R<List<CTRL_INF_DTO>> getCtrlInfo(@RequestBody CTRL_INF_VO ctrlInf) {
                return null;
            }
        };
    }
}
