package com.yingfei.dataManagement.api;

import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.factory.RemoteSystemNotificationInfFallbackFactory;
import com.yingfei.entity.domain.SYSTEM_NOTIFICATION_INF;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 测试服务
 */
@FeignClient(contextId = "remoteSystemNotificationInfService", value = ServiceNameConstants.DATAMANAGEMENT_SERVICE, fallbackFactory = RemoteSystemNotificationInfFallbackFactory.class)
public interface RemoteSystemNotificationInfService {

    /**
     * 批量创建系统通知 feign
     */
    @PostMapping("/systemNotificationInf/saveBatch")
    public R<?> saveBatch(@RequestBody List<SYSTEM_NOTIFICATION_INF> systemNotificationINFs);
}
