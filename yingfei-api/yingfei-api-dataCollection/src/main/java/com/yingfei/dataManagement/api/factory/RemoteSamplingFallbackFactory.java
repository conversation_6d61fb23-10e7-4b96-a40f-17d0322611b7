package com.yingfei.dataManagement.api.factory;

import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.RemoteSamplingService;
import com.yingfei.entity.requestEntity.SamplingRequestEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户服务降级处理
 *
 *
 */
@Component
public class RemoteSamplingFallbackFactory implements FallbackFactory<RemoteSamplingService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteSamplingFallbackFactory.class);
    @Override
    public RemoteSamplingService create(Throwable throwable) {
        log.error("过程服务调用失败:{}", throwable.getMessage());
        return new RemoteSamplingService() {
            @Override
            public R<?> performSampling(@RequestBody SamplingRequestEntity samplingRequestEntity) {
                return null;
            }
        };
    }
}
