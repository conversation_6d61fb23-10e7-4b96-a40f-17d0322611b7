package com.yingfei.dataManagement.api;

import com.yingfei.common.core.constant.ServiceNameConstants;
import com.yingfei.common.core.domain.R;
import com.yingfei.dataManagement.api.factory.RemoteSamplingFallbackFactory;
import com.yingfei.entity.requestEntity.SamplingRequestEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 文件上传服务
 */
@FeignClient(contextId = "remoteSamplingServicee", value = ServiceNameConstants.DATACOLLECTION_SERVICE, fallbackFactory = RemoteSamplingFallbackFactory.class)
public interface RemoteSamplingService {

    /**
     * 执行抽样任务
     * @param samplingRequestEntity
     */
    @PostMapping("/sampling/performSampling")
    public R<?> performSampling(@RequestBody SamplingRequestEntity samplingRequestEntity);

}
