# JSON反序列化错误修复说明

## 📋 问题描述

**错误信息**：
```
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.ArrayList<java.lang.Long>` from String value (token `JsonToken.VALUE_STRING`)
```

**发生位置**：`getMonthTrend`接口，时间：2025-08-19 07:43:24

**根本原因**：前端传递的`emplIdList`参数是字符串格式，但后端期望的是`ArrayList<Long>`类型

## 🔍 问题分析

### 前端可能的传参格式
1. **字符串格式**：`"emplIdList": "1,2,3"`
2. **数组格式**：`"emplIdList": [1, 2, 3]`
3. **字符串数组格式**：`"emplIdList": ["1", "2", "3"]`

### 后端期望格式
```java
private List<Long> emplIdList;
```

Jackson默认无法将字符串`"1,2,3"`直接反序列化为`List<Long>`类型。

## 💡 解决方案

### 1. 自定义反序列化器
为`MonthTrendQueryDTO`中的所有`List<Long>`字段添加自定义反序列化器：

```java
@JsonDeserialize(using = LongListDeserializer.class)
private List<Long> emplIdList;
```

### 2. 反序列化器实现
```java
public static class LongListDeserializer extends JsonDeserializer<List<Long>> {
    @Override
    public List<Long> deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        
        if (p.getCurrentToken().isScalarValue()) {
            // 处理字符串格式："1,2,3"
            String value = p.getValueAsString();
            if (!StringUtils.hasText(value)) {
                return new ArrayList<>();
            }
            
            return Arrays.stream(value.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } else {
            // 处理数组格式：[1, 2, 3] 或 ["1", "2", "3"]
            List<Long> result = new ArrayList<>();
            while (p.nextToken() != null && !p.getCurrentToken().isStructEnd()) {
                if (p.getCurrentToken().isNumeric()) {
                    result.add(p.getLongValue());
                } else if (p.getCurrentToken().isScalarValue()) {
                    String strValue = p.getValueAsString();
                    if (StringUtils.hasText(strValue)) {
                        result.add(Long.valueOf(strValue.trim()));
                    }
                }
            }
            return result;
        }
    }
}
```

## 🎯 修复范围

### 受影响的字段
- `partIdList` - 产品id列表
- `ptrvIdList` - 产品版本id列表  
- `prcsIdList` - 过程id列表
- `testIdList` - 测试id列表
- `emplIdList` - 负责人id列表

### 支持的输入格式
1. **字符串格式**：`"1,2,3"`
2. **数组格式**：`[1, 2, 3]`
3. **字符串数组格式**：`["1", "2", "3"]`
4. **空值处理**：`""` 或 `null`
5. **单个值**：`"123"`

## 🧪 测试用例

### 测试场景1：字符串格式
```json
{
  "emplIdList": "1,2,3",
  "partIdList": "10,20,30",
  "startDate": "2024-01",
  "endDate": "2024-08"
}
```

### 测试场景2：数组格式
```json
{
  "emplIdList": [1, 2, 3],
  "partIdList": [10, 20, 30],
  "startDate": "2024-01",
  "endDate": "2024-08"
}
```

### 测试场景3：字符串数组格式
```json
{
  "emplIdList": ["1", "2", "3"],
  "partIdList": ["10", "20", "30"],
  "startDate": "2024-01",
  "endDate": "2024-08"
}
```

### 测试场景4：混合格式
```json
{
  "emplIdList": "1,2,3",
  "partIdList": [10, 20, 30],
  "prcsIdList": ["100", "200"],
  "testIdList": "1000",
  "startDate": "2024-01",
  "endDate": "2024-08"
}
```

## 🚀 部署步骤

1. **编译项目**：
   ```bash
   mvn clean compile
   ```

2. **重启服务**：
   - 重启`yingfei-modules-dataManagement`服务

3. **验证修复**：
   - 运行`test_monthtrend_fix.bat`
   - 测试`POST /monthTrend/getMonthTrend`接口

## 📝 注意事项

1. **向后兼容**：修复后仍支持原有的数组格式传参
2. **性能影响**：自定义反序列化器对性能影响微乎其微
3. **扩展性**：可以轻松扩展到其他需要类似处理的DTO类
4. **错误处理**：包含完善的异常处理和空值处理

## 🔄 后续优化建议

1. **统一处理**：考虑创建通用的`LongListDeserializer`工具类
2. **全局配置**：可以在Jackson全局配置中处理类似问题
3. **前端规范**：建议前端统一使用数组格式传参，避免字符串格式
4. **文档更新**：更新API文档，明确支持的参数格式
