# TypeHandler 故障排除指南

## 问题现象
`@TableField(typeHandler = LongListTypeHandler.class)` 注解未生效，List<Long> 字段无法正确序列化/反序列化。

## 常见原因及解决方案

### 1. TypeHandler 未注册
**原因**: MyBatis 不知道存在这个 TypeHandler
**解决方案**: 
- ✅ 已添加 `TypeHandlerConfig.java` 配置类
- ✅ 已修改 `MyBatisPlusConfig.java` 添加注册逻辑

### 2. 数据库字段类型不匹配
**原因**: 数据库字段类型与 TypeHandler 期望的类型不符
**检查方法**:
```sql
DESCRIBE SAMPLING_TASK_CONFIG;
```
**要求**: 字段必须是文本类型 (VARCHAR, TEXT, LONGTEXT 等)

### 3. TypeHandler 注解配置错误
**检查项目**:
- ✅ `@MappedTypes({List.class})` 
- ✅ `@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR, JdbcType.CLOB})`
- ✅ `@TableField(typeHandler = LongListTypeHandler.class)`

### 4. Spring Boot 自动配置冲突
**可能原因**: Spring Boot 的自动配置覆盖了自定义配置
**解决方案**: 在 application.yml 中添加:
```yaml
mybatis-plus:
  configuration:
    type-handlers-package: com.yingfei.common.core.handler
```

### 5. 类路径问题
**检查**: 确保 TypeHandler 类在正确的包路径下
**当前路径**: `com.yingfei.common.core.handler.LongListTypeHandler`

## 验证步骤

### 步骤1: 检查 TypeHandler 是否被加载
在启动日志中查找:
```
成功注册 LongListTypeHandler
成功注册 IntegerListTypeHandler
```

### 步骤2: 运行测试用例
执行 `TypeHandlerTest.java` 中的测试方法:
```bash
mvn test -Dtest=TypeHandlerTest#testLongListTypeHandler
```

### 步骤3: 检查数据库存储
查看数据库中的实际存储值:
```sql
SELECT F_NOTICE_USER, F_MOTICE_RULE, F_NOTICE_TYPE 
FROM SAMPLING_TASK_CONFIG 
WHERE F_NAME = 'TypeHandler测试配置';
```
**期望结果**: 应该看到逗号分隔的字符串，如 "1001,1002,1003"

### 步骤4: 检查对象映射
在代码中打印查询结果:
```java
SAMPLING_TASK_CONFIG config = mapper.selectById(id);
System.out.println("通知用户: " + config.getF_NOTICE_USER());
```
**期望结果**: 应该是 List<Long> 对象，不是字符串

## 调试技巧

### 1. 启用 MyBatis 日志
在 application.yml 中添加:
```yaml
logging:
  level:
    com.yingfei.dataCollection.mapper: DEBUG
    org.apache.ibatis: DEBUG
```

### 2. 添加调试日志
在 TypeHandler 的方法中添加日志:
```java
@Override
public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType) throws SQLException {
    log.debug("设置参数: {}", parameter);
    String str = parameter.stream()
            .map(String::valueOf)
            .collect(Collectors.joining(","));
    log.debug("转换后的字符串: {}", str);
    ps.setString(i, str);
}
```

### 3. 检查 SQL 执行
观察实际执行的 SQL 语句，确认参数绑定是否正确。

## 常见错误信息

### 错误1: ClassNotFoundException
```
java.lang.ClassNotFoundException: com.yingfei.common.core.handler.LongListTypeHandler
```
**解决**: 检查类路径和包名是否正确

### 错误2: 类型转换异常
```
java.lang.ClassCastException: java.lang.String cannot be cast to java.util.List
```
**解决**: TypeHandler 未生效，按本指南重新配置

### 错误3: SQL 异常
```
Data truncation: Data too long for column
```
**解决**: 增加数据库字段长度

## 最终检查清单

- [ ] TypeHandler 类存在且实现正确
- [ ] TypeHandler 已注册到 MyBatis 配置中
- [ ] 数据库字段类型为文本类型
- [ ] 实体类字段添加了正确的注解
- [ ] 测试用例通过
- [ ] 启动日志显示 TypeHandler 注册成功
- [ ] 实际数据库操作正常

## 联系支持
如果按照以上步骤仍无法解决问题，请提供:
1. 完整的错误日志
2. 数据库表结构
3. 实体类定义
4. MyBatis 配置信息
