package com.yingfei.common.redis.service;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 加锁解锁工具类
 * </p>
 */
@Slf4j
@Component
public class RedisLock {

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newScheduledThreadPool(4);
    @Resource
    private Redisson redisson;

    /**
     * Redisson获取锁
     *
     * @param lockKey      锁名
     * @param uuid         唯一标识
     * @param delaySeconds 过期时间
     * @param unit         单位
     * @return 是否获取成功
     */
    public boolean getLock(String lockKey, final String uuid, long delaySeconds, final TimeUnit unit) {
        RLock rLock = redisson.getLock(lockKey);
        boolean success = false;
        try {
            success = rLock.tryLock(0, delaySeconds, unit);
        } catch (InterruptedException e) {
            log.error("[RedisLock][Rlock]>>>> 加锁异常: ", e);
        }
        return success;
    }

    /**
     * Redisson释放锁
     *
     * @param lockKey 锁名
     */
    public void releaseLock(String lockKey) {
        RLock rLock = redisson.getLock(lockKey);
        log.debug("[RedisLock][Rlock]>>>> {}, status: {} === unlock thread id is: {}", rLock.isHeldByCurrentThread(), rLock.isLocked(),
                Thread.currentThread().getId());

        rLock.unlock();
    }

    /**
     * Redisson延迟释放锁
     *
     * @param lockKey   锁名
     * @param delayTime 延迟时间
     * @param unit      单位
     */
    public void delayUnlock(final String lockKey, long delayTime, TimeUnit unit) {
        if (!StringUtils.hasText(lockKey)) {
            return;
        }
        if (delayTime <= 0) {
            releaseLock(lockKey);
        } else {
            EXECUTOR_SERVICE.schedule(() -> releaseLock(lockKey), delayTime, unit);
        }
    }

}