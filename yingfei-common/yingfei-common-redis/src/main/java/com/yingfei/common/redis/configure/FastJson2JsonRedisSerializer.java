package com.yingfei.common.redis.configure;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONFactory;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.reader.ObjectReaderProvider;
import com.alibaba.fastjson2.writer.ObjectWriter;
import com.alibaba.fastjson2.writer.ObjectWriterProvider;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class FastJson2JsonRedisSerializer<T> implements RedisSerializer<T> {
    public static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
    private static final int BUFFER_SIZE = 256 * 1024;
    private final Class<T> clazz;
    private final JSONWriter.Feature[] writerFeatures;
    private final JSONReader.Feature[] readerFeatures;

    // 固定 UTC 格式：带毫秒 + Z
    private static final DateTimeFormatter UTC_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("UTC"));

    static {
        ObjectWriterProvider writerProvider = JSONFactory.getDefaultObjectWriterProvider();
        ObjectReaderProvider readerProvider = JSONFactory.getDefaultObjectReaderProvider();

        // LocalDateTime -> UTC
        writerProvider.register(LocalDateTime.class, new ObjectWriter<LocalDateTime>() {
            @Override
            public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
                LocalDateTime ldt = (LocalDateTime) object;
                String utc = UTC_FORMATTER.format(ldt.atZone(ZoneId.of("UTC")));
                jsonWriter.writeString(utc);
            }
        });

        // java.util.Date -> UTC
        writerProvider.register(java.util.Date.class, new ObjectWriter<java.util.Date>() {
            @Override
            public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
                java.util.Date date = (java.util.Date) object;
                String utc = UTC_FORMATTER.format(date.toInstant().atZone(ZoneId.of("UTC")));
                jsonWriter.writeString(utc);
            }
        });

        // readerProvider 默认支持 ISO8601 + Z 解析，不需要额外配置
    }

    public FastJson2JsonRedisSerializer(Class<T> clazz) {
        this.clazz = clazz;

        this.writerFeatures = new JSONWriter.Feature[]{
                JSONWriter.Feature.WriteClassName,
                JSONWriter.Feature.ReferenceDetection,
                JSONWriter.Feature.IgnoreErrorGetter,
                JSONWriter.Feature.WriteBigDecimalAsPlain,
                JSONWriter.Feature.LargeObject
        };

        this.readerFeatures = new JSONReader.Feature[]{
                JSONReader.Feature.SupportAutoType,
                JSONReader.Feature.IgnoreSetNullValue,
                JSONReader.Feature.TrimString
        };
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        if (t == null) {
            return new byte[0];
        }
        try (ByteArrayOutputStream out = new ByteArrayOutputStream(BUFFER_SIZE)) {
            JSON.writeTo(out, t, writerFeatures);
            return out.toByteArray();
        } catch (IOException e) {
            throw new SerializationException("Redis Serialization failed :" + e.getMessage(), e);
        }
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        try {
            return JSON.parseObject(bytes, 0, bytes.length, DEFAULT_CHARSET, clazz, readerFeatures);
        } catch (Exception e) {
            throw new SerializationException("Redis Serialization failed :" + e.getMessage(), e);
        }
    }
}

