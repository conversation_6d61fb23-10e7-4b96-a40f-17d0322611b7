package com.yingfei.common.redis.configure;

public class RedisConstant {

    /**
     * 产品版本号
     */
//    public static final String PART_REV_NAME = "GlobalConfiguration:part_rev_name";

    /**
     * 默认的报警限
     */
    public static final String WARNING_LIMIT = "GlobalConfiguration:warning_limit_name";

    /**
     * 默认的合理限
     */
    public static final String REASONABLE_LIMIT = "GlobalConfiguration:reasonable_limit_name";

    /**
     * 消息提醒标识
     */
    public static final String SEND_MSG_USER = "send_msg_user_";

    /**
     * 子组缓存最大天数
     */
//    public static final String SUB_GROUP_CACHE_MAX_DAY = "GlobalConfiguration:subgroup_cache_max_day";


    /**
     * 消息日志保存天数
     */
//    public static final String MSG_LOG_SAVE_DAY = "GlobalConfiguration:msg_log_save_day";

    /**
     * 缓存锁  防止多服务情况多次缓存
     */
    public static final String MSG_LOG_SAVE_DAY_LOCK = "msg_log_save_day_lock";

    /**
     * 操作日志清理锁  防止多服务情况多次清理
     */
    public static final String OPER_LOG_CLEANUP_LOCK = "oper_log_cleanup_lock";
    /**
     * 自动采集日志清理锁  防止多服务情况多次清理
     */
    public static final String SCHEDULE_LOG_CLEANUP_LOCK = "schedule_log_cleanup_lock";
    /**
     * 数据监控保存天数
     */
//    public static final String MONITOR_SAVE_DAY = "GlobalConfiguration:monitor_save_day";
    /**
     * 数据监控保存条数
     */
//    public static final String MONITOR_SAVE_NUM = "GlobalConfiguration:monitor_save_num";

    /**
     * 公差限缓存
     */
    public static final String SPEC_KEY = "GlobalConfiguration:spec_key:";

    /**
     * 控制限缓存
     */
    public static final String CTRL_KEY = "GlobalConfiguration:ctrl_key:";

    /**
     * 聚合分析子组数据
     */
    public static final String SUB_GROUP_DATA = "AggregateAnalysis:sub_group_data:%s";

    /**
     * 箱线图详情列表数据
     */
    public static final String BOX_PLOTS_DATA = "BoxPlots:data:%s";

    /**
     * 实时能力趋势详情列表数据
     */
    public static final String STREAM_TREND_DATA = "StreamTrend:data:%s";
    public static final String STREAM_TREND_DATA2 = "StreamTrend:data2:%s";
    public static final String STREAM_TREND_DATA3 = "StreamTrend:data3:%s";

    /**
     * 实时能力趋势详情查询条件
     */
    public static final String STREAM_TREND_CONDITION = "StreamTrend:Condition:%s";

    /**
     * 数据字典缓存
     */
    public static final String DICT_CACHE = "GlobalConfiguration:dict_cache";

    /**
     * 语言类型
     */
    public static final String LANGUAGE_TYPE = "GlobalConfiguration:language_type";

    /**
     * 单项分析,聚合分析是否开启参数集缓存
     */
    public static final String ABLE_PARAMETER_SET_CACHE = "GlobalConfiguration:enable_parameter_set_cache";

    /**
     * 数据库采集
     */
    public static final String DB_COLLECT_IDENTIFY = "db_collect:identify";

    public static final String DB_AUTO_COLLECT_TASK_IDENTIFY = "db_collect:auto_task";


    /**
     * 数据库采集执行sql保存
     */
    public static final String DB_COLLECT_EXECUTE_SQL = "db_collect:execute_sql";

    /**
     * 数据库采集启动sql保存
     */
    public static final String DB_COLLECT_START_SQL = "db_collect:start_sql";

    /**
     * 数据库数据采集闲置过期时间监听
     */
    public static final String IDLE_TIME = "db_collect_idle_time:";
    /**
     * 数据库数据采集闲置子组内容
     */
    public static final String IDLE_TIME_DATA = "db_collect_idle_time:subgroupDataVO:";
    /**
     * 数据库数据采集闲置缓存
     */
    public static final String IDLE_TIME_CACHE = "db_collect_idle_time:cache:";

    /**
     * 子组输入临时保存
     */
    public static final String SUBGROUP_INPUT_TEMP = "subgroup_input_temp:%s-%s";

    /**
     * 子组缓存正在处理临时保存
     */
    public static final String SUBGROUP_PROCESSING_TEMP = "subgroup_processing_temp:%s";

    /**
     * 子组待处理正在处理临时保存
     */
    public static final String SUBGROUP_PROCESSING_PENDING = "subgroup_processing_pending:%s";

    /**
     * 检查计划标识
     */
    public static final String CHECK_PLAN_IDENTIFY = "check_plan_identify";

    /**
     * 检查计划报警触发标识
     */
    public static final String CHECK_PLAN_ALARM_TRIGGER = "check_plan_alarm_trigger";

    /**
     * 企业微信access_token
     */
    public static final String QY_WECHAT_ACCESS_TOKEN = "qy_wechat_access_token";

    /**
     * 钉钉access_token
     */
    public static final String DING_DING_ACCESS_TOKEN = "ding_ding_access_token";

    /**
     * 超级管理员用户id
     */
    public static final String ADMIN_USER_ID = "admin_user_id";

    /**
     * 账户安全配置
     */
//    public static final String ACCOUNT_SECURITY_CONFIGURATION = "account_security_configuration";

    /**
     * 用户登录失败次数
     */
    public static final String ACCOUNT_LOGIN_FAILURES = "account_login_failures:%s";
    /**
     * 用户登录失败锁定
     */
    public static final String ACCOUNT_LOGIN_FAILURES_LOCK = "account_login_failures_lock:%s";

    /**
     * 实时质量概览
     */
    public static final String QUALITY_OVERVIEW = "quality_overview";

    /**
     * 数据文件解析
     */
    public static final String DATA_IMPORT = "data_import_";

    /**
     * 数据文件解析保存并新增
     */
    public static final String DATA_IMPORT_SAVE = "data_import_save";

    /**
     * 待处理文件名和数据缓存
     */
    public static final String FILENAME_DATA_CACHE = "data_import:%s:fileName_data_cache";

    /**
     * 正在处理文件名缓存
     */
    public static final String BE_DEALING_WITH = "data_import:%s:be_dealing_with";

    /**
     * 处理失败文件名缓存
     */
    public static final String PROCESSING_FAILURE_CACHE = "data_import:%s:processing_failure_cache";

    /**
     * 采集任务状态统计
     */
    public static final String ACQUISITION_TASK_STATISTICS = "data_import:acquisition_task_statistics:%s";

    /**
     * 页面分析查询条件
     */
    public static final String PAGE_ANALYZE_QUERY_CONDITION = "page_analyze:%s:query_condition";
    public static final String PAGE_ANALYZE_LATEST_QUERY_CONDITION = "page_analyze:%s:latest_query_condition";

    /**
     * 根据页面分析查询条件查询出的子组
     */
    public static final String PAGE_ANALYZE_SUBGROUP_DATA = "page_analyze:%s:subgroup_data";
    public static final String PAGE_ANALYZE_LATEST_SUBGROUP_DATA = "page_analyze:%s:latest_subgroup_data";
    /**
     * 范围查询数据缓存
     */
    public static final String PAGE_ANALYZE_QUERY_SUBGROUP_DATA = "page_analyze:%s:query_subgroup_data";
    /**
     * 范围查询数据缓存 过期时间
     */
    public static final Long PAGE_ANALYZE_QUERY_SUBGROUP_DATA_EXPIRATION_TIME = 60L*10;
    /**
     * 设置实时质量概览统计时间
     */
    public static final String SET_QUALITY_OVERVIEW_TIME = "set_quality_overview_time";

    /**
     * 数据服务授权保存
     */
    public static final String DATA_MANAGEMENT_SERVICE = "service:data_management_service";

    /**
     * 采集服务授权保存
     */
    public static final String DATA_COLLECT_SERVICE = "service:data_collection_service";

    /**
     * 是否清空对应MQ消息
     */
    public static final String CLEAR_MQ = "clear_mq:";

    /**
     * id生成
     */
    public static final String ID_GENERATOR = "id_generator:";

    /**
     * 子组标签id生成
     */
    public static final String SGRP_TAG = "sgrp_tag";

    /**
     * 消息日志缓存
     */
    public static final String MESSAGE_LOG = "message_log:";



    public static final String INSPECTION_CACHE_KEY = "inspection:cache:";
    public static final String INSPECTION_CACHE_BACKUP_KEY = "inspection:backup:";
    public static final String INSPECTION_MESSAGE_KEY = "inspection:message:";
    public static final Long INSPECTION_MESSAGE_EXPIRE = 60L*10;
}
