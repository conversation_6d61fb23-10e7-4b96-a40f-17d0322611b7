package com.yingfei.common.datascope.aspect;

import com.yingfei.common.core.enums.DataSourceEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.datascope.annotation.DataScope;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.common.security.utils.SecurityUtils;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.dto.HIERARCHY_INF_DTO;
import com.yingfei.entity.enums.DbLinkEnum;
import com.yingfei.entity.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 默认数据库判断
 */
@Slf4j
@Aspect
@Component
public class DbSelectAspect {

    @Resource
    private RedisService redisService;
    @Value("${spring.datasource.druid.driver-class-name}")
    private String driverClassName;

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, ApiOperation controllerDataScope) throws Throwable {
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, ApiOperation controllerDataScope) {
        //获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取参数
        if (joinPoint.getArgs() == null || joinPoint.getArgs().length == 0) {
            return;
        }
        Object arg = joinPoint.getArgs()[0];
        if (arg == null) {
            log.info("自动填充参数为空");
            return;
        }
        int dbType = DbLinkEnum.getType(driverClassName).getType();
        try {
            setDbType(arg, dbType);
        } catch (Exception e) {
            log.info("请求方法没有实体,跳过!");
        }
    }

    private static void setDbType(Object arg, int dbType) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Method method = arg.getClass().getMethod("setDbType",Integer.class);
        method.setAccessible(true); // 允许访问私有方法
        method.invoke(arg, dbType);
    }

}

