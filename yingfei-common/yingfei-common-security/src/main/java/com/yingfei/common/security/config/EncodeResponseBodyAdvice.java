package com.yingfei.common.security.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yingfei.common.core.domain.CryptoData;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.utils.AESUtil;
import com.yingfei.common.core.utils.RsaUtil;
import com.yingfei.common.security.annotation.SecurityParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * @desc 返回数据加密
 */
@Slf4j
@ControllerAdvice(basePackages = "com.yingfei.*.controller")
public class EncodeResponseBodyAdvice implements ResponseBodyAdvice {
    @Value("${rsa.publicKey}")
    private String publicKey;
    @Value("${aes.key}")
    private String aesKey;

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        boolean encode = false;
        if (methodParameter.getMethod().isAnnotationPresent(SecurityParameter.class)) {
            //获取注解配置的包含和去除字段
            SecurityParameter serializedField = methodParameter.getMethodAnnotation(SecurityParameter.class);
            //出参是否需要加密
            encode = serializedField.outEncode();
        }
        if (!(body instanceof R)) {
            return body;
        }
        R<?> resData = (R<?>) body;

        if (encode) {
            log.info("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行加密");
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                if (resData.getData() == null) {
                    return resData;
                }
                String result = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(resData.getData());
                CryptoData cryptoData = new CryptoData();
                String aesEncrypt = AESUtil.encryptStr(result, aesKey);
                String encrypt = RsaUtil.encrypt(aesKey, publicKey);
                log.info("RSA加密AES秘钥----------->{}", encrypt);
                cryptoData.setKey(encrypt);
                cryptoData.setContent(aesEncrypt);
                return R.ok(cryptoData);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行解密出现异常：" + e.getMessage());
            }
        }
        return resData;
    }
}

