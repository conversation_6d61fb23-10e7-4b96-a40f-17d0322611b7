package com.yingfei.common.security.annotation;

import java.lang.annotation.*;

/**
 * 用户信息自动填充注解
 * 用于Controller方法，自动为返回结果填充创建人和编辑人姓名
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FillUserInfo {

    /**
     * 是否启用用户信息填充
     */
    boolean enabled() default true;

    /**
     * 创建人ID字段名
     */
    String createUserField() default "F_CRUE";

    /**
     * 创建人姓名字段名
     */
    String createNameField() default "createName";

    /**
     * 编辑人ID字段名
     */
    String updateUserField() default "F_EDUE";

    /**
     * 编辑人姓名字段名
     */
    String updateNameField() default "updateName";

    /**
     * 需要填充的数据路径，支持嵌套对象
     * 例如: "data", "data.list", "result.records"
     */
    String[] dataPaths() default {"data"};
}
