package com.yingfei.common.security.aspect;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.web.domain.AjaxResult;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.annotation.FillUserInfo;
import com.yingfei.entity.dto.EMPL_INF_DTO;
import com.yingfei.entity.vo.EMPL_INF_VO;
import com.yingfei.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller层用户信息自动填充切面
 * 支持R<T>、AjaxResult、TableDataInfo<T>、List等返回类型
 */
@Aspect
@Component
@Slf4j
public class ControllerUserInfoAspect {

    @Resource
    private RemoteUserService remoteUserService;

    @Around("@annotation(fillUserInfo)")
    public Object fillUserInfo(ProceedingJoinPoint joinPoint, FillUserInfo fillUserInfo) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        if (!fillUserInfo.enabled() || result == null) {
            return result;
        }

        try {
            // 处理不同类型的返回结果
            if (result instanceof R) {
                fillUserInfoForR((R<?>) result, fillUserInfo);
            } else if (result instanceof TableDataInfo) {
                fillUserInfoForTableDataInfo((TableDataInfo<?>) result, fillUserInfo);
            } else if (result instanceof AjaxResult) {
                fillUserInfoForAjaxResult((AjaxResult) result, fillUserInfo);
            } else if (result instanceof List) {
                fillUserInfoForList((List<?>) result, fillUserInfo);
            } else {
                fillUserInfoForObject(result, fillUserInfo);
            }
        } catch (Exception e) {
            log.error("Controller层用户信息填充失败", e);
        }

        return result;
    }

    /**
     * 为R<T>响应主体填充用户信息
     */
    private void fillUserInfoForR(R<?> r, FillUserInfo annotation) {
        if (r == null || r.getData() == null) {
            return;
        }

        for (String dataPath : annotation.dataPaths()) {
            Object targetData = getDataByPath(r, dataPath);
            if (targetData instanceof List) {
                fillUserInfoForList((List<?>) targetData, annotation);
            } else if (targetData instanceof TableDataInfo) {
                fillUserInfoForTableDataInfo((TableDataInfo<?>) targetData, annotation);
            } else if (targetData != null) {
                fillUserInfoForObject(targetData, annotation);
            }
        }
    }

    /**
     * 为TableDataInfo<T>分页数据填充用户信息
     */
    private void fillUserInfoForTableDataInfo(TableDataInfo<?> tableDataInfo, FillUserInfo annotation) {
        if (tableDataInfo == null || CollectionUtils.isEmpty(tableDataInfo.getRows())) {
            return;
        }

        // 直接处理rows列表数据
        fillUserInfoForList(tableDataInfo.getRows(), annotation);
    }

    /**
     * 为AjaxResult填充用户信息
     */
    private void fillUserInfoForAjaxResult(AjaxResult ajaxResult, FillUserInfo annotation) {
        for (String dataPath : annotation.dataPaths()) {
            Object targetData = getDataByPath(ajaxResult, dataPath);
            if (targetData instanceof List) {
                fillUserInfoForList((List<?>) targetData, annotation);
            } else if (targetData instanceof TableDataInfo) {
                fillUserInfoForTableDataInfo((TableDataInfo<?>) targetData, annotation);
            } else if (targetData != null) {
                fillUserInfoForObject(targetData, annotation);
            }
        }
    }

    /**
     * 根据路径获取数据
     */
    private Object getDataByPath(Object obj, String path) {
        if (obj == null || path == null) return null;

        String[] paths = path.split("\\.");
        Object current = obj;

        for (String p : paths) {
            if (current == null) return null;

            try {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(p);
                } else if (current instanceof AjaxResult) {
                    current = ((AjaxResult) current).get(p);
                } else if (current instanceof R) {
                    // 处理R<T>类型
                    if ("data".equals(p)) {
                        current = ((R<?>) current).getData();
                    } else {
                        Field field = getField(current.getClass(), p);
                        if (field != null) {
                            field.setAccessible(true);
                            current = field.get(current);
                        } else {
                            return null;
                        }
                    }
                } else if (current instanceof TableDataInfo) {
                    // 处理TableDataInfo<T>类型
                    if ("rows".equals(p)) {
                        current = ((TableDataInfo<?>) current).getRows();
                    } else {
                        Field field = getField(current.getClass(), p);
                        if (field != null) {
                            field.setAccessible(true);
                            current = field.get(current);
                        } else {
                            return null;
                        }
                    }
                } else {
                    Field field = getField(current.getClass(), p);
                    if (field != null) {
                        field.setAccessible(true);
                        current = field.get(current);
                    } else {
                        return null;
                    }
                }
            } catch (Exception e) {
                log.debug("获取路径{}数据失败: {}", p, e.getMessage());
                return null;
            }
        }

        return current;
    }

    /**
     * 获取字段，支持继承
     */
    private Field getField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 为列表数据填充用户信息
     */
    private void fillUserInfoForList(List<?> list, FillUserInfo annotation) {
        if (CollectionUtils.isEmpty(list)) return;

        // 收集用户ID
        Set<Long> userIds = new HashSet<>();
        for (Object item : list) {
            collectUserIds(item, userIds, annotation);
        }

        if (CollectionUtils.isEmpty(userIds)) return;

        // 批量获取用户信息
        Map<Long, String> userMap = getUserMap(new ArrayList<>(userIds));

        // 填充用户姓名
        for (Object item : list) {
            fillUserNames(item, userMap, annotation);
        }
    }

    /**
     * 为单个对象填充用户信息
     */
    private void fillUserInfoForObject(Object obj, FillUserInfo annotation) {
        if (obj == null) return;

        Set<Long> userIds = new HashSet<>();
        collectUserIds(obj, userIds, annotation);

        if (CollectionUtils.isEmpty(userIds)) return;

        Map<Long, String> userMap = getUserMap(new ArrayList<>(userIds));
        fillUserNames(obj, userMap, annotation);
    }

    /**
     * 收集用户ID
     */
    private void collectUserIds(Object obj, Set<Long> userIds, FillUserInfo annotation) {
        if (obj == null) return;

        try {
            // 收集创建人ID
            Long createUserId = getFieldValue(obj, annotation.createUserField(), Long.class);
            if (createUserId != null) {
                userIds.add(createUserId);
            }

            // 收集编辑人ID
            Long updateUserId = getFieldValue(obj, annotation.updateUserField(), Long.class);
            if (updateUserId != null) {
                userIds.add(updateUserId);
            }
        } catch (Exception e) {
            log.debug("收集用户ID失败: {}", e.getMessage());
        }
    }

    /**
     * 填充用户姓名
     */
    private void fillUserNames(Object obj, Map<Long, String> userMap, FillUserInfo annotation) {
        if (obj == null || ObjectUtils.isEmpty(userMap)) return;

        try {
            // 填充创建人姓名
            Long createUserId = getFieldValue(obj, annotation.createUserField(), Long.class);
            if (createUserId != null && userMap.containsKey(createUserId)) {
                setFieldValue(obj, annotation.createNameField(), userMap.get(createUserId));
            }

            // 填充编辑人姓名
            Long updateUserId = getFieldValue(obj, annotation.updateUserField(), Long.class);
            if (updateUserId != null && userMap.containsKey(updateUserId)) {
                setFieldValue(obj, annotation.updateNameField(), userMap.get(updateUserId));
            }
        } catch (Exception e) {
            log.debug("填充用户姓名失败: {}", e.getMessage());
        }
    }

    /**
     * 获取字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> fieldType) {
        try {
            Field field = getField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                return fieldType.isInstance(value) ? (T) value : null;
            }
        } catch (Exception e) {
            log.debug("获取字段{}值失败: {}", fieldName, e.getMessage());
        }
        return null;
    }

    /**
     * 设置字段值
     */
    private void setFieldValue(Object obj, String fieldName, Object value) {
        try {
            // 先尝试直接设置字段
            Field field = getField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                field.set(obj, value);
                return;
            }

            // 尝试setter方法
            String setterName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method setter = obj.getClass().getMethod(setterName, String.class);
            setter.invoke(obj, value);
        } catch (Exception e) {
            log.debug("设置字段{}值失败: {}", fieldName, e.getMessage());
        }
    }

    /**
     * 批量获取用户信息
     */
    private Map<Long, String> getUserMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }

        try {
            EMPL_INF_VO empl = new EMPL_INF_VO();
            empl.setIds(userIds);

            R<List<EMPL_INF_DTO>> result = remoteUserService.getList(empl);
            if (result != null && result.getData() != null) {
                return result.getData().stream()
                        .collect(Collectors.toMap(
                                EMPL_INF_DTO::getF_EMPL,
                                EMPL_INF_DTO::getF_NAME,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.error("批量获取用户信息失败", e);
        }

        return new HashMap<>();
    }
}
