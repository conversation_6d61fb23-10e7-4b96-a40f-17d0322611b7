package com.yingfei.common.security.config;

import com.alibaba.fastjson2.support.spring.http.converter.FastJsonHttpMessageConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.security.interceptor.AccessTokenInterceptor;
import com.yingfei.common.security.interceptor.HeaderInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * 拦截器配置
 */
public class WebMvcConfig implements WebMvcConfigurer {
    /**
     * 不需要拦截地址
     */
    public static final String[] excludeUrls = {"/refresh","/profile/**",};


    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    @Lazy
    private AccessTokenInterceptor accessTokenInterceptor;
    /**
     * 配置消息转换器
     * 添加改配置的原因 com.yingfei.common.security.config.JacksonConfig 中配置了自定义的ObjectMapper（时间格式配置及Long类型数据的序列化）
     * 这里需要移除所有的JSON消息转换器，确保使用自定义的ObjectMapper
     * @param converters
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 移除所有FastJson消息转换器
        converters.removeIf(c -> c instanceof FastJsonHttpMessageConverter);
        // 移除所有Jackson消息转换器
        converters.removeIf(c -> c instanceof MappingJackson2HttpMessageConverter);
        // 添加我们自定义配置的Jackson转换器，确保在列表最前面
        converters.add(0,new MappingJackson2HttpMessageConverter(objectMapper));
    }


    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                //允许的请求方式
                .allowedMethods("*")
                // 允许的请求头
                .allowedHeaders("*")
                .maxAge(3600)
                .allowCredentials(true);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(getAccessTokenInterceptor())
        registry.addInterceptor(accessTokenInterceptor)
                .addPathPatterns("/**/external/**")
                .order(-11);
        registry.addInterceptor(getHeaderInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(excludeUrls)
                .order(-10);

    }

    /**
     * 自定义请求头拦截器
     */
    public HeaderInterceptor getHeaderInterceptor() {
        return new HeaderInterceptor();
    }
    public AccessTokenInterceptor getAccessTokenInterceptor() {
        return new AccessTokenInterceptor();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /* 本地文件上传路径 */
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**")
                .addResourceLocations("file:" + DownloadConfig.getProfile() + "/")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
}
