package com.yingfei.common.security.interceptor;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.constant.SecurityConstants;
import com.yingfei.common.core.context.SecurityContextHolder;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.JwtUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.model.LoginUser;
import com.yingfei.system.api.RemoteUserService;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * AccessToken拦截器，用于验证来自URL参数的令牌
 */
public class AccessTokenInterceptor implements AsyncHandlerInterceptor {

    private static final String ACCESS_TOKEN_PARAM = "accessToken";
    @Value("${third-party-token.secret}")
    private String secret;
    @Resource
    RemoteUserService remoteUserService;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        // 从请求参数中获取token
        String token = request.getParameter(ACCESS_TOKEN_PARAM);

        if (StringUtils.isNotEmpty(token)) {
            final Claims claims;
            try {
                claims = JwtUtils.parseToken(token, secret);
            } catch (Exception e) {
                throw new BaseException("The accessToken is invalid or expired");
            }
            final String account = claims.get(SecurityConstants.ACCOUNT).toString();
            // 查询用户信息
            R<LoginUser> userResult = remoteUserService.getLoginUserInfo(account, SecurityConstants.INNER);
               if(!Constants.SUCCESS.equals(userResult.getCode())){
                   throw new BaseException("Exception to obtaining user information");
               }
            LoginUser loginUser = userResult.getData();
            if (StringUtils.isNull(loginUser)) {
                throw new BaseException("user not found");
            }
            SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        SecurityContextHolder.remove();
    }
}

