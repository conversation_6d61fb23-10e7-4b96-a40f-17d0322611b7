package com.yingfei.common.security.aspect;

import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;


/**
 * 数据过滤处理
 */
@Slf4j
@Aspect
@Component
public class CreateUpdateByAspect {


    @Before("@annotation(createUpdateBy)")
    public void doBefore(JoinPoint point, CreateUpdateBy createUpdateBy) throws Throwable {
        handleDataScope(point, createUpdateBy);
    }

    protected void handleDataScope(final JoinPoint joinPoint, CreateUpdateBy createUpdateBy) {
        // 获取当前的用户
        Long userId = SecurityUtils.getUserId();
        if (userId == null) userId = 1L;
        //获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        CreateUpdateBy autoFill = signature.getMethod().getAnnotation(CreateUpdateBy.class);
        //获取注解的值
        CreateUpdateByEnum value = autoFill.businessType();

        //获取参数
        Object arg = joinPoint.getArgs()[0];
        if (arg == null) {
            log.info("自动填充参数为空");
            return;
        }
//        //如果是插入 则获取参数 把创建人和修改人填充进去
        if (value == CreateUpdateByEnum.INSERT) {
            try {
                Method setF_CRUE = arg.getClass().getDeclaredMethod("setF_CRUE", Long.class);
                Method setF_EDUE = arg.getClass().getDeclaredMethod("setF_EDUE", Long.class);
                setF_CRUE.invoke(arg, userId);
                setF_EDUE.invoke(arg, userId);
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                e.printStackTrace();
            }
        }
        //如果是更新 则获取参数 把updateTime填充进去
        if (value == CreateUpdateByEnum.UPDATE) {
            try {
                Method setF_EDUE = arg.getClass().getDeclaredMethod("setF_EDUE", Long.class);
                setF_EDUE.invoke(arg, userId);
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                e.printStackTrace();
            }
        }
    }

}
