package com.yingfei.common.core.i18n;

import com.yingfei.common.core.utils.I18nUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

@Configuration
public class I18nConfig {

    @Bean(name = "customMessageSource")
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("i18n/message"); // 匹配 message_zh-cn.properties
        messageSource.setDefaultEncoding("UTF-8"); // 支持中文
        messageSource.setFallbackToSystemLocale(true); // 回退到系统语言环境
        messageSource.setCacheSeconds(3600); // 缓存 1 小时
        messageSource.setAlwaysUseMessageFormat(true); // 支持占位符
        return messageSource;
    }

    @Bean
    public I18nUtils i18nUtils(@Qualifier("customMessageSource") MessageSource messageSource) {
        I18nUtils.init(messageSource); // 初始化 I18nUtils
        return new I18nUtils();
    }
}
