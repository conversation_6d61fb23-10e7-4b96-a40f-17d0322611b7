package com.yingfei.common.core.config;

import com.yingfei.common.core.handler.IntegerListTypeHandler;
import com.yingfei.common.core.handler.LongListTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * TypeHandler 配置类
 * 专门用于注册自定义的 TypeHandler
 */
@Configuration
@Slf4j
public class TypeHandlerConfig {

    @Autowired(required = false)
    private List<SqlSessionFactory> sqlSessionFactoryList;

    /**
     * 在 Spring 容器初始化完成后注册 TypeHandler
     */
    @PostConstruct
    public void registerTypeHandlers() {
        if (sqlSessionFactoryList == null || sqlSessionFactoryList.isEmpty()) {
            log.warn("未找到 SqlSessionFactory，跳过 TypeHandler 注册");
            return;
        }

        log.info("开始注册自定义 TypeHandler，SqlSessionFactory 数量: {}", sqlSessionFactoryList.size());

        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            try {
                TypeHandlerRegistry registry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();
                
                // 注册 LongListTypeHandler
                registry.register(LongListTypeHandler.class);
                log.info("成功注册 LongListTypeHandler");
                
                // 注册 IntegerListTypeHandler
                registry.register(IntegerListTypeHandler.class);
                log.info("成功注册 IntegerListTypeHandler");
                
            } catch (Exception e) {
                log.error("注册 TypeHandler 时发生异常", e);
            }
        }
        
        log.info("TypeHandler 注册完成");
    }
}
