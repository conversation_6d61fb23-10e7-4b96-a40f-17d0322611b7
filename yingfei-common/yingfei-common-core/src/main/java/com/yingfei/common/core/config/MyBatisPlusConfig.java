package com.yingfei.common.core.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.yingfei.common.core.handler.IntegerListTypeHandler;
import com.yingfei.common.core.handler.LongListTypeHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Configuration
public class MyBatisPlusConfig {

    @Resource
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor()); // 分页插件
        return interceptor;
    }

    /**
     * 注册自定义 TypeHandler
     */
    @PostConstruct
    public void registerTypeHandlers() {
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            TypeHandlerRegistry typeHandlerRegistry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();

            // 注册 LongListTypeHandler
            typeHandlerRegistry.register(List.class, LongListTypeHandler.class);
            typeHandlerRegistry.register(LongListTypeHandler.class);

            // 注册 IntegerListTypeHandler
            typeHandlerRegistry.register(List.class, IntegerListTypeHandler.class);
            typeHandlerRegistry.register(IntegerListTypeHandler.class);
        }
    }
}
