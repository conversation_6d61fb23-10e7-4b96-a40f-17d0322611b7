package com.yingfei.common.core.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.yingfei.common.core.handler.IntegerListTypeHandler;
import com.yingfei.common.core.handler.LongListTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

@Configuration
@Slf4j
public class MyBatisPlusConfig {

    @Autowired(required = false)
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor()); // 分页插件
        return interceptor;
    }

    /**
     * 注册自定义 TypeHandler
     */
    @PostConstruct
    public void registerTypeHandlers() {
        if (sqlSessionFactoryList == null || sqlSessionFactoryList.isEmpty()) {
            log.warn("未找到 SqlSessionFactory，跳过 TypeHandler 注册");
            return;
        }

        log.info("开始注册自定义 TypeHandler，SqlSessionFactory 数量: {}", sqlSessionFactoryList.size());

        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            try {
                TypeHandlerRegistry typeHandlerRegistry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();

                // 注册 LongListTypeHandler
                typeHandlerRegistry.register(LongListTypeHandler.class);
                log.info("成功注册 LongListTypeHandler");

                // 注册 IntegerListTypeHandler
                typeHandlerRegistry.register(IntegerListTypeHandler.class);
                log.info("成功注册 IntegerListTypeHandler");

            } catch (Exception e) {
                log.error("注册 TypeHandler 时发生异常", e);
            }
        }

        log.info("TypeHandler 注册完成");
    }
}
