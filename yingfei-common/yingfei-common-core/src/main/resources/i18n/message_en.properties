#validate Messages:

SUCCESS = Operation successful
FAIL = Operation failed
DELETE = Delete


sys_exception = System exception
service_not_found = Service not found
the_user_has_no_permission = The user does not have permission for this function

# BpmProcessInstanceDeleteReasonEnum
REJECT_TASK = Task rejected, reason: {}
CANCEL_TASK = Task actively cancelled, reason: {}
MULTI_TASK_END = Automatically cancelled by system, reason: Multi-task approval has met conditions, no need to approve this task
REJECT_TASK_REASON_PREFIX = Task rejected, reason:

INVALID_TYPE = Invalid type

# AuthExceptionEnum
AUTH_PERMISS_EXCEPTION = Insufficient permissions
AUTH_MARK_DUPLICATION_EXCEPTION = Permission identifier duplicated
AUTH_PASSWORD_EXCEPTION = Login password error
AUTH_ACCOUNT_EXPIRED_EXCEPTION = This account has expired
AUTH_USER_NOT_LOGIN_EXCEPTION = User not logged in
IP_BLACKLIST = This IP has been blacklisted by the system
LOGIN_ACCOUNT_DOES_NOT_EXIST = Login account does not exist
AUTH_ACCOUNT_PASSWORD_EMPTY_EXCEPTION = Mobile phone number/password must be filled in
LOGIN_ACCOUNT_UP_TO_THE_LIMIT = The number of failed login attempts for this user has reached the limit, please try again after 1 hour!
LOGIN_USER_COUNT_REACH_LIMIT = The number of logged-in users in this system has reached the limit

# CommonExceptionEnum
PARAMETER_MISSING_EXCEPTION = Request parameter missing
PRESENCE_SUBDATA__EXCEPTION = This group has sub-data
DATA_NOT_FOUND_EXCEPTION = This data does not exist
IMPORT_DATA_NOT_NULL_EXCEPTION = Import data cannot be empty
MQ_SEND_EXCEPTION = Message sending failed
DO_NOT_SUBMIT_DATA_TWICE = Do not submit data repeatedly
ACCESS_TOKEN_FAILED = Failed to obtain access_token
SEND_CONFIG_NOT_EXIST = Message sending configuration not set
THE_IMPORTED_FILE_TYPE_IS_NOT_SUPPORTED = Imported file type not supported
FEIGN_ERROR = Feign call failed
DATA_ALREADY_EXISTS_EXCEPTION = Data already exists
UNSUPPORTED_DATABASE_TYPE_EXCEPTION = Unsupported database type

# DataCollectionExceptionEnum
NUMBER_OF_COLUMNS_DOES_NOT_MATCH_EXCEPTION = Number of columns in imported file does not match, please check file format
START_LINE_EXCEPTION = Please check if the starting line is correct
TASK_TIME_NOT_REACHED = Task start time not reached, cannot resume
SCHEDULE_JOB_NAME_DUPLICATION_EXCEPTION=Automatic collection task name duplication
SCHEDULE_JOB_DATA_FORMAT_ERROR=Automatic collection task data format error

# DataManagementExceptionEnum
PRODUCT_NAME_DUPLICATION_EXCEPTION = Product name duplicated
PROCESS_NAME_DUPLICATION_EXCEPTION = Process name duplicated
TEST_NAME_DUPLICATION_EXCEPTION = Test name duplicated
TAG_GRP_NAME_DUPLICATION_EXCEPTION = Tag group name duplicated
TAG_NAME_DUPLICATION_EXCEPTION = Tag name duplicated
SHIFT_GRP_NAME_DUPLICATION_EXCEPTION = Shift group name duplicated
SHIFT_NAME_DUPLICATION_EXCEPTION = Shift name duplicated
PRODUCT_BATCH_NAME_DUPLICATION_EXCEPTION = Product batch name duplicated
PRODUCT_SERIAL_NUMBER_NAME_DUPLICATION_EXCEPTION = Product serial number name duplicated
JOB_GRP_NAME_DUPLICATION_EXCEPTION = Work order group name duplicated
JOB_NAME_DUPLICATION_EXCEPTION = Work order name duplicated
DESC_GRP_NAME_DUPLICATION_EXCEPTION = Custom descriptor group name duplicated
DESC_NAME_DUPLICATION_EXCEPTION = Custom descriptor name duplicated
DEF_GRP_NAME_DUPLICATION_EXCEPTION = Defect code group name duplicated
DEF_NAME_DUPLICATION_EXCEPTION = Defect code name duplicated
ROOT_CAUSE_GRP_NAME_DUPLICATION_EXCEPTION = Root cause group name duplicated
ROOT_CAUSE_NAME_DUPLICATION_EXCEPTION = Root cause name duplicated
RESPONSE_ACTION_GRP_NAME_DUPLICATION_EXCEPTION = Response action group name duplicated
RESPONSE_ACTION_NAME_DUPLICATION_EXCEPTION = Response action name duplicated
PARAMETER_SET_NAME_DUPLICATION_EXCEPTION = Parameter set name duplicated
PROCESSING_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Data processing template name duplicated
ACTIVED_RULE_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Alarm rule template name duplicated
WARNING_LIMIT_EXCEPTION = Alarm limit configuration error
REASONABLE_LIMIT_EXCEPTION = Reasonable limit configuration error
USL_LSL_EXCEPTION = Tolerance limit configuration error
PRODUCT_REV_NAME_DUPLICATION_EXCEPTION = Product revision name duplicated
MANUFACTURING_PROCESS_NAME_DUPLICATION_EXCEPTION = Manufacturing process structure name duplicated
DELETE_THE_SUBPLAN_FIRST_EXCEPTION = Please delete the sub-plan first
CLEAR_THE_CACHE_SUBGROUP_EXCEPTION = Failed to clear cache subgroup
PROCESS_DEFINITION_KEY_NOT_MATCH = The expected process definition key is ({}), current is ({}), please modify the BPMN diagram
PROCESS_DEFINITION_NAME_NOT_MATCH = The expected process definition name is ({}), current is ({}), please modify the BPMN diagram
MODEL_KEY_VALID = Invalid process key format, must start with letter or underscore, followed by any letter, number, hyphen, underscore, or period!
MODEL_KEY_EXISTS = A process with key [{}] already exists
MODEL_NOT_EXISTS = Process model does not exist
MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG = Failed to deploy process, reason: Process form not configured, please click [Modify Process] button to configure
FORM_NOT_EXISTS = Dynamic form does not exist
PROCESS_DEFINITION_NOT_EXISTS = Process definition does not exist
PROCESS_INSTANCE_NOT_EXISTS = Process instance does not exist
TASK_COMPLETE_FAIL_NOT_EXISTS = Failed to complete approval task, reason: The task is not in unapproved state
TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF = Failed to complete approval task, reason: You are not the assignee of this task
PROCESS_DEFINITION_IS_SUSPENDED = Process definition is in suspended state
PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS = Failed to cancel process instance, process is not running
PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF = Failed to cancel process instance, you did not initiate this process
TASK_ASSIGN_RULE_EXISTS = Assignment rule already exists for process ({}) task ({})
TASK_ASSIGN_RULE_NOT_EXISTS = Process task does not exist
TASK_UPDATE_FAIL_NOT_MODEL = Only task assignment rules of process models can be modified
MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG = Failed to deploy process, reason: User task ({}) has no assignment rule configured, please click [Modify Process] button to configure
TASK_ASSIGN_FORM_EXISTS = Assignment form already exists for process ({}) task ({})
TASK_CREATE_FAIL_NO_CANDIDATE_USER = Operation failed, reason: Cannot find approver for the task!
GAUGE_AGENT_NAME_DUPLICATION_EXCEPTION = Gauge Agent name or hardware ID already exists
GAUGE_HARDWARE_ID_NOT_EXISTS = Gauge hardware ID does not exist
GAUGE_FORMAT_NAME_DUPLICATION_EXCEPTION = Gauge parsing rule name duplicated
GAUGE_INTERFACE_NAME_DUPLICATION_EXCEPTION = Gauge interface configuration name duplicated
GAUGE_CONNECTION_NOT_EXISTS = Gauge connection configuration does not exist
GAUGE_DEVICE_NOT_EXISTS = Gauge device does not exist
GAUGE_FORMAT_NOT_EXISTS = Gauge parsing configuration does not exist
PLEASE_CHECK_THE_TERMINAL_CONFIGURATION = Please check terminal symbol configuration
TERMINAL_SYMBOL_NOT_EXISTS = Terminal symbol does not exist
PLEASE_CHECK_THE_INITIATOR_CONFIGURATION = Please check initiator configuration
THE_CHANNEL_NUMBER_DOES_NOT_MATCH = Channel number does not match
CTRL_DUPLICATION_EXCEPTION = Control limit duplicated
SPEC_DUPLICATION_EXCEPTION = Specification limit duplicated
PARAMETER_SET_NOT_EXISTS = Parameter set does not exist
MENU_ALREADY_ASSOCIATED_ANALYSIS_TEMPLATE = Menu is already associated with analysis template
CHART_TYPE_NOT_EXISTS = Chart type does not exist
QUADRANT_CHART_CONFIG_NOT_EXISTS = Quadrant chart configuration does not exist
ANALYSIS_DASHBOARD_NOT_EXISTS = Analysis dashboard does not exist
CHART_CONFIG_NOT_EXISTS = Chart configuration does not exist
DICT_CODE_DUPLICATION_EXCEPTION = Dictionary code and content duplicated
JOB_GRP_NOT_EXISTS = Work group does not exist
SHIFT_GRP_NOT_EXISTS = Shift group does not exist
DESC_GRP_NOT_EXISTS = Descriptor group does not exist
DES_GRP_NOT_EXISTS = Defect code group does not exist
MENU_NOT_ASSOCIATED_PARAMETER_SET = Menu is not associated with parameter set
DEF_GRP_NOT_ALLOW_CREATE_DEF = This defect code group does not allow creating defect codes
DESC_GRP_NOT_ALLOW_CREATE_DESC = This descriptor group does not allow creating descriptors
JOB_GRP_NOT_ALLOW_CREATE_JOB = This work group does not allow creating work orders
DB_CONFIG_NOT_EXISTS = Database configuration does not exist
TEST_SQL_COLUMN_NAME_DUPLICATION_EXCEPTION = Test SQL has duplicate column names
ANALYSIS_DASHBOARD_TEMPLATE_NAME_DUPLICATION_EXCEPTION = Template name duplicated
TEST_VAL_IS_NULL = Test value is empty
PRODUCT_TEST_DUPLICATION_EXCEPTION = Product test duplicated
THE_CACHE_IS_BEING_PROCESSED = The cache is being processed, please try again later
MFPS_NOT_EXISTS = Manufacturing process does not exist
MFND_NOT_EXISTS = Manufacturing node does not exist
PLAN_NOT_EXISTS = Inspection plan does not exist
CHILD_NOT_EXISTS = Sub-plan does not exist
MFPS_PLNT_INCONFORMITY = Manufacturing process factory does not match selected factory
PLEASE_SELECT_FACTORY = Please select factory
STRUCTURE_UNDER_CONFIGURATION = Structure configuration is incomplete
INSPECTION_TYPE_GRP_NAME_DUPLICATION_EXCEPTION = Inspection type group name duplicated
INSPECTION_TYPE_DAT_NAME_DUPLICATION_EXCEPTION = Inspection type name duplicated
START_TIME_GT_END_TIME = Start date is greater than end date
PARAMETER_SET_RELEVANCE_ANALYSIS_DASHBOARD = Parameter set is already associated with analysis dashboard!
INSPECTION_TYPE_DAT_DUPLICATION_EXCEPTION = Some inspection types are in use
MONITOR_SAVE_NUM_EXCEPTION = Monitor cache subgroup count configuration is incorrect
MODEL_UNPUBLISHED = Process model is not published
EFFECTIVE_TOLERANCE_LIMIT = Limit not configured
UNIT_NAME_DUPLICATION_EXCEPTION = Unit name duplicated
MAPPING_DATA_NOT_EXIST = Mapping data does not exist
UWL_GT_USL_EXCEPTION = Alarm limit cannot be greater than specification limit
URL_LT_USL_EXCEPTION = Reasonable limit cannot be less than specification limit
SWITCH_LANGUAGE_NOT_EXISTS = Switch language does not exist
NOT_CLOSE_PART_ALL_VERSION = It is forbidden to close all versions under the product
CHART_TYPE_MISMATCH_EXCEPTION = Chart type mismatch
PART_IS_NOT_NULL = Product cannot be empty
PTRV_IS_NOT_NULL = Product version cannot be empty
PRCS_IS_NOT_NULL = Process cannot be empty
TEST_IS_NOT_NULL = Test item cannot be empty
PLNT_NOT_MATCH = Plant does not match
PLNT_NOT_ACCESSIBLE= Plant not in accessible hierarchy
FACTORY_NOT_EXISTS = Factories do not exist
PUSH_TIME_IS_NOT_NULL = Push time cannot be empty


# SystemExceptionEnum
LENGTH_EXCEPTION = Insufficient permissions
INPUT_STREAM_EXCEPTION = Error reading file
ACCOUNT_EXIST_EXCEPTION = Login account already exists
EMPLOYEE_CODE_EXIST_EXCEPTION = Employee ID already exists
PASSWORD_NOT_NULL_EXCEPTION = Password cannot be empty
OLD_PASSWORD_NOT_SAME_EXCEPTION = Original password does not match
PASSWORD_CANNOT_SAME_HISTORY_EXCEPTION = New password cannot be the same as historical passwords
DEPT_NAME_DUPLICATION_EXCEPTION = Department name duplicated
ROLE_BIND_EXCEPTION = Selected role already has bound accounts
ROLE_NAME_DUPLICATION_EXCEPTION = Role name duplicated
USER_HIER_NOT_EXISTS = User hierarchy does not exist
ACCOUNT_NOT_EXIST_EXCEPTION = Account does not exist or is not activated
PERMISSION_BIND_EXCEPTION = Selected permission template already has bound roles
EMAIL_EXIST_EXCEPTION = Employee email already exists
WECHAT_EXIST_EXCEPTION = Employee WeChat already exists
DING_DING_EXIST_EXCEPTION = Employee DingTalk account already exists
MENU_ANALYSIS_PAGE_EXCEPTION = This menu has associated analysis page
HIERARCHY_TYPE_EXCEPTION = Hierarchy type error
ALARM_RULE_DUPLICATION_EXCEPTION=Duplicate alarm rule name or abbreviation
NOTIFICATION_RULE_NAME_DUPLICATION_EXCEPTION=Duplicate alarm notification rule name


TOKEN_CANNOT_BE_EMPTY = Token cannot be empty
TOKEN_EXPIRED_OR_INVALID = Token has expired or is invalid!
LOGIN_STATUS_EXPIRED = Login status has expired
TOKEN_VERIFICATION_FAILED = Token verification failed


SYSTEM_LOGIN_USER_COUNT_REACHED_LIMIT = The number of logged-in users in the system has reached the limit!
NO_ACCESS_PERMISSION_PLEASE_CONTACT_ADMIN_FOR_AUTHORIZATION = No access permission, please contact administrator for authorization

NO_INTERNAL_ACCESS_PERMISSION_ACCESS_NOT_ALLOWED = No internal access permission, access not allowed
NO_USER_INFO_SET_ACCESS_NOT_ALLOWED = No user information set, access not allowed

REFLECTION_IMPLEMENTATION_CLASS_EXCEPTION = Reflection implementation class exception
ACCESS_TO_THE_METHOD_IS_DENIED = Access to the method is denied
EXCEPTION_OCCURRED_WHILE_EXECUTING_THE_ALARM_METHOD = Exception occurred while executing the alarm method
SAVE_CONDITION_PARSING_ERROR = Save condition parsing error
FAILED_TO_GET_EXPRESSION_TRIGGER = Failed to get expression trigger
FAILED_TO_CREATE_SCHEDULED_TASK = Failed to create scheduled task
FAILED_TO_UPDATE_SCHEDULED_TASK = Failed to update scheduled task
FAILED_TO_EXECUTE_SCHEDULED_TASK_IMMEDIATELY = Failed to execute scheduled task immediately
FAILED_TO_PAUSE_SCHEDULED_TASK = Failed to pause scheduled task
FAILED_TO_RESUME_SCHEDULED_TASK = Failed to resume scheduled task
FAILED_TO_DELETE_SCHEDULED_TASK = Failed to delete scheduled task
PLEASE_ENTER_ANALYSIS_PERSPECTIVE = Please enter analysis perspective
PLEASE_CONVERT_TO_XLSX_FORMAT_FOR_IMPORT = Please convert to xlsx format for import
HIERARCHY_PARSING_ERROR = Hierarchy parsing error

VIOLIN_PLOTS_EXPORT = Violin plot export
BOX_PLOTS_EXPORT = Box plot export
STREAM_TREND_EXPORT = Real-time capability trend chart export
REAL_TIME_CAPABILITY_ANALYSIS = Real-time capability analysis



# New business logic Chinese information
MD5_NOT_EXISTS = MD5 does not exist!
USER_PASSWORD_MUST_FILL = User/password must be filled in
ACCOUNT_LENGTH_BETWEEN_2_20 = Account length must be between 2 and 20 characters
PASSWORD_LENGTH_BETWEEN_5_20 = Password length must be between 5 and 20 characters
IMPORT_FAILED = Import failed
GET_USER_FAILED = Failed to get user
GET_HIERARCHY_FAILED = Failed to get hierarchy

# Data analysis related
PRODUCT = Product
VERSION = Version
PROCESS = Process
TEST = Test
DEFECT_CODE = Defect Code
WORK = Work
BATCH = Batch
SHIFT = Shift
SERIAL_NUMBER = Serial Number
TIME_RANGE = Time Range
DATA_STATISTICS_METHOD = Data Statistics Method
BASED_ON_TIME_GROUPING = Group by Time
TIME_INTERVAL = Time Interval
BASED_ON_SUBGROUP_QUANTITY = Group by Subgroup Quantity
GROUP_COUNT = Group Count
OFFSET = Offset
REAL_TIME_CAPABILITY_ANALYSIS_EXPORT = Real-time Capability Analysis
SUBGROUP_ID = Subgroup ID
SUBGROUP_TIME = Subgroup Time
WORK_ORDER = Work Order
TEST_SEQUENCE_NUMBER = Test Sequence Number
ACTUAL_VALUE = Actual Value
CPK_TARGET_VALUE_1 = CPK Target Value 1
CPK_TARGET_VALUE_2 = CPK Target Value 2
CPK = cpk
CP = cp
PLANT_NAME = Plant Name
MFPS_NAME = Manufacturing Process Name
MFPS_NODE_NAME = Manufacturing Process Node Name
PLAN_NAME = Inspection Plan Name
CHILD_PLAN_NAME = Child Plan Name
PART_NAME = Product Name
PTRV_NAME = Revision Name
PRCS_NAME = Process Name
LOT_NAME = Lot Name
JOB_GRP_NAME = Job Group Name
JOB_NAME = Job Name
SHIFT_GRP_NAME = Shift Group Name
SHIFT_NAME = Shift Name
PUSH_TIME = Push Time
ERROR_MSG = Error Message
F_SAMPLING_TASK_CONFIG_ID = Sampling Task Configuration ID
TEST_DATA = Test Data
TEST_NAME = Test Name
TEST_NO = Test Number
TEST_VAL = Test Value
SUB_TEST_VAL = Sub-Test Value





# System constant information
LOGIN_SUCCESS_MESSAGE = Login successful
LOGOUT_MESSAGE = Logout successful
DATA_VOLUME_TOO_LARGE = Data volume too large
AUTHENTICATION_CENTER_STARTUP_SUCCESS = Authentication center started successfully


# File operation exceptions
FILE_UPLOAD_FAILED = File upload failed
FILE_SHEET_NOT_EXIST = File sheet does not exist
APPLICATION_CONTEXT_NULL = "applicaitonContext attribute is null, please check if SpringContextHolder is injected!"

# Common information
SECOND = Second
MINUTE = Minute
HOUR = Hour
DAY = Day
WEEK = Week
MONTH = Month
YEAR = Year
QUARTER = Quarter

ANALYSIS_REPORT = Analysis Report
PARAMETER_SET = Parameter Set
DATE_RANGE = Date Range
EXPORT_DATE = Export Date
EXPORT_USER = Export User
DATA_DETAIL = Data Detail

TOLERANCE_LIMIT = Tolerance Limit
CONTROL_LIMIT = Control Limit
NOT_EXIST = Not Exist
TEST_NUMBER = Test Number

PROCESS_TASK_NO_MATCHING_RULE_FOUND = Process task ({}/{}/{}) found no matching rule
PROCESS_TASK_TOO_MANY_RULES_FOUND = Process task ({}/{}/{}) found too many rules ({})

PARAMETER_REQUEST_DATA_MISSING_EXCEPTION = Parameter [requestData] missing exception!
PARAMETER_REQUEST_DATA_NULL_EXCEPTION = Parameter [requestData] parsing null pointer exception!
PARAMETER_CONTENT_PARSING_EXCEPTION = Parameter [content] parsing exception!
MONTHLY_DATA_ABNORMAL = Monthly data abnormal:
MANUALLY_TERMINATE_FLOW = Manually terminate flow
TIMEOUT = Timeout


VERIFICATION_CODE_CANNOT_BE_EMPTY = Verification code cannot be empty
VERIFICATION_CODE_EXPIRED = Verification code has expired
VERIFICATION_CODE_NOT_EXIST_OR_EXPIRED = Verification code does not exist or has expired
VERIFICATION_CODE_ERROR = Verification code error
PARAMETER_DOES_NOT_MEET_SPECIFICATIONS_CANNOT_QUERY = Parameter does not meet specifications, cannot query
PARAMETER_HAS_SQL_INJECTION_RISK = Parameter has SQL injection risk
TOKEN_NOT_PROVIDED = Token not provided
INVALID_TOKEN = Invalid token
REDIS_CONFIG_INIT_FAILED = Redis config init failed

EXCEPTION_TIME_STRING_PARSING_ERROR = The time string cannot be parsed 
INSERT_SUBGROUP_DATA_COLLECT_EXCEPTION = Added subgroup data exceptions
SAMPLING_CONFIG_NOT_EXIST = The sampling configuration does not exist
SAMPLING_CONFIG_VALIDATE_FAILED = Sample configuration validation failed
SAMPLING_TASK_FAIL = The sampling task failed

SYSTEM_MESSAGE = system message
EMAIL = email
QY_WECHAT = wechat
DING_DING = dingTalk

DATA_SUBGROUP_COLLECT_FAIL = Inspection data consume fail
INSPECTION_DATA_CONSUME_FAIL = Inspection data consumption failure, error message
SEND_INSPECTION_DATA_FAILED = Failed to send inspection data, error message
AUTH_FAIL_NO_HARDWARE_ID = Authorization verification fails, and the machine number does not exist
AUTH_FAIL_NO_SERVICE = Authorization verification fails, and the system service has stopped!

SAMPLE_CONFIG_NAME_NOT_NULL = Sample configuration name cannot be empty
SAMPLING_TYPE_NOT_NULL = Sampling type cannot be empty
SAMPLING_STRATEGY_NOT_NULL = Sampling strategy cannot be empty
SAMPLING_METHOD_NOT_NULL = Sampling method cannot be empty
PLANT_NOT_NULL = Plant cannot be empty
TIME_INTERVAL_NOT_NULL = Time interval cannot be empty
PRODUCTION_BEAT_COUNT_NOT_NULL = Production beat count cannot be empty
EX_TIME_NOT_NULL = Expiration time cannot be empty
NOTICE_USER_AND_ROLE_CANNOT_BE_EMPTY = Notice user and notice role cannot be empty at the same time

INSPECTION_PLAN_TIMEOUT_ALARM = Inspection plan timeout alarm
