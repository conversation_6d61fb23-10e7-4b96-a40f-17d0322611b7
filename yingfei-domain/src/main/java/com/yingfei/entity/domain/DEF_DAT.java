package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存缺陷信息表
 * @TableName DEF_DAT
 */
@TableName(value ="DEF_DAT")
@Data
public class DEF_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DEF;

    /**
     * 缺陷所关联的缺陷组ID
     */
    private Long F_DFGP = 0L;

    /**
     * 缺陷名称
     */
    private String F_NAME;

    /**
     * 缺陷图片
     */
    private String F_IMAGE = "";

    /**
     * 缺陷因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static DEF_DAT init() {
        DEF_DAT data = new DEF_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }


}
