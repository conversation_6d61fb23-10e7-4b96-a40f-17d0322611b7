package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存序列号信息表
 * @TableName SN_INF
 */
@TableName(value ="SN_INF")
@Data
public class SN_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_SN;

    /**
     * 分公司主键
     */
    private Long F_DIV = 0L;

    /**
     * 序列号关联的产品ID(PART_INF)
     */
    private Long F_PART = 0L;

    /**
     * 序列号名称
     */
    private String F_NAME;

    /**
     * 序列号因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;


    public static SN_INF init() {
        SN_INF data = new SN_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }

}
