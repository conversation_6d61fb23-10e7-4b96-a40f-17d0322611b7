package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 原始检验数据表
 * @TableName raw_inspection
 */
@TableName(value = "RAW_INSPECTION")
@Data
public class RAW_INSPECTION {

    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 抽样任务配置ID
     */
    private Long F_SAMPLING_TASK_CONFIG_ID;

    /**
     * 产品名称
     */
    private String F_PART_NAME;

    /**
     * 过程名称
     */
    private String F_PRCS_NAME;

    /**
     * 产品版本名称
     */
    private String F_PTRV_NAME;

    /**
     * 测试名称
     */
    private String F_TEST_NAME;

    /**
     * 批次名称
     */
    private String F_LOT_NAME;

    /**
     * 工单名称
     */
    private String F_JOB_NAME;

    /**
     * 工单组名称
     */
    private String F_JOB_GRP_NAME;

    /**
     * 班次名称
     */
    private String F_SHIFT_NAME;

    /**
     * 班次组名称
     */
    private String F_SHIFT_GRP_NAME;

    /**
     * 描述符信息
     */
    private String F_DESC_DATA;

    /**
     * 工艺流程名称
     */
    private String F_MFPS_NAME;

    /**
     * 工艺节点名称
     */
    private String F_MFND_NAME;

    /**
     * 检查计划名称
     */
    private String F_PLAN_NAME;

    /**
     * 测试数据，以JSON格式存储
     */
    private String F_TEST_DATA;

    /**
     * 接收时间
     */
    private Date F_RECEIVE_TIME;

    /**
     * 状态，0：未被抽样，1：已被抽样
     */
    private Integer  F_STATUS = 0;

    /**
     * 抽样时间
     */
    private Date F_SAMPLING_TIME;
}

