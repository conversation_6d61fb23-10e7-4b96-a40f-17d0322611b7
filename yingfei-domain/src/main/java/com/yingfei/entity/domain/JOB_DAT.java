package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 储存工单信息表
 * @TableName JOB_DAT
 */
@TableName(value ="JOB_DAT")
@Data
public class JOB_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_JOB;

    /**
     * 工单所关联的工单组ID
     */
    private Long F_JBGP = 0L;

    /**
     * 工单名称
     */
    private String F_NAME;

    /**
     * 工单因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 工单开放使用时间按
     */
    private Date F_RELEASE_TIME;

    /**
     * 工单关闭使用时间按
     */
    private Date F_CLOSE_TIME;

    public static JOB_DAT init() {
        JOB_DAT data = new JOB_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
