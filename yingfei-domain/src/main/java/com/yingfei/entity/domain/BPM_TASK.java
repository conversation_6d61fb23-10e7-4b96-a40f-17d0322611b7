package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 工作流的流程任务的拓展表
 * @TableName BPM_TASK
 */
@TableName(value ="BPM_TASK")
@Accessors(chain = true)
@Data
public class BPM_TASK extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_TASK;

    /**
     * 任务的审批人
     */
    private String F_ASSIGNEE_USER;

    /**
     * 任务名称
     */
    private String F_NAME;

    /**
     * 任务编号
     */
    private String F_TASK_NUM;

    /**
     * 任务的结果
     */
    private Integer F_RESULT;

    /**
     * 审批建议
     */
    private String F_REASON;

    /**
     * 任务的结束时间
     */
    private Date F_END_TIME;

    /**
     * 流程定义的编号
     */
    private String F_PROCESS_DEFINITION;

    /**
     * 流程实例的编号
     */
    private String F_PROCESS_INSTANCE;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 修改用户
     */
    private Long F_EDUE;

    /**
     * 同一个流程的唯一标识
     */
    private String F_IDENTIFICATION = "0";

    /**
     * 表单字段json
     */
    private String F_FIELDS;

    /**
     * 任务审核人
     */
    private Long F_AUDIT_USER;

    /**
     * 是否上传佐证(0:否 1:是)
     */
    private Integer F_IS_UPLOAD = 0;

    public static BPM_TASK init() {
        BPM_TASK bpmTask = new BPM_TASK();
        BeanUtils.setAllFieldsToNull(bpmTask);
        return bpmTask;
    }
}
