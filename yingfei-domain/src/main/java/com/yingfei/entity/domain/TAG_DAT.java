package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存标签信息表
 * @TableName TAG_DAT
 */
@TableName(value ="TAG_DAT")
@Data
public class TAG_DAT extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_TAG;

    /**
     * 标签所关联的标签组ID(TAG_GRP)
     */
    private Long F_TGGP = 0L;

    /**
     * 标签名称
     */
    private String F_NAME;

    /**
     * 标签因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static TAG_DAT init() {
        TAG_DAT data = new TAG_DAT();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }


}
