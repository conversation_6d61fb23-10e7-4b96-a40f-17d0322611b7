package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import lombok.Data;

/**
 * 流程模型表
 * @TableName BPM_MODE
 */
@TableName(value ="BPM_MODE")
@Data
public class BPM_MODE extends BaseEntity {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_MODE;

    /**
     * 名称
     */
    private String F_NAME;

    /**
     * 编码
     */
    private String F_KEY;

    /**
     * 分类
     */
    private String F_CATEGORY;

    /**
     * 版本
     */
    private String F_VERSION;

    /**
     * 流程XML
     */
    private String F_BPMN_XML;

    /**
     * 表单信息
     */
    private String F_FROM_INFO;

    /**
     * 流程部署id
     */
    private String F_DEPLOYMENT;

    /**
     * 描述
     */
    private String F_DESCRIPTION;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 修改用户
     */
    private Long F_EDUE;

}
