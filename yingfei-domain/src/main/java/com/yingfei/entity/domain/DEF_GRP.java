package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 储存缺陷组信息表
 * @TableName DEF_GRP
 */
@TableName(value ="DEF_GRP")
@Data
public class DEF_GRP extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_DFGP;

    /**
     * 分公司主键
     */
    private Long F_DIV = 0L;

    /**
     * 缺陷组名称
     */
    private String F_NAME;

    /**
     * 缺陷组因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 是否允许创建新值(0:不允许  1:允许)
     */
    private Integer F_TYPE = 0;

    /**
     * 输入新值时是否提示(0:不提示  1:提示)
     */
    private Integer F_HINT = 1;

    public static DEF_GRP init() {
        DEF_GRP data = new DEF_GRP();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
