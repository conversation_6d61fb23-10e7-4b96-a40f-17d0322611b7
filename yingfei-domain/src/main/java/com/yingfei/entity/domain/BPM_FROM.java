package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 工作流的表单定义表
 * @TableName BPM_FROM
 */
@TableName(value = "BPM_FROM")
@Data
public class BPM_FROM extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_FROM;

    /**
     * 表单名
     */
    private String F_NAME;

    /**
     * 开启状态
     */
    private Integer F_STATUS = 0;

    /**
     * 表单的配置
     */
    private String F_CONF;

    /**
     * 表单项的数组
     */
    private String F_FIELDS;

    /**
     * 备注
     */
    private String F_REMAEK;

    /**
     * 是否删除(0:否 1:是)
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 创建用户
     */
    private Long F_CRUE;

    /**
     * 修改用户
     */
    private Long F_EDUE;

    /**
     * 表单类型(0:自定义表单 1:固定表单)
     */
    private Integer F_TYPE = 0;

    public static BPM_FROM init() {
        BPM_FROM bpmFrom = new BPM_FROM();
        BeanUtils.setAllFieldsToNull(bpmFrom);
        return bpmFrom;
    }
}
