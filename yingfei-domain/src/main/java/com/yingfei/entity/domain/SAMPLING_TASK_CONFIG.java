package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.handler.IntegerListTypeHandler;
import com.yingfei.common.core.handler.LongListTypeHandler;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.List;

/**
 * 抽样任务配置表
 *
 * @TableName SAMPLING_TASK_CONFIG
 */
@TableName(value = "SAMPLING_TASK_CONFIG")
@Data
public class SAMPLING_TASK_CONFIG extends BaseEntity {

    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_ID;

    /**
     * 配置名称
     */
    private String F_NAME;

    /**
     * 配置描述
     */
    private String F_DESCRIPTION = "";

    /**
     * 抽样类型：1-时间抽样，2-生产节拍
     */
    private Integer F_SAMPLING_TYPE;

    /**
     * 抽样数量
     */
    private Integer F_SAMPLING_COUNT;

    /**
     * 抽样策略：1-基于产品，2-基于过程，3-基于产品过程，5-基于推送数量
     */
    private Integer F_SAMPLING_STRATEGY;

    /**
     * 抽样方式：1-随机，2-时间升序，3-时间倒序
     */
    private Integer F_SAMPLING_METHOD;

    /**
     * 时间间隔（分钟）- 用于时间抽样
     */
    private Integer F_TIME_INTERVAL;

    /**
     * 生产节拍数量 - 用于生产节拍抽样
     */
    private Integer F_PRODUCTION_BEAT_COUNT;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Integer F_ENABLED = YesOrNoEnum.YES.getType();

    /**
     * 异常通知用户
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> F_NOTICE_USER;

    /**
     * 异常通知角色
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> F_MOTICE_RULE;

    /**
     * 异常通知类型 notificationTypeEnum
     * 1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> F_NOTICE_TYPE;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    /**
     * 工厂ID（配置归属于工厂这一层级）
     */
    private Long F_PLNT = 0L;

    public static SAMPLING_TASK_CONFIG init() {
        SAMPLING_TASK_CONFIG data = new SAMPLING_TASK_CONFIG();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
