package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.bean.BeanUtils;
import lombok.Data;

import java.util.Date;

/**
 * 储存批次信息表
 * @TableName LOT_INF
 */
@TableName(value ="LOT_INF")
@Data
public class LOT_INF extends BaseEntity {
    /**
     * 记录主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long F_LOT;

    /**
     * 分公司主键
     */
    private Long F_DIV = 0L;

    /**
     * 批次关联的产品ID(PART_INF)
     */
    private Long F_PART = 0L;

    /**
     * 批次关联的过程ID，IQC使用(PRCS_INF)
     */
    private Long F_PRCS = 0L;

    /**
     * 批次名称
     */
    private String F_NAME;

    /**
     * 批量，IQC使用，默认值为0
     */
    private Integer F_COUNT = 0;

    /**
     * 批次开放使用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date F_RELEASE_TIME;

    /**
     * 批次关闭使用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date F_CLOSE_TIME;

    /**
     * 批次因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

    public static LOT_INF init() {
        LOT_INF data = new LOT_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }


}
