package com.yingfei.entity.vo;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
* 菜单表
* @TableName MENU_INF
*/
@Data
@ApiModel
public class MENU_INF_VO extends BaseEntity {

    /**
    * 菜单id
    */
    @ApiModelProperty("菜单id")
    private Long F_MENU;
    /**
    * 菜单名称
    */
    @NotBlank(message="[菜单名称]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("菜单名称")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_NAME;
    /**
    * 菜单顺序
    */
    @NotNull(message="[菜单顺序]不能为空")
    @ApiModelProperty("菜单顺序")
    private Integer F_ORDER;
    /**
    * 菜单路由
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("菜单路由")
    private String F_PATH;
    /**
    * 菜单类型(0目录 1菜单 2按钮)
    */
    @NotNull(message="[菜单类型(0目录 1菜单 2按钮)]不能为空")
    @ApiModelProperty("菜单类型(0目录 1菜单 2按钮)")
    private Integer F_TYPE;

    @ApiModelProperty("外链地址")
    private String  F_EXTERNAL_LINK;

    /**
    * 自定义菜单几乘几
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("自定义菜单几乘几")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_DES;
    /**
    * 父级菜单id
    */
    @NotNull(message="[父级菜单id]不能为空")
    @ApiModelProperty("父级菜单id")
    private Long F_PARENT;
    /**
    * 页面唯一标识
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("页面唯一标识")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_CODE;
    /**
    * 权限标识
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("权限标识")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_PERMS;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;
    /**
    * 页面类型(0:系统定义,1:聚合分析,2:单向分析,3:自定义)
    */
    @ApiModelProperty("页面类型(0:系统定义,1:聚合分析,2:单向分析,3:自定义)")
    private Integer F_PAGE;

    @ApiModelProperty("角色id")
    private Long F_ROLE;

    private List<Long> menuIds;
}
