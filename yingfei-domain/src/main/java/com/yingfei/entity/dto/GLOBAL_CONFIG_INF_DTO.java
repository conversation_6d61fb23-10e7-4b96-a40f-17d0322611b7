package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GLOBAL_CONFIG_INF_DTO extends BaseEntity {

    @ApiModelProperty("记录主键")
    private Long F_GC;

    @ApiModelProperty("配置信息")
    private String F_DATA;

    @ApiModelProperty("模板名称")
    private String F_NAME;

    @ApiModelProperty("模板描述")
    private String F_DESC;

    @ApiModelProperty("所属人员ID 0:全局")
    private Long F_EMPL = 0L;

    @ApiModelProperty("所属人员名称")
    private String enplName;

    @ApiModelProperty("数据类型 0:全局配置 1:控制图配置")
    private Integer F_TYPE = 0;

    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;

    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    @ApiModelProperty("删除标识 0:未删除 1:已删除")
    private Integer F_DEL;
}
