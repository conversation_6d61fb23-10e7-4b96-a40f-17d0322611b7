package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 量具连接参数表
* @TableName GAUGE_CONNECTION
*/
@Data
public class GAUGE_CONNECTION_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_GICP;
    /**
    * 量具接口表id
    */
    @ApiModelProperty("量具接口表id")
    private Long F_GAIN;
    /**
    * Agent表id
    */
    @ApiModelProperty("Agent表id")
    private Long F_GAAG;
    /**
    * 端口设置json(com口和通讯参数)
    */
    @ApiModelProperty("端口设置json(com口和通讯参数)")
    private String F_CONFIG;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    @ApiModelProperty("量具接口配置信息")
    private GAUGE_INTERFACE_DTO gaugeInterfaceDto;

    @ApiModelProperty("量具连接配置")
    private GAUGE_CONNECTION_CONFIG_DTO gaugeConnectionConfigDto;

    @ApiModelProperty("量具Agent名称")
    private String agentName;

    @ApiModelProperty("量具Agent信息")
    private GAUGE_AGENT_DTO gaugeAgentDto;
}
