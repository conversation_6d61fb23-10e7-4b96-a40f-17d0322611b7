package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.List;

/**
* 储存报警规则模板信息表
* @TableName ACTIVED_RULE_TEMPLATE_INF
*/
@Data
public class ACTIVED_RULE_TEMPLATE_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_ARTP;
    /**
    * 分公司主键
    */
    @ApiModelProperty("分公司主键")
    private Long F_DIV = 0L;
    /**
    * 报警规则模板名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("报警规则模板名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 图表1所激活的报警规则
    */
    @ApiModelProperty("图表1所激活的报警规则")
    private String F_CHART_ONE;
    /**
    * 图表2所激活的报警规则
    */
    @ApiModelProperty("图表2所激活的报警规则")
    private String F_CHART_TWO;
    /**
    * 图表3所激活的报警规则
    */
    @ApiModelProperty("图表3所激活的报警规则")
    private String F_CHART_THREE;
    /**
    * 报警规则模板因子，默认为1
    */
    @ApiModelProperty("报警规则模板因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    private List<String> chartOneList;

    private List<String> chartTwoList;

    private List<String> chartThreeList;
}
