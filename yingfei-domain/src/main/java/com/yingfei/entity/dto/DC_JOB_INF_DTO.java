package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 自动采集任务配置
* @TableName DC_JOB_INF
*/
@Data
public class DC_JOB_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_DCJB;
    /**
    * 名称
    */
    @ApiModelProperty("名称")
    private String F_NAME;
    /**
    * 数据库地址
    */
    @ApiModelProperty("数据库地址")
    private String F_URL;
    /**
    * 数据库账号
    */
    @ApiModelProperty("数据库账号")
    private String F_USERNAME;
    /**
    * 数据库密码
    */
    @ApiModelProperty("数据库密码")
    private String F_PASSWORD;
    /**
    * 数据库配置表主键
    */
    @ApiModelProperty("数据库配置表主键")
    private Long F_DBCO;
    /**
    * 任务开始时间
    */
    @ApiModelProperty("任务开始时间")
    private Date F_START_TIME;
    /**
    * 时间间隔
    */
    @ApiModelProperty("时间间隔")
    private Integer F_TIME_INTERVAL;
    /**
    * 时间类型
    */
    @ApiModelProperty("时间类型")
    private Integer F_TIME_TYPE;
    /**
    * 处理SQL
    */
    @ApiModelProperty("处理SQL")
    private String F_SQL;
    /**
    * 启动SQL
    */
    @ApiModelProperty("启动SQL")
    private String F_START_SQL;

    @ApiModelProperty("是否启用启动sql(0:否 1:是)")
    private String F_START_TYPE;

    /**
     * 数据库名称
     */
    @ApiModelProperty("数据库名称")
    private String F_DB_NAME;

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;
}
