package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.INSPECTION_PLAN_RECORD_INF;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
* 检查计划表
* @TableName INSPECTION_PLAN_INF
*/
@Data
public class INSPECTION_PLAN_INF_DTO extends BaseEntity {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    private Long F_PLAN;
    /**
    * 流程结构主键
    */
    @ApiModelProperty("流程结构主键")
    private Long F_MFPS;
    /**
    * 节点表主键
    */
    @ApiModelProperty("节点表主键")
    private Long F_MFND;
    /**
    * 计划名称  在整个工艺节点中必须唯一
    */
    @ApiModelProperty("计划名称  在整个工艺节点中必须唯一")
    private String F_NAME;

    /**
     * 最大样本量
     */
    @ApiModelProperty("最大样本量")
    private Integer F_SBNO;

    /**
    * 主体数据
    */
    @ApiModelProperty("主体数据")
    private String F_DATA;

    /**
     * 子计划数据
     */
    @ApiModelProperty("子计划数据")
    private String F_CHILD;

    /**
    * 创建用户
    */
    @ApiModelProperty("创建用户")
    private Long F_CRUE;
    /**
    * 编辑用户
    */
    @ApiModelProperty("编辑用户")
    private Long F_EDUE;

    /**
    * 子计划数量
    */
    @ApiModelProperty("子计划数量")
    private Integer F_COUNT;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 上传图片
     */
    @ApiModelProperty("上传图片")
    private String F_IMG;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String F_EMPL;

    /**
     * 角色id
     */
    @ApiModelProperty("角色id")
    private String F_ROLE;

    /**
     * 批次是否开启(0:关闭  1:开启)
     */
    @ApiModelProperty("批次是否开启(0:关闭  1:开启)")
    private Integer F_LOT_STATUS;

    /**
     * 检验计划类型(0:静态,1:动态)
     */
    @ApiModelProperty("检验计划类型(0:静态,1:动态)")
    private Integer F_TYPE = 0;

    /**
     * 流程名称
     */
    private String F_MFPSName;

    /**
     * 节点名称
     */
    private String F_MFNDName;


    /**
     * 筛选条件list
     */
    @ApiModelProperty("筛选条件list")
    private INSPECTION_PLAN_CONFIGURATION_DTO inspectionPlanConfigurationDto;


    /**
     * 子计划list
     */
    @ApiModelProperty("子计划list")
    private Map<String,List<INSPECTION_PLAN_CHILD_DTO>> inspectionPlanChildDtoList;

    /**
     * 临时保存子组
     */
    private List<SubgroupDataVO> tempSaveInfoList;

    /**
     * 获取待处理的子组数量
     */
    private Integer waitDealNum;

    /**
     * 检查计划定时执行记录最新数据
     */
    private INSPECTION_PLAN_RECORD_INF inspectionPlanRecordInf;
}
