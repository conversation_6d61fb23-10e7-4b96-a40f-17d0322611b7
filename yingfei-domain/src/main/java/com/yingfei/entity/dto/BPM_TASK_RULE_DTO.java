package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* Bpm 任务规则表
* @TableName BPM_TASK_RULE
*/
@Data
@Accessors(chain = true)
public class BPM_TASK_RULE_DTO extends BaseEntity {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    private Long F_RULE;
    /**
    * 流程模型的编号
    */
    @ApiModelProperty("流程模型的编号")
    private Long F_MODE;
    /**
    * 流程定义的编号
    */
    @ApiModelProperty("流程定义的编号")
    private String F_PROCESS_DEFINITION;
    /**
    * 流程任务定义的 key
    */
    @ApiModelProperty("流程任务定义的 key")
    private String F_TASK_KEY;
    /**
    * 规则类型
    */
    @ApiModelProperty("规则类型")
    private Integer F_TYPE;
    /**
    * 规则值，JSON 数组
    */
    @ApiModelProperty("规则值，JSON 数组")
    private String F_OPTIONS;
    /**
    * 是否删除(0:否 1:是)
    */
    @ApiModelProperty("是否删除(0:否 1:是)")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 创建用户
    */
    @ApiModelProperty("创建用户")
    private Long F_CRUE;
    /**
    * 修改用户
    */
    @ApiModelProperty("修改用户")
    private Long F_EDUE;

    @ApiModelProperty("流程任务定义的名字")
    private String taskDefinitionName;

    /**
     * 表单表id
     */
    @ApiModelProperty("表单表id")
    private Long F_FROM;

    @ApiModelProperty("排序字段")
    private Integer num;

    @ApiModelProperty("表单名称")
    private String fromName;

    @ApiModelProperty("表单类型")
    private Integer fromType;
}
