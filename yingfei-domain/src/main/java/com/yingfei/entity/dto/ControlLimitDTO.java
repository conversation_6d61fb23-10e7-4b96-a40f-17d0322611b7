package com.yingfei.entity.dto;

import com.yingfei.entity.enums.ControlChartSingleEnum;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 2023-12-13 下午 2:55
 * @description 控制限计算（基于数据库中控制限记录）DTO
 */
@Data
@ApiModel
public class ControlLimitDTO {
    /**
     * 样本量
     */
    @ApiModelProperty("样本量")
    private Double n;
    /**
     * 过程均值
     */
    @ApiModelProperty("过程均值")
    private Double F_MEAN;
    /**
     * 过程西格玛
     */
    @ApiModelProperty("过程西格玛")
    private Double F_SP;

    /**
     * 西格玛数量(数值范围：1.623~4.417)
     */
    private Double F_SIGMA_COUNT;

    /**
     * 件内西格玛
     */
    @ApiModelProperty("件内西格玛")
    private Double F_SW;
    /**
     * 过程西格玛下（如果用户有特殊要求，会定义过程西格玛下）
     */
    @ApiModelProperty("过程西格玛下")
    private Double F_SPL;
    /**
     * 计算出的CL值
     */
    @ApiModelProperty("计算出的CL值")
    private Double CL;

    /**
     * 计算出的UCL值
     */
    @ApiModelProperty("计算出的UCL值")
    private Double UCL;

    /**
     * 计算出的LCL值
     */
    @ApiModelProperty("计算出的LCL值")
    private Double LCL;
    /**
     * 为所有历史子组极差的均值  极差=每个子组最大值-最小值
     */
    @ApiModelProperty("为所有历史子组极差的均值")
    private Double R;

    /**
     * 为所有历史子组移动极差的均值   移动极差 = (当前子组的实际值-上一子组实际值)的绝对值
     */
    @ApiModelProperty("为所有历史子组移动极差的均值")
    private Double MR;

    /**
     * 为所有子组均值的均值
     */
    @ApiModelProperty("为所有子组均值的均值")
    private Double Mean;

    /**
     * 当前子组均值
     */
    private Double nowMean;

    /**
     * 当前子组极差
     */
    private Double nowR;

    /**
     * 当前子组移动极差
     */
    private Double nowMR;

    /**
     * 当前子组标准差
     */
    private Double nowSD;

    /**
     * 公差限
     */
    private SPEC_INF_DTO specInfDto;

    /**
     * 获取R的值  极差=每个子组最大值-最小值
     *
     * @param sgrpExtList
     * @return
     */
    public static Double R(List<SubgroupDataVO> sgrpExtList) {
        List<Double> list = new ArrayList<>();
        /*极差=每个子组最大值-最小值*/
        sgrpExtList.forEach(subgroupDataVO -> {
            List<SGRP_VAL_CHILD_DTO.Test> testList = subgroupDataVO.getSgrpValChildDtoList().get(0).getTestList();
            Double max = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).max(Double::compareTo).orElse(0d);
            Double min = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal).filter(Objects::nonNull).min(Double::compareTo).orElse(0d);
            list.add(max - min);
        });
        /*算所有极差的均值*/
        return list.stream().mapToDouble(Double::doubleValue).average().orElse(0d);
    }

    /**
     * 获取MR的值   移动极差 = (当前子组的实际值-上一子组实际值)的绝对值
     *
     * @return
     */
    public static Double MR(List<SubgroupDataVO> sgrpExtList) {
        /*移动极差 = (当前子组的实际值-上一子组实际值)的绝对值*/
        List<Double> list = new ArrayList<>();
        AtomicReference<Double> d = new AtomicReference<>(0d);
        sgrpExtList.forEach(subgroupDataVO -> {
            Double sum = subgroupDataVO.getSgrpValChildDtoList().get(0).getTestList().get(0).getTestVal();
            if (d.get() == 0d) {
                d.set(sum);
            } else {
                list.add(Math.abs(sum - d.get()));
                d.set(sum);
            }
        });
        /*算所有移动极差的均值*/
        return list.stream().mapToDouble(Double::doubleValue).average().orElse(0d);
    }

    /**
     * 获取Mean的值  为所有子组均值的均值
     *
     * @return
     */
    public static Double Mean(List<SubgroupDataVO> sgrpExtList) {
        List<Double> list = new ArrayList<>();
        /*算每个子组的均值*/
        sgrpExtList.forEach(subgroupDataVO -> {
            List<SGRP_VAL_CHILD_DTO.Test> testList = subgroupDataVO.getSgrpValChildDtoList().get(0).getTestList();
            Double aDouble = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal)
                    .filter(Objects::nonNull).reduce(Double::sum).orElse(0d) / testList.size();
            list.add(aDouble);
        });
        /*算所有子组均值的均值*/
        return list.stream().mapToDouble(Double::doubleValue).average().orElse(0d);
    }

}
