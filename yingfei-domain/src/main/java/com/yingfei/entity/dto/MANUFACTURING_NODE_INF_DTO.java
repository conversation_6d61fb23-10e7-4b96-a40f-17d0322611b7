package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* 流程节点表
* @TableName MANUFACTURING_NODE_INF
*/
@Data
public class MANUFACTURING_NODE_INF_DTO extends BaseEntity {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    private Long F_MFND;

    /**
     * 节点名称
     */
    @ApiModelProperty("节点名称")
    private String F_NAME;

    /**
    * 流程表主键
    */
    @ApiModelProperty("流程表主键")
    private Long F_MFPS;
    /**
    * 节点筛选条件
    */
    @ApiModelProperty("节点筛选条件")
    private String F_DATA;
    /**
    * 创建用户
    */
    @ApiModelProperty("创建用户")
    private Long F_CRUE;
    /**
    * 编辑用户
    */
    @ApiModelProperty("编辑用户")
    private Long F_EDUE;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date F_CRTM;
    /**
    * 编辑时间
    */
    @ApiModelProperty("编辑时间")
    private Date F_EDTM;
    /**
    * 采集计划数量
    */
    @ApiModelProperty("采集计划数量")
    private Integer F_COUNT;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 筛选条件list
     */
    @ApiModelProperty("筛选条件list")
    private List<PARAMETER_CHILD_DTO> parameterChildDtoList;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 流程对应工厂
     */
    private Long plnt;

    /**
     * 节点下检查计划对应子组
     */
    List<SubgroupDataDTO> subgroupDataDTOList = new ArrayList<>();

    /**
     * 子组对应报警事件
     */
    List<EVNT_INF_DTO> evntInfDtoList;
}
