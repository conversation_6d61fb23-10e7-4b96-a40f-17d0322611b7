package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 用户菜单列配置
*/
@Data
public class LAYOUT_INF_DTO extends BaseEntity {

    /**
     * 记录主键
     */
    @ApiModelProperty("记录主键")
    private Long F_ID;
    /**
     * 保存用户id (如果是全局配置  该id默认是0)
     */
    @ApiModelProperty("保存用户id (如果是全局配置  该id默认是0)")
    private Long F_EMPL;

    /**
     * 菜单ID
     */
    @ApiModelProperty("菜单ID")
    private Long F_MENU;
    /**
     * 列保存信息
     */
    @ApiModelProperty("列保存信息")
    private String F_DATA;
    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 1=系统页面布局；2=看板风格；3=模型名称
     */
    private Integer F_TYPE = 1;
}
