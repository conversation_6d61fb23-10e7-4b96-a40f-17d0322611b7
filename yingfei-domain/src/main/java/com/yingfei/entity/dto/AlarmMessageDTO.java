package com.yingfei.entity.dto;

import lombok.Data;

/**
 * 报警信息类
 */
@Data
public class AlarmMessageDTO {

    /**
     * 报警描述名称
     */
    private String name;

    /**
     * 报警类型(1:公差限  2:控制限)
     */
    private Integer type;

    /**
     * 报警数量
     */
    private Integer num;

    /**
     * 报警图表名称
     */
    private String chart;

    /**
     * 公差限或控制限对应的描述id
     */
    private Long id;

    /**
     * 满足报警是否将子组失效(0:不失效 1:失效)
     */
    private Integer flag = 0;

    /**
     * 报警监控目标算出来的cp
     */
    private Double cp;

    /**
     * 报警监控目标算出来的cpk
     */
    private Double cpk;

    /**
     * 满足的控制限枚举类型
     * @see com.yingfei.entity.enums.StatisticalViolationTypeEnum
     */
    private Integer ruleType;

    /**
     * 对比值
     */
    private Double compareValue;

    /**
     * 实际值
     */
    private Double actualValue;

    /**
     * 对比值和实际值连接符(如:>,<)
     */
    private String connector;

}
