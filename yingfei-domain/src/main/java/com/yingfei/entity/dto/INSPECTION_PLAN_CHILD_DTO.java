package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 检查计划  子计划json对象
 */
@Data
@ApiModel
public class INSPECTION_PLAN_CHILD_DTO {

    /**
     * 子计划id
     */
    @ApiModelProperty("子计划id")
    private Long childId;

    /**
     * 子计划测试id
     */
    @ApiModelProperty("子计划测试id")
    private String testId;

    /**
     * 子计划测试名称
     */
    @ApiModelProperty("子计划测试名称")
    private String testName;

    /**
     * 子计划样本量
     */
    @ApiModelProperty("子计划样本量")
    private Integer num;

    /**
     * 子计划序号
     */
    @ApiModelProperty("子计划序号")
    private Integer order;

    /**
     * 数据来源信息(0:键盘 1:量具  2:计算  3:数据库取值)
     */
    @ApiModelProperty("数据来源信息")
    private Integer dataSources;

    /**
     * 数据计算公式
     */
    @ApiModelProperty("数据计算公式")
    private String formula;

    /**
     * 是否打开样本量
     */
    private boolean sampleNumOpen = false;

    /**
     * 量具id
     */
    @ApiModelProperty("量具设备id")
    private String equipId;

    /**
     * 串口信息
     */
    @ApiModelProperty("串口信息")
    private String serialPort;

    @ApiModelProperty("硬件标识")
    private String hardwareId;

    /**
     * 数据库取值筛选条件
     */
    @ApiModelProperty("数据库取值筛选条件")
    private String databaseValue;

    /**
     * 是否激活边界
     */
    @ApiModelProperty("是否激活边界")
    private boolean boundary = false;

    /**
     * 子计划属性选择列表
     *
     * @see com.yingfei.entity.enums.ChildPlanAttributesEnum
     */
    @ApiModelProperty("子计划属性选择列表")
    private List<String> childPlanAttributesList;

    /**
     * 数据库取值配置
     */
    @ApiModelProperty("数据库取值配置")
    private DataValConfigDTO dataValConfigDTO;
}
