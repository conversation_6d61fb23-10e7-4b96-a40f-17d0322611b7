package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 子组详情
 */
@Data
@ApiModel
public class SubgroupInfoDTO {

    /**
     * 报警详情
     */
    @ApiModelProperty("报警详情")
    private String alarmDetail;

    /**
     * 子组样品
     */
    @ApiModelProperty("子组样品")
    private SubgroupDataDTO sample;


    /**
     * 子组失效标识(0--激活；1--失效)
     */
    @ApiModelProperty("子组失效标识(0--激活；1--失效)")
    private Integer flag;

    public SubgroupInfoDTO() {

    }

    public SubgroupInfoDTO(SubgroupDataDTO subgroupDataDTO) {
        this.flag = subgroupDataDTO.getF_FLAG();
        this.alarmDetail = alarmDetail;
    }

}
