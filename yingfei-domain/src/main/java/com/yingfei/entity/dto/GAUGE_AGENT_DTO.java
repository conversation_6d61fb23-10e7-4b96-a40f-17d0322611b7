package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Agent和硬件标识对照表
* @TableName GAUGE_AGENT
*/
@Data
public class GAUGE_AGENT_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_GAAG;
    /**
    * Agent名称
    */
    @ApiModelProperty("Agent名称")
    private String F_NAME;
    /**
    * 硬件标识
    */
    @ApiModelProperty("硬件标识")
    private String F_HARDWARE;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

}
