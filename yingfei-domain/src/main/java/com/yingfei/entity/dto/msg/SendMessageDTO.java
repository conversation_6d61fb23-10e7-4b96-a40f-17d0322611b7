package com.yingfei.entity.dto.msg;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SendMessageDTO {
    /**
     * 消息类型  1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知
     */
    private List<Integer> msgType;
    /**
     * 用户id列表
     */
    private List<Long> userIds;
    /**
     * 角色id列表
     */
    private List<Long> roleIds;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;

    public SendMessageDTO(List<Integer> msgType, List<Long> userIds, List<Long> roleIds, String title, String content) {
        this.msgType = msgType;
        this.userIds = userIds;
        this.roleIds = roleIds;
        this.title = title;
        this.content = content;
    }
}
