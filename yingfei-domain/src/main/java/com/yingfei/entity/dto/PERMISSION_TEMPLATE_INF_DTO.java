package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 权限模板表
*/
@Data
public class PERMISSION_TEMPLATE_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_ID;
    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
    private String F_NAME;
    /**
    * 模板描述
    */
    @ApiModelProperty("模板描述")
    private String F_DESC;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    @ApiModelProperty("菜单id列表")
    private List<Long> menuIds;

    private List<ROLE_INF_DTO> roleInfDtoList;
}
