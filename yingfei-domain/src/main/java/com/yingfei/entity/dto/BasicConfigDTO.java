package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基础配置信息
 */
@Data
@ApiModel
public class BasicConfigDTO {

    /**
     * 产品版本
     */
    @ApiModelProperty("产品版本")
    private String partRev;

    /**
     * 报警限
     */
    @ApiModelProperty("报警限")
    private String warningLimit;

    /**
     * 合理限
     */
    @ApiModelProperty("合理限")
    private String reasonableLimit;

    /**
     * 子组最大缓存天数
     */
    @ApiModelProperty("子组最大缓存天数")
    private String subGroupCacheMaxDay;

    /**
     * 消息日志最大保存天数
     */
    @ApiModelProperty("消息日志最大保存天数")
    private String msgLogSaveDay;

    /**
     * 数据监控缓存保存天数
     */
    @ApiModelProperty("数据监控缓存保存天数")
    private String monitorSaveDay;

    /**
     * 数据监控缓存保存条数
     */
    @ApiModelProperty("数据监控缓存保存条数")
    private String monitorSaveCount;

    /**
     * 单项分析,聚合分析是否开启参数集缓存
     */
    @ApiModelProperty("单项分析,聚合分析是否开启参数集缓存")
    private boolean openParameterSet;

    /**
     * 获取实时质量概览统计时间
     */
    @ApiModelProperty("获取实时质量概览统计时间")
    private String qualityOverviewTime;
}
