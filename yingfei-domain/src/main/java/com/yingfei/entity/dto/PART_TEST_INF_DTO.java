package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;

/**
* 产品测试关联图片
* @TableName PART_TEST_INF
*/
@Data
public class PART_TEST_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_PATI;
    /**
    * 产品ID
    */
    @ApiModelProperty("产品ID")
    private Long F_PART;
    /**
    * 测试ID
    */
    @ApiModelProperty("测试ID")
    private Long F_TEST;
    /**
    * 产品测试组合的图片
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("产品测试组合的图片")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_IMAGE;

    /**
     * 1:绑定显示图片 (默认1)
     */
    private Integer F_TYPE;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String F_TEXT;

    /**
     * 产品版本ID
     */
    @ApiModelProperty("产品版本ID")
    private Long F_PTRV;

    /**
     * 对应检验计划ID
     */
    @ApiModelProperty("对应检验计划ID")
    private Long F_PLAN;

    /**
     * 对应检验计划绑定内容
     */
    @ApiModelProperty("对应检验计划绑定内容")
    private String F_DATA;


    /**
     * 产品名称
     */
    private String partName;

    /**
     * 测试名称
     */
    private String testName;

    /**
     * 工厂名称
     */
    private String F_PLNT_NAME;

    /**
     * 检验计划名称
     */
    private String planName;


}
