package com.yingfei.entity.dto;


import com.yingfei.entity.util.HistogramUtil;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 直方图数据摘要DTO
 */
@Data
@ApiModel
public class DataSummaryDTO {

    /*-------------------------------数据摘要--------------------------*/
    /**
     * 测试数量
     */
    @ApiModelProperty("测试数量:数据集中测试值的总数")
    private Integer testNum;

    /**
     * 子测试数量
     */
    @ApiModelProperty("子测试数量:数据集中F_SBNO的最大值")
    private Integer subTestNum;

    /**
     * 子组数量
     */
    @ApiModelProperty("子组数量:数据集中子组总数（每个子组包含1个或多个测试值）")
    private Integer subGroupNum;

    /**
     * 子组大小(样本量)
     */
    @ApiModelProperty("子组大小:测试数量/子组数量")
    private Double subGroupSize;

    /**
     * 最大值
     */
    @ApiModelProperty("最大值:数据集中的最大值")
    private Double max;

    /**
     * 最小值
     */
    @ApiModelProperty("最小值:数据集中的最小值")
    private Double min;

    /**
     * 均值
     */
    @ApiModelProperty("均值:数据集中的平均值")
    private Double mean;

    /**
     * 短期标准差
     */
    @ApiModelProperty("短期标准差:1.S/c4  2.R/d2  " +
            "1. 样本量>9时，使用公式1" +
            "2. 样本量<9时，使用公式2" +
            "注：c4和d2为纠偏系数，跟样本量有关。如果样本量为整数，可通过查表获得；如果样本量为小数，则通过插值法计算出来")
    private Double shortTermStandardDeviation;

    /**
     * 长期标准差
     */
    @ApiModelProperty("长期标准差: 查看公式表")
    private Double longTermStandardDeviation;

    /**
     * 稳健系数(Robustness)
     */
    @ApiModelProperty("稳健系数(Robustness):(短期标准差/长期标准差)*100  如果长期标准差为0，则稳健系数的值为Null")
    private Double robustness;

    /**
     * 变异系数(CoVar)
     */
    @ApiModelProperty("变异系数(CoVar):(长期标准差/均值)*100  如果均值为0，则CoVar的值为Null")
    private Double coVar;

    /**
     * Ca
     */
    @ApiModelProperty("Ca:0.5*(过程均值-目标值)/(USL-LSL) ")
    private Double ca;
    /*-------------------------------数据摘要--------------------------*/



    /*-------------------------------规格--------------------------*/
    /**
     * USL
     */
    @ApiModelProperty("USL:公差上限")
    private Double usl;

    /**
     * 目标值
     */
    @ApiModelProperty("目标值")
    private Double targetValue;

    /**
     * LSL
     */
    @ApiModelProperty("LSL:公差下限")
    private Double lsl;

    /**
     * Z USL
     */
    @ApiModelProperty("Z USL:(公差上限-均值)/短期标准差  如果短期标准差的值为0，则Z USL的值为Null")
    private Double zUsl;

    /**
     * Z 目标值
     */
    @ApiModelProperty("Z 目标值:(目标值-均值)/短期标准差  如果短期标准差的值为0，则Z 目标值的值为Null")
    private Double zTargetValue;

    /**
     * Z LSL
     */
    @ApiModelProperty("Z LSL:(均值-公差下限)/短期标准差  如果短期标准差的值为0，则Z LSL的值为Null")
    private Double zLsl;
    /*-------------------------------规格--------------------------*/


    /*-------------------------------超公差限--------------------------*/
    /**
     * 实际值>USL
     */
    @ApiModelProperty("实际值>USL :数据集中数值超公差上限的百分比")
    private Double uslPercent;

    /**
     * 实际值<LSL
     */
    @ApiModelProperty("实际值<LSL :数据集中数值超公差下限的百分比")
    private Double lslPercent;

    /**
     * 实际值总数
     */
    @ApiModelProperty("实际值总数:实际值>USL+实际值<LSL")
    private Double actualValueTotal;

    /**
     * 实际PPM
     */
    @ApiModelProperty("实际PPM:实际值总数*10000")
    private Double actualPpm;

    /**
     * 预期值>USL
     */
    @ApiModelProperty("预期值>USL:(1-NORML.DIST(USL,均值,长期标准差))*100")
    private Double expectedUsl;

    /**
     * 预期值<LSL
     */
    @ApiModelProperty("预期值<LSL:(NORML.DIST(LSL,均值,长期标准差))*100")
    private Double expectedLsl;

    /**
     * 预期值总数
     */
    @ApiModelProperty("预期值总数:预期值>USL+预期值<LSL")
    private Double expectedValueTotal;

    /**
     * 逾期PPM
     */
    @ApiModelProperty("逾期PPM:预期值总数*10000")
    private Double expectedPpm;
    /*-------------------------------超公差限--------------------------*/


    /*-------------------------------过程潜力指数--------------------------*/
    /**
     * Cp
     */
    @ApiModelProperty("Cp:" +
            "1.(USL-LSL)/(6*短期标准差)" +
            "2.(USL-均值)/(3*短期标准差)" +
            "3.(均值-LSL)/(3*短期标准差)" +
            "(1.双边公差使用公式一" +
            "2.单边上限，即只有上公差的时候，使用公式二" +
            "3.单边下限，即只有下公差的时候，使用公式三" +
            "注：短期标准差为0时，Cp值为Null)")
    private Double cp;

    /**
     * Pp
     */
    @ApiModelProperty("Pp:" +
            "1. (USL-LSL)/(6*短期标准差)" +
            "2. (USL-均值)/(3*短期标准差)" +
            "3. (均值-LSL)/(3*短期标准差)" +
            "(1.双边公差使用公式一" +
            "2.单边上限，即只有上公差的时候，使用公式二" +
            "3.单边下限，即只有下公差的时候，使用公式三" +
            "注：长期标准差为0时，Pp值为Null)")
    private Double pp;

    /**
     * Cr
     */
    @ApiModelProperty("Cr:1/Cp Cp值为0或者Null时，Cr的值为Null")
    private Double cr;

    /**
     * Pr
     */
    @ApiModelProperty("Pr:1/Pp Pp值为0或者Null时，Pr的值为Null")
    private Double pr;
    /*-------------------------------过程潜力指数--------------------------*/


    /*-------------------------------过程能力指数--------------------------*/
    /**
     * Cpk
     */
    @ApiModelProperty("Cpk:" +
            "1. {(USL-均值)/(3*短期标准差), (均值-LSL)/(3*短期标准差)}中最小值" +
            "2. (USL-均值)/(3*短期标准差)" +
            "3. (均值-LSL)/(3*短期标准差)" +
            "(1. 双边公差使用公式一" +
            "2. 单边上限，即只有上公差的时候，使用公式二" +
            "3. 单边下限，即只有下公差的时候，使用公式三)")
    private Double cpk;

    /**
     * Cpu
     */
    @ApiModelProperty("Cpu:(USL-均值)/(3*短期标准差) 如果没有上公差，则Cpu的值为Null")
    private Double cpu;

    /**
     * Cpl
     */
    @ApiModelProperty("Cpl:(均值-LSL)/(3*短期标准差) 如果没有下公差，则Cpl的值为Null")
    private Double cpl;

    /**
     * Cpm
     */
    @ApiModelProperty("Cpm:(USL-LSL)/Sqrt(Pow(短期标准差)+Pow(均值-目标值)) " +
            "两种情况下Cpm的值为Null" +
            "1. 分母为0" +
            "2. 没有USL，LSL和目标值这三个参数中的任何一种")
    private Double cpm;

    /**
     * SIGL(短期)
     */
    @ApiModelProperty("SIGL(短期):3*Cpk")
    private Double siglShortTerm;

    /**
     * Ppk
     */
    @ApiModelProperty("Ppk:" +
            "1. {(USL-均值)/(3*长期标准差), (均值-LSL)/(3*长期标准差)}中最小值" +
            "2. (USL-均值)/(3*长期标准差)" +
            "3. (均值-LSL)/(3*长期标准差)" +
            "(1. 双边公差使用公式一" +
            "2. 单边上限，即只有上公差的时候，使用公式二" +
            "3. 单边下限，即只有下公差的时候，使用公式三)")
    private Double ppk;

    /**
     * Ppu
     */
    @ApiModelProperty("Ppu:(USL-均值)/(3*长期标准差) 如果没有上公差，则Ppu的值为Null")
    private Double ppu;

    /**
     * Ppl
     */
    @ApiModelProperty("Ppl:(均值-LSL)/(3*长期标准差) 如果没有下公差，则Ppl的值为Null")
    private Double ppl;

    /**
     * Ppm
     */
    @ApiModelProperty("Ppm:(USL-LSL)/Sqrt(Pow(长期标准差)+Pow(均值-目标值)) " +
            "两种情况下Ppm的值为Null" +
            "1. 分母为0" +
            "2. 没有USL，LSL和目标值这三个参数中的任何一种")
    private Double ppm;

    /**
     * SIGL(长期)
     */
    @ApiModelProperty("SIGL(长期):3*Ppk")
    private Double siglLongTerm;

    @ApiModelProperty("CP目标值(SPEC_LIM--F_CP)")
    private Double F_CPTAR;

    @ApiModelProperty("CPK目标值(SPEC_LIM--F_CPK)")
    private Double F_CPKTAR;

    @ApiModelProperty("CP目标值(SPEC_LIM--F_PP)")
    private Double F_PPTAR;

    @ApiModelProperty("CPK目标值(SPEC_LIM--F_PPK)")
    private Double F_PPKTAR;

    /**
     * 合理上限
     */
    @ApiModelProperty("合理上限")
    private Double url;
    /**
     * 合理下限
     */
    @ApiModelProperty("合理下限")
    private Double lrl;
    /**
     * 报警上限
     */
    @NotNull(message = "[报警上限]不能为空")
    @ApiModelProperty("报警上限")
    private Double uwl;
    /**
     * 报警下限
     */
    @ApiModelProperty("报警下限")
    private Double lwl;
    /**
     * 件内上限
     */
    @ApiModelProperty("件内上限")
    private Double uwp;
    /**
     * 件内下限
     */
    @ApiModelProperty("件内下限")
    private Double lwp;
    /**
     * 子组均值上限
     */
    @ApiModelProperty("子组均值上限")
    private Double ual;
    /**
     * 子组均值下限
     */
    @ApiModelProperty("子组均值下限")
    private Double lal;

    /*测试列表*/
    private List<Double> valList;

    /**
     * 获取基本数据
     *
     * @param subgroupList    子组数据
     * @param type            取值类型(1:取实际值(或者子测试均值) 2:取子测试的值  没有子测试的话不做处理 3:取测试的实际值和所有子测试的值组装)
     * @param shortSdTermType 短期西格玛计算类型(0:自动检测，1:西格玛法，2:极差法，3:合并标准差(西格玛法平方+极差法平方的和再开方))
     * @return
     */
    public static DataSummaryDTO getBasic(List<SubgroupDataDTO> subgroupList, Integer type, Integer shortSdTermType) {
        DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
        /*获取测试列表*/
        List<SGRP_VAL_DTO> sgrpValDtoList =
                subgroupList.stream().map(SubgroupDataDTO::getSgrpValDto).collect(Collectors.toList());
        List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = sgrpValDtoList.stream().map(SGRP_VAL_DTO::getSgrpValChildDto).collect(Collectors.toList());
        return getDataSummaryDTO(type, shortSdTermType, dataSummaryDTO, sgrpValDtoList, sgrpValChildDtoList);
    }

    /**
     * 数据监控计算cp和cpk
     * @param subgroupDataVOList
     * @param specInfDto
     * @return
     */
    public static DataSummaryDTO monitorGetCPAndCpk(List<SubgroupDataVO> subgroupDataVOList, SPEC_INF_DTO specInfDto) {
        List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList = subgroupDataVOList.stream().map(SubgroupDataVO::getSgrpValChildDto).collect(Collectors.toList());
        List<SGRP_VAL_DTO> list = new ArrayList<>();
        sgrpValChildDtoList.forEach(sgrpValChildDto -> {
            SGRP_VAL_DTO sgrpValDto = new SGRP_VAL_DTO();
            sgrpValDto.setSgrpValChildDto(sgrpValChildDto);
            list.add(sgrpValDto);
        });
        List<Double> doubles = sgrpValChildDtoList.stream()
                .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
        DataSummaryDTO dataSummaryDTO = new DataSummaryDTO();
        /*获取测试值数量*/
        dataSummaryDTO.setTestNum(doubles.size());
        /*获取数据集中的平均值*/
        dataSummaryDTO.setMean(doubles.stream().mapToDouble(Double::doubleValue).average().orElse(0d));
        /*获取子组数量*/
        dataSummaryDTO.setSubGroupNum(list.size());

        /*获取子组大小(样本量)*/
        dataSummaryDTO.setSubGroupSize(
                HistogramUtil.getSubgroupSize(dataSummaryDTO.getTestNum(), dataSummaryDTO.getSubGroupNum()));
        /*获取短期标准差 todo 修改阈值  需要可配置 */
        dataSummaryDTO.setShortTermStandardDeviation(
                HistogramUtil.getShortTermStandardDeviation(dataSummaryDTO.getSubGroupSize(), list, 0));

        /*获取长期标准差*/
        dataSummaryDTO.setLongTermStandardDeviation(
                HistogramUtil.getLongTermStandardDeviation(sgrpValChildDtoList.stream()
                        .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                        .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList())));

        /*获取Cp*/
        dataSummaryDTO.setCp(HistogramUtil.getCp(specInfDto.getF_USL(), specInfDto.getF_LSL(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取Cpk*/
        dataSummaryDTO.setCpk(HistogramUtil.getCpk(specInfDto.getF_USL(), specInfDto.getF_LSL(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));
        return dataSummaryDTO;
    }


    private static DataSummaryDTO getDataSummaryDTO(Integer type, Integer shortSdTermType, DataSummaryDTO dataSummaryDTO, List<SGRP_VAL_DTO> sgrpValDtoList, List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList) {
        List<Double> doubles = new ArrayList<>();
        /*取实际值(或者子测试均值)*/
        if (type == 1) {
            doubles = sgrpValChildDtoList.stream()
                    .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
        } else if (type == 2) {
            /*取子测试的值  没有子测试的话不做处理*/
            doubles = sgrpValChildDtoList.stream()
                    .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.Test::getSubTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.SubTest::getSubTestValue)
                    .collect(Collectors.toList());
        } else if (type == 3) {
            /*取测试的实际值和所有子测试的值组装*/
            doubles = sgrpValChildDtoList.stream()
                    .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList());
            List<Double> childValList = sgrpValChildDtoList.stream()
                    .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.Test::getSubTestList).flatMap(List::stream)
                    .map(SGRP_VAL_CHILD_DTO.SubTest::getSubTestValue)
                    .collect(Collectors.toList());
            doubles.addAll(childValList);
        }
        dataSummaryDTO.setValList(doubles);
        /*获取测试值数量*/
        dataSummaryDTO.setTestNum(doubles.size());

        /*获取子测试数量 数据集中F_SBNO的最大值"*/
        Integer subTestNum = sgrpValDtoList.stream().map(SGRP_VAL_DTO::getF_SBSZ)
                .max(Comparator.comparing(Integer::intValue)).orElse(0);
        dataSummaryDTO.setSubTestNum(subTestNum == 0 ? 1 : subTestNum);

        /*获取子组数量*/
        dataSummaryDTO.setSubGroupNum(sgrpValDtoList.size());

        /*获取子组大小(样本量)*/
        dataSummaryDTO.setSubGroupSize(
                HistogramUtil.getSubgroupSize(dataSummaryDTO.getTestNum(), dataSummaryDTO.getSubGroupNum()));

        /*获取数据集中的最大值*/
        dataSummaryDTO.setMax(sgrpValChildDtoList.stream().map(SGRP_VAL_CHILD_DTO::getMax).filter(Objects::nonNull).max(Double::compareTo).orElse(0d));

        /*获取数据集中的最小值*/
        dataSummaryDTO.setMin(sgrpValChildDtoList.stream().map(SGRP_VAL_CHILD_DTO::getMax).filter(Objects::nonNull).min(Double::compareTo).orElse(0d));

        /*获取数据集中的平均值*/
        dataSummaryDTO.setMean(doubles.stream().mapToDouble(Double::doubleValue).average().orElse(0d));

        /*获取短期标准差 todo 修改阈值  需要可配置 */
        dataSummaryDTO.setShortTermStandardDeviation(
                HistogramUtil.getShortTermStandardDeviation(dataSummaryDTO.getSubGroupSize(), sgrpValDtoList, shortSdTermType));

        /*获取长期标准差*/
        dataSummaryDTO.setLongTermStandardDeviation(
                HistogramUtil.getLongTermStandardDeviation(sgrpValChildDtoList.stream()
                        .map(SGRP_VAL_CHILD_DTO::getTestList).flatMap(List::stream)
                        .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).collect(Collectors.toList())));

        /*获取稳健系数*/
        dataSummaryDTO.setRobustness(
                HistogramUtil.getRobustness(dataSummaryDTO.getShortTermStandardDeviation(),
                        dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取变异系数*/
        dataSummaryDTO.setCoVar(
                HistogramUtil.getCoVar(dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));
        return dataSummaryDTO;
    }

    /**
     * 缺陷或不良重新计算均值
     */
    public static void recalculateMean(DataSummaryDTO dataSummaryDTO, List<SubgroupDataDTO> subgroupList) {
        /*所有测试值之和除以所有子组样本量之和*/
        BigDecimal sumBD = subgroupList.stream()
                .map(SubgroupDataDTO::getSgrpValDtoList)
                .flatMap(List::stream)
                .map(dto -> new BigDecimal(dto.getF_SGSZ().toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal total = dataSummaryDTO.getValList().stream()
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal meanValue = total.divide(sumBD, 12, RoundingMode.HALF_UP)
                .setScale(8, RoundingMode.HALF_UP);
        dataSummaryDTO.setMean(meanValue.doubleValue());
    }

    /**
     * 获取目标和超公差限数据
     */
    public static void getBasicTwo(DataSummaryDTO dataSummaryDTO) {

        /*获取ca*/
        dataSummaryDTO.setCa(HistogramUtil.getCa(dataSummaryDTO.getMean(), dataSummaryDTO.getTargetValue(),
                dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl()));

        /*获取Z USL*/
        dataSummaryDTO.setZUsl(HistogramUtil.getZUsl(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Z LSL*/
        dataSummaryDTO.setZLsl(HistogramUtil.getZLsl(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Z 目标值*/
        dataSummaryDTO.setZTargetValue(HistogramUtil.getZTargetValue(dataSummaryDTO.getTargetValue(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取实际值>USL*/
        dataSummaryDTO.setUslPercent(HistogramUtil.getUslPercent(dataSummaryDTO.getValList(), dataSummaryDTO.getUsl()));

        /*获取实际值<LSL*/
        dataSummaryDTO.setLslPercent(HistogramUtil.getLslPercent(dataSummaryDTO.getValList(), dataSummaryDTO.getLsl()));

        /*获取实际值总数*/
        dataSummaryDTO.setActualValueTotal(HistogramUtil.
                getActualValueTotal(dataSummaryDTO.getUslPercent(), dataSummaryDTO.getLslPercent()));

        /*获取实际PPM*/
        dataSummaryDTO.setActualPpm(HistogramUtil.getActualPPM(dataSummaryDTO.getActualValueTotal()));

        /*获取预期值>USL*/
        dataSummaryDTO.setExpectedUsl(HistogramUtil.getExpectUsl(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取预期值<LSL*/
        dataSummaryDTO.setExpectedLsl(HistogramUtil.getExpectLsl(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取预期值总数*/
        dataSummaryDTO.setExpectedValueTotal(HistogramUtil.getExpectValueTotal(dataSummaryDTO.getExpectedUsl(),
                dataSummaryDTO.getExpectedLsl()));

        /*获取预期PPM*/
        dataSummaryDTO.setExpectedPpm(HistogramUtil.getExpectPPM(dataSummaryDTO.getExpectedValueTotal()));
    }

    /**
     * 获取过程潜力指数
     */
    public static void getBasicThree(DataSummaryDTO dataSummaryDTO) {
        /*获取Cp*/
        dataSummaryDTO.setCp(HistogramUtil.getCp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取Pp*/
        dataSummaryDTO.setPp(HistogramUtil.getPp(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Cr*/
        dataSummaryDTO.setCr(HistogramUtil.getCr(dataSummaryDTO.getCp()));

        /*获取Pr*/
        dataSummaryDTO.setPr(HistogramUtil.getPr(dataSummaryDTO.getPp()));
    }

    public static void getBasicFour(DataSummaryDTO dataSummaryDTO) {
        /*获取Cpk*/
        dataSummaryDTO.setCpk(HistogramUtil.getCpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取Cpu*/
        dataSummaryDTO.setCpu(HistogramUtil.getCpu(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取Cpl*/
        dataSummaryDTO.setCpl(HistogramUtil.getCpl(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取Cpm*/
        dataSummaryDTO.setCpm(HistogramUtil.getCpm(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getTargetValue(),
                dataSummaryDTO.getShortTermStandardDeviation()));

        /*获取SIGL(短期)*/
        dataSummaryDTO.setSiglShortTerm(HistogramUtil.getSiglShortTerm(dataSummaryDTO.getCpk()));

        /*获取Ppk*/
        dataSummaryDTO.setPpk(HistogramUtil.getPpk(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Ppu*/
        dataSummaryDTO.setPpu(HistogramUtil.getPpu(dataSummaryDTO.getUsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Ppl*/
        dataSummaryDTO.setPpl(HistogramUtil.getPpl(dataSummaryDTO.getLsl(), dataSummaryDTO.getMean(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取Ppm*/
        dataSummaryDTO.setPpm(HistogramUtil.getPpm(dataSummaryDTO.getUsl(), dataSummaryDTO.getLsl(),
                dataSummaryDTO.getMean(), dataSummaryDTO.getTargetValue(),
                dataSummaryDTO.getLongTermStandardDeviation()));

        /*获取SIGL(长期)*/
        dataSummaryDTO.setSiglLongTerm(HistogramUtil.getSiglLongTerm(dataSummaryDTO.getPpk()));
    }

    /**
     * 公差限赋值
     * @param dataSummaryDTO
     * @param specInfDto
     */
    public static void setSpec(DataSummaryDTO dataSummaryDTO, SPEC_INF_DTO specInfDto) {
        if (ObjectUtils.isNotEmpty(specInfDto)) {
            dataSummaryDTO.setUsl(specInfDto.getF_USL());
            dataSummaryDTO.setLsl(specInfDto.getF_LSL());
            dataSummaryDTO.setTargetValue(specInfDto.getF_TAR());
            dataSummaryDTO.setF_CPTAR(specInfDto.getF_CP());
            dataSummaryDTO.setF_CPKTAR(specInfDto.getF_CPK());
            dataSummaryDTO.setF_PPTAR(specInfDto.getF_PP());
            dataSummaryDTO.setF_PPKTAR(specInfDto.getF_PPK());

            dataSummaryDTO.setUrl(specInfDto.getF_URL());
            dataSummaryDTO.setLrl(specInfDto.getF_LRL());
            dataSummaryDTO.setUwl(specInfDto.getF_UWL());
            dataSummaryDTO.setLwl(specInfDto.getF_LWL());
            dataSummaryDTO.setUwp(specInfDto.getF_UWP());
            dataSummaryDTO.setLwp(specInfDto.getF_LWP());
            dataSummaryDTO.setUal(specInfDto.getF_UAL());
            dataSummaryDTO.setLal(specInfDto.getF_LAL());
        }
    }
}
