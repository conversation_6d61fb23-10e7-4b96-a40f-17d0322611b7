package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 能力象限图返回DTO
 */
@Data
public class QuadrantalDiagramDTO {
    @ApiModelProperty("子组对应的产品id")
    private Long F_PART;

    @ApiModelProperty("子组对应的产品名称")
    private String F_PARTName;

    @ApiModelProperty("子组对应的产品版本id")
    private Long F_PTRV;

    @ApiModelProperty("子组对应的产品版本名称")
    private String F_PTRVName;

    @ApiModelProperty("子组对应的过程id")
    private Long F_PRCS;

    @ApiModelProperty("子组对应的过程名称")
    private String F_PRCSName;

    @ApiModelProperty("子组对应的测试id")
    private Long F_TEST;

    @ApiModelProperty("测试名称")
    private String F_TESTName;

    @ApiModelProperty("算出来的cp/目标cp")
    private Double cp;

    @ApiModelProperty("算出来的cpk/目标cpk")
    private Double cpk;

    @ApiModelProperty("基本概要")
    private DataSummaryDTO dataSummaryDTO;
}
