package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 分析页面模板表
* @TableName ANALYSIS_DASHBOARD_TEMPLATE_INF
*/
@Data
public class ANALYSIS_DASHBOARD_TEMPLATE_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_ADTI;
    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
    private String F_NAME;
    /**
    * 模板类型(0:聚合分析,1:)
    */
    @ApiModelProperty("模板类型(0:聚合分析,1:)")
    private Integer F_TYPE;
    /**
    * 模板所选的图表及图表的配置json
    */
    @ApiModelProperty("模板所选的图表及图表的配置json")
    private String F_DATA;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    @ApiModelProperty("目标配置json")
    private String F_CONFIG;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String F_DESC;

    @ApiModelProperty("菜单数量")
    private Integer menuSize = 0;

    @ApiModelProperty("关联菜单信息")
    private List<MENU_INF_DTO> menuInfDtoList;
}
