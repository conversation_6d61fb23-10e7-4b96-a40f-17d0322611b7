package com.yingfei.entity.dto;

import com.alibaba.fastjson2.JSONObject;
import com.yingfei.common.core.annotation.Excel;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.TEST_INF;
import com.yingfei.entity.enums.SpecificationLimitViolation;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import com.yingfei.entity.vo.ControlLimitVO;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.vo.SPEC_INF_VO;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公差限表
 *
 * @TableName SPEC_INF
 */
@Data
public class SPEC_INF_DTO extends BaseEntity {

    /**
     * 记录主键
     */
    @ApiModelProperty("记录主键")
    private Long F_SPEC;
    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    private Long F_PART;
    /**
     * 测试ID
     */
    @ApiModelProperty("测试ID")
    private Long F_TEST;
    /**
     * 过程ID
     */
    @ApiModelProperty("过程ID")
    private Long F_PRCS;
    /**
     * 工作ID
     */
    @ApiModelProperty("工作ID")
    private Long F_JOB;

    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称")
    private String partName;

    @ApiModelProperty("过程名称")
    @Excel(name = "过程名称")
    private String prcsName;

    @ApiModelProperty("测试名称")
    @Excel(name = "测试名称")
    private String testName;

    @ApiModelProperty("产品版本名称")
    @Excel(name = "产品版本名称")
    private String ptrvName;

    @ApiModelProperty("工作名称")
    @Excel(name = "工作名称")
    private String jobName;

    /**
     * 公差上限
     */
    @ApiModelProperty("公差上限")
    @Excel(name = "公差上限")
    private Double F_USL;
    /**
     * 目标值
     */
    @ApiModelProperty("目标值")
    @Excel(name = "目标值")
    private Double F_TAR;
    /**
     * 公差下限
     */
    @ApiModelProperty("公差下限")
    @Excel(name = "公差下限")
    private Double F_LSL;
    /**
     * 合理上限
     */
    @ApiModelProperty("合理上限")
    @Excel(name = "合理上限")
    private Double F_URL;
    /**
     * 合理下限
     */
    @ApiModelProperty("合理下限")
    @Excel(name = "合理下限")
    private Double F_LRL;
    /**
     * 报警上限
     */
    @NotNull(message = "[报警上限]不能为空")
    @ApiModelProperty("报警上限")
    @Excel(name = "报警上限")
    private Double F_UWL;
    /**
     * 报警下限
     */
    @ApiModelProperty("报警下限")
    @Excel(name = "报警下限")
    private Double F_LWL;
    /**
     * 件内上限
     */
    @ApiModelProperty("件内上限")
    @Excel(name = "件内上限")
    private Double F_UWP;
    /**
     * 件内下限
     */
    @ApiModelProperty("件内下限")
    @Excel(name = "件内下限")
    private Double F_LWP;
    /**
     * 子组均值上限
     */
    @ApiModelProperty("子组均值上限")
    @Excel(name = "子组均值上限")
    private Double F_UAL;
    /**
     * 子组均值下限
     */
    @ApiModelProperty("子组均值下限")
    @Excel(name = "子组均值下限")
    private Double F_LAL;
    /**
     * 目标Cp
     */
    @ApiModelProperty("目标Cp")
    @Excel(name = "目标Cp")
    private Double F_CP;
    /**
     * 目标Cpk
     */
    @ApiModelProperty("目标Cpk")
    @Excel(name = "目标Cpk")
    private Double F_CPK;
    /**
     * 目标Pp
     */
    @ApiModelProperty("目标Pp")
    @Excel(name = "目标Pp")
    private Double F_PP;
    /**
     * 目标Ppk
     */
    @ApiModelProperty("目标Ppk")
    @Excel(name = "目标Ppk")
    private Double F_PPK;
    /**
     * 各界限是否报警标识
     */
    @ApiModelProperty("各界限是否报警标识")
    private Integer F_AFLAG;
    /**
     * 各界限是否激活标识
     */
    @ApiModelProperty("各界限是否激活标识")
    private Integer F_EFLAG;
    /**
     * 因子，默认为1
     */
    @ApiModelProperty("因子，默认为1")
    @Excel(name = "因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
     * 记录最后编辑用户ID
     */
    @ApiModelProperty("记录最后编辑用户ID")
    private Long F_EDUE;
    /**
     * 产品版本ID
     */
    @ApiModelProperty("产品版本ID")
    private Long F_PTRV;

    DataSummaryDTO dataSummaryDTO;

    /**
     * 报警激活标识
     */
    @ApiModelProperty("报警激活标识")
    private SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto;


    public static SPEC_INF_DTO getSpecInfDto(SPEC_INF_VO specInfVo, List<SPEC_INF_DTO> specInfDtos) {
        if (CollectionUtils.isEmpty(specInfDtos)) return null;
        SPEC_INF_DTO specInfDto = null;
        if (specInfDtos.size() == 1) {
            if (ObjectUtils.isNotEmpty(specInfDtos.get(0)) && ObjectUtils.isNotEmpty(specInfVo.getF_PRCS())) {
                   if (ObjectUtils.isNotEmpty(specInfDtos.get(0).getF_PRCS()) && specInfDtos.get(0).getF_PRCS().equals(specInfVo.getF_PRCS())) {
                       specInfDto = specInfDtos.get(0);
                   } else if (ObjectUtils.isEmpty(specInfDtos.get(0).getF_PRCS()) || specInfDtos.get(0).getF_PRCS().equals(0L)) {
                       specInfDto = specInfDtos.get(0);
                   }
            }
        } else {
            List<SPEC_INF_DTO> list = new ArrayList<>();
            /*过程*/
            list = specInfDtos.stream().filter(specLim1 ->
                    (specLim1.getF_PRCS() != null && specLim1.getF_PRCS().equals(specInfVo.getF_PRCS()))
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                specInfDto = list.get(0);
            }
            if (CollectionUtils.isEmpty(list)) {
                list = specInfDtos.stream().filter(specLim1 ->
                        ( ObjectUtils.isEmpty(specLim1.getF_PRCS()) || specLim1.getF_PRCS().equals(0L))
                ).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    specInfDto = list.get(0);
                }
            }
        }
        if (specInfDto == null) {
            return null;
        }
        checkSpec(specInfDto);
        specInfDto.setSpecActivationAlarmDto(checkAlarm(specInfDto));
        return specInfDto;
    }

    /**
     * 判断公差限值是否存在
     *
     * @param specInf
     */
    public static void checkSpec(SPEC_INF_DTO specInf) {
        String binary = Integer.toBinaryString(specInf.getF_EFLAG());
        /*将binary 补齐24位*/
        binary = String.format("%24s", binary).replace(" ", "0");
        /*合理上限,上限判断第一个1位置是否为1*/
        specInf.setF_URL(binary.charAt(23) == '1' ? specInf.getF_URL() : null);
        /*公差上线,上限判断第二个1位置是否为1*/
        specInf.setF_USL(binary.charAt(22) == '1' ? specInf.getF_USL() : null);
        /*报警上限,上限判断第三个1位置是否为1*/
        specInf.setF_UWL(binary.charAt(21) == '1' ? specInf.getF_UWL() : null);
        /*目标值,上限判断第四个1位置是否为1*/
        specInf.setF_TAR(binary.charAt(20) == '1' ? specInf.getF_TAR() : null);
        /*报警下限,上限判断第五个1位置是否为1*/
        specInf.setF_LWL(binary.charAt(19) == '1' ? specInf.getF_LWL() : null);
        /*公差下限,上限判断第六个1位置是否为1*/
        specInf.setF_LSL(binary.charAt(18) == '1' ? specInf.getF_LSL() : null);
        /*合理下限,上限判断第七个1位置是否为1*/
        specInf.setF_LRL(binary.charAt(17) == '1' ? specInf.getF_LRL() : null);
        /*件内上限,上限判断第八个1位置是否为1*/
        specInf.setF_UWP(binary.charAt(16) == '1' ? specInf.getF_UWP() : null);
        /*件内下限,上限判断第九个1位置是否为1*/
        specInf.setF_LWP(binary.charAt(15) == '1' ? specInf.getF_LWP() : null);
        /*子组均值上,上限判断第十个1位置是否为1*/
        specInf.setF_UAL(binary.charAt(14) == '1' ? specInf.getF_UAL() : null);
        /*子组均值下,上限判断第十一个1位置是否为1*/
        specInf.setF_LAL(binary.charAt(13) == '1' ? specInf.getF_LAL() : null);
    }

    /**
     * 判断公差限报警是否存在
     *
     * @param specInf
     */
    public static SPEC_ACTIVATION_ALARM_DTO checkAlarm(SPEC_INF_DTO specInf) {
        SPEC_ACTIVATION_ALARM_DTO specActivationAlarmDto = new SPEC_ACTIVATION_ALARM_DTO();
        String binary = Integer.toBinaryString(specInf.getF_AFLAG());
        /*将binary 补齐24位*/
        binary = String.format("%24s", binary).replace(" ", "0");
        /*合理上限,上限判断第一个1位置是否为1*/
        specActivationAlarmDto.setF_URL_ALARM(binary.charAt(23) == '1' ? 1 : 0);
        /*公差上线,上限判断第二个1位置是否为1*/
        specActivationAlarmDto.setF_USL_ALARM(binary.charAt(22) == '1' ? 1 : 0);
        /*报警上限,上限判断第三个1位置是否为1*/
        specActivationAlarmDto.setF_UWL_ALARM(binary.charAt(21) == '1' ? 1 : 0);
        /*报警下限,上限判断第四个1位置是否为1*/
        specActivationAlarmDto.setF_LWL_ALARM(binary.charAt(20) == '1' ? 1 : 0);
        /*公差下限,上限判断第五个1位置是否为1*/
        specActivationAlarmDto.setF_LSL_ALARM(binary.charAt(19) == '1' ? 1 : 0);
        /*合理下限,上限判断第六个1位置是否为1*/
        specActivationAlarmDto.setF_LRL_ALARM(binary.charAt(18) == '1' ? 1 : 0);
        /*件内上限,上限判断第七个1位置是否为1*/
        specActivationAlarmDto.setF_UWP_ALARM(binary.charAt(17) == '1' ? 1 : 0);
        /*件内下限,上限判断第八个1位置是否为1*/
        specActivationAlarmDto.setF_LWP_ALARM(binary.charAt(16) == '1' ? 1 : 0);
        /*子组均值上,上限判断第九个1位置是否为1*/
        specActivationAlarmDto.setF_UAL_ALARM(binary.charAt(15) == '1' ? 1 : 0);
        /*子组均值下,上限判断第十个1位置是否为1*/
        specActivationAlarmDto.setF_LAL_ALARM(binary.charAt(14) == '1' ? 1 : 0);
        return specActivationAlarmDto;
    }


    public static final String actualValue = "_actualValue";
    public static final String compareValue = "_compareValue";
    public static final String connector = "_connector";

    /**
     * 触发报警
     * 1. USL、LSL、UWL、LWL、URL、LRL：适用于类型是变量的测试。常规变量测试：判断单个值是否超过界限；子测试：判断平均值是否超过界限
     * 2. UWP和LWP：适用于类型是变量的子测试，判断单个子测试是否超过界限
     * 3. UAL和LAL：适用于缺陷、不良和变量，判断均值是否超过界限。均值=测试值之和/样本量
     *
     * @return
     */
    public static List<AlarmMessageDTO> triggerAlarm(SPEC_INF_DTO specInfDto, SubgroupDataVO subgroupDataVO, TEST_INF testInf) {
        List<AlarmMessageDTO> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        SPEC_ACTIVATION_ALARM_DTO alarmDto = specInfDto.getSpecActivationAlarmDto();
        List<SGRP_VAL_CHILD_DTO.Test> testList = subgroupDataVO.getSgrpValChildDto().getTestList();
        for (SGRP_VAL_CHILD_DTO.Test test : testList) {
            if (testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType())) {
                if (alarmDto.getF_USL_ALARM() == 1 && specInfDto.getF_USL() != null) {
                    if (test.getTestVal() > specInfDto.getF_USL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.USL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.USL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.USL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.USL.getDesc() + compareValue, specInfDto.getF_USL());
                            jsonObject.put(SpecificationLimitViolation.USL.getDesc() + connector, ">");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.USL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_LSL_ALARM() == 1 && specInfDto.getF_LSL() != null) {
                    if (test.getTestVal() < specInfDto.getF_LSL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.LSL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.LSL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.LSL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.LSL.getDesc() + compareValue, specInfDto.getF_LSL());
                            jsonObject.put(SpecificationLimitViolation.LSL.getDesc() + connector, "<");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.LSL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_UWL_ALARM() == 1 && specInfDto.getF_UWL() != null) {
                    if (test.getTestVal() > specInfDto.getF_UWL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.UWL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.UWL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.UWL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.UWL.getDesc() + compareValue, specInfDto.getF_UWL());
                            jsonObject.put(SpecificationLimitViolation.UWL.getDesc() + connector, ">");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.UWL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_LWL_ALARM() == 1 && specInfDto.getF_LWL() != null) {
                    if (test.getTestVal() < specInfDto.getF_LWL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.LWL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.LWL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.LWL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.LWL.getDesc() + compareValue, specInfDto.getF_LWL());
                            jsonObject.put(SpecificationLimitViolation.LWL.getDesc() + connector, "<");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.LWL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_URL_ALARM() == 1 && specInfDto.getF_URL() != null) {
                    if (test.getTestVal() > specInfDto.getF_URL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.URL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.URL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.URL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.URL.getDesc() + compareValue, specInfDto.getF_URL());
                            jsonObject.put(SpecificationLimitViolation.URL.getDesc() + connector, ">");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.URL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_LRL_ALARM() == 1 && specInfDto.getF_LRL() != null) {
                    if (test.getTestVal() < specInfDto.getF_LRL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.LRL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.LRL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.LRL.getDesc() + actualValue, test.getTestVal());
                            jsonObject.put(SpecificationLimitViolation.LRL.getDesc() + compareValue, specInfDto.getF_LRL());
                            jsonObject.put(SpecificationLimitViolation.LRL.getDesc() + connector, "<");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.LRL.getDesc(), (int) o + 1);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(test.getSubTestList())) {
                /*UWP和LWP：适用于类型是变量的子测试，判断单个子测试是否超过界限*/
                if (testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType())) {
                    test.getSubTestList().forEach(subTest -> {
                        if (alarmDto.getF_UWP_ALARM() == 1 && specInfDto.getF_UWP() != null) {
                            if (subTest.getSubTestValue() > specInfDto.getF_UWP()) {
                                Object o = jsonObject.get(SpecificationLimitViolation.UWP.getDesc());
                                if (o == null) {
                                    jsonObject.put(SpecificationLimitViolation.UWP.getDesc(), 1);
                                    jsonObject.put(SpecificationLimitViolation.UWP.getDesc() + actualValue, subTest.getSubTestValue());
                                    jsonObject.put(SpecificationLimitViolation.UWP.getDesc() + compareValue, specInfDto.getF_UWP());
                                    jsonObject.put(SpecificationLimitViolation.UWP.getDesc() + connector, ">");

                                } else {
                                    jsonObject.put(SpecificationLimitViolation.UWP.getDesc(), (int) o + 1);
                                }
                            }
                        }
                        if (alarmDto.getF_LWP_ALARM() == 1 && specInfDto.getF_LWP() != null) {
                            if (subTest.getSubTestValue() < specInfDto.getF_LWP()) {
                                Object o = jsonObject.get(SpecificationLimitViolation.LWP.getDesc());
                                if (o == null) {
                                    jsonObject.put(SpecificationLimitViolation.LWP.getDesc(), 1);
                                    jsonObject.put(SpecificationLimitViolation.LWP.getDesc() + actualValue, subTest.getSubTestValue());
                                    jsonObject.put(SpecificationLimitViolation.LWP.getDesc() + compareValue, specInfDto.getF_LWP());
                                    jsonObject.put(SpecificationLimitViolation.LWP.getDesc() + connector, "<");
                                } else {
                                    jsonObject.put(SpecificationLimitViolation.LWP.getDesc(), (int) o + 1);
                                }
                            }
                        }
                    });
                }
            }
        }

        if (testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.VARIABLE.getType()) ||
                testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.DEFECT.getType()) ||
                testInf.getF_TYPE().equals(TEST_INF_TYPEEnum.DEFECTIVE.getType())) {
            /*UAL和LAL：适用于缺陷、不良和变量，判断均值是否超过界限。均值=测试值之和/样本量*/
            if (alarmDto.getF_UAL_ALARM() == 1 || alarmDto.getF_LAL_ALARM() == 1) {
                Double sum = testList.stream().map(SGRP_VAL_CHILD_DTO.Test::getTestVal).reduce(Double::sum).orElse(0d);
                double avg = sum / subgroupDataVO.getF_SGSZ();
                if (alarmDto.getF_UAL_ALARM() == 1 && specInfDto.getF_UAL() != null) {
                    if (avg > specInfDto.getF_UAL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.UAL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + actualValue, avg);
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + compareValue, specInfDto.getF_UAL());
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + connector, ">");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc(), (int) o + 1);
                        }
                    }
                }
                if (alarmDto.getF_LAL_ALARM() == 1 && specInfDto.getF_LAL() != null) {
                    if (avg < specInfDto.getF_LAL()) {
                        Object o = jsonObject.get(SpecificationLimitViolation.LAL.getDesc());
                        if (o == null) {
                            jsonObject.put(SpecificationLimitViolation.LAL.getDesc(), 1);
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + actualValue, avg);
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + compareValue, specInfDto.getF_LAL());
                            jsonObject.put(SpecificationLimitViolation.UAL.getDesc() + connector, "<");
                        } else {
                            jsonObject.put(SpecificationLimitViolation.LAL.getDesc(), (int) o + 1);
                        }
                    }
                }
            }
        }

        /**
         * 重新组装json
         * name  SpecificationLimitViolation 描述
         * num   触发同一个报警数量
         * type  1:公差限 2:控制限
         */
        for (SpecificationLimitViolation value : SpecificationLimitViolation.values()) {
            if (jsonObject.get(value.getDesc()) != null) {
                AlarmMessageDTO data = new AlarmMessageDTO();
                Object num = jsonObject.get(value.getDesc());
                data.setName(value.getDesc());
                data.setId((long) value.getType());
                data.setNum(Integer.valueOf(num.toString()));
                data.setType(1);
                data.setActualValue(jsonObject.get(value.getDesc() + actualValue) == null ? null : Double.valueOf(jsonObject.get(value.getDesc() + actualValue).toString()));
                data.setCompareValue(jsonObject.get(value.getDesc() + compareValue) == null ? null : Double.valueOf(jsonObject.get(value.getDesc() + compareValue).toString()));
                data.setConnector(jsonObject.get(value.getDesc() + connector) == null ? null : jsonObject.get(value.getDesc() + connector).toString());
                list.add(data);
            }
        }
        return list;
    }


    /**
     * 数据点判断是否超公差
     */
    public static void exceedSpec(SPEC_INF_DTO specInfDto, List<ControlLimitVO> controlLimitVOS) {
        SPEC_ACTIVATION_ALARM_DTO alarmDto = specInfDto.getSpecActivationAlarmDto();
        for (ControlLimitVO controlLimitVO : controlLimitVOS) {
            for (DataPointVO dataPointVO : controlLimitVO.getDataPointVOList()) {
                if (dataPointVO.getPoint() == null) continue;
                if (alarmDto.getF_USL_ALARM() == 1 && specInfDto.getF_USL() != null) {
                    if (dataPointVO.getPoint() > specInfDto.getF_USL()) {

                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.USL.getDesc());
                    }
                }
                if (alarmDto.getF_LSL_ALARM() == 1 && specInfDto.getF_LSL() != null) {
                    if (dataPointVO.getPoint() < specInfDto.getF_LSL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.LSL.getDesc());
                    }
                }
                if (alarmDto.getF_URL_ALARM() == 1 && specInfDto.getF_URL() != null) {
                    if (dataPointVO.getPoint() > specInfDto.getF_URL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.URL.getDesc());
                    }
                }
                if (alarmDto.getF_LRL_ALARM() == 1 && specInfDto.getF_LRL() != null) {
                    if (dataPointVO.getPoint() < specInfDto.getF_LRL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.LRL.getDesc());
                    }
                }
                if (alarmDto.getF_UWL_ALARM() == 1 && specInfDto.getF_UWL() != null) {
                    if (dataPointVO.getPoint() > specInfDto.getF_UWL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.UWL.getDesc());
                    }
                }
                if (alarmDto.getF_LWL_ALARM() == 1 && specInfDto.getF_LWL() != null) {
                    if (dataPointVO.getPoint() < specInfDto.getF_LWL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.LWL.getDesc());
                    }
                }
                if (alarmDto.getF_UWP_ALARM() == 1 && specInfDto.getF_UWP() != null) {
                    if (dataPointVO.getPoint() > specInfDto.getF_UWP()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.UWP.getDesc());
                    }
                }
                if (alarmDto.getF_LWP_ALARM() == 1 && specInfDto.getF_LWP() != null) {
                    if (dataPointVO.getPoint() < specInfDto.getF_LWP()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.LWP.getDesc());
                    }
                }
                if (alarmDto.getF_UAL_ALARM() == 1 && specInfDto.getF_UAL() != null) {
                    if (dataPointVO.getPoint() > specInfDto.getF_UAL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.UAL.getDesc());
                    }
                }
                if (alarmDto.getF_LAL_ALARM() == 1 && specInfDto.getF_LAL() != null) {
                    if (dataPointVO.getPoint() < specInfDto.getF_LAL()) {
                        dataPointVO.setType(YesOrNoEnum.YES.getType());
                        dataPointVO.getSubgroupInfoDTO().setAlarmDetail(SpecificationLimitViolation.LAL.getDesc());
                    }
                }
            }
        }
    }
    public void initData() {
        if (F_PART == null) F_PART = 0L;
        if (F_PRCS == null) F_PRCS = 0L;
        if (F_TEST == null) F_TEST = 0L;
        if (F_PTRV == null) F_PTRV = 0L;
    }
}
