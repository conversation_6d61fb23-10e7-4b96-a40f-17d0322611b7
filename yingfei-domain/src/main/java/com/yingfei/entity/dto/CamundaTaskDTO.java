package com.yingfei.entity.dto;

import lombok.Data;

import java.util.Date;

/**

 BpmTaskExtDO taskExtDO = new BpmTaskExtDO()
 .setTaskId(task.getId())
 .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee())).setName(task.getName())
 .setProcessDefinitionId(task.getProcessDefinitionId()).setProcessInstanceId(task.getProcessInstanceId());
 taskExtDO.setCreateTime(LocalDateTimeUtil.of(task.getCreateTime()));

 */
@Data
public class CamundaTaskDTO {
    private String taskId;
    private String assigneeUserId;
    private String name;
    private String processDefinitionId;
    private String processInstanceId;
    private Date createTime;
    private Long processStartUserId;
    private String processDefinitionName;
    private String describe;
    private String identification;
    private Long F_CRUE;
    private Long F_EDUE;
    private Integer F_IS_UPLOAD;
}
