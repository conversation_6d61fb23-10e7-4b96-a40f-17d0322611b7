package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.PARAMETER_EMPL_LINK;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 参数集表
* @TableName PARAMETER_SET_INF
*/
@Data
public class PARAMETER_SET_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_PRST;
    /**
    * 参数集名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("参数集名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;

    /**
     * 筛选条件list
     */
    @ApiModelProperty("筛选条件list")
    private List<PARAMETER_CHILD_DTO> parameterChildDtoList;

    /**
    * 时间窗口类型。1=动态 0=静态
    */
    @ApiModelProperty("时间窗口类型。1=动态 0=静态")
    private Integer F_TIME_WINDOW_TYPE;
    /**
    * 日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年
    */
    @ApiModelProperty("日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年")
    private Integer F_DATERANGE_TYPE;
    /**
    * 日期范围值
    */
    @ApiModelProperty("日期范围值")
    private Integer F_RANGE_INTERVAL;
    /**
    * 开始时间（仅当选择静态窗口时使用此值）
    */
    @ApiModelProperty("开始时间（仅当选择静态窗口时使用此值）")
    private Date F_START_DATE;
    /**
    * 结束时间（仅当选择静态窗口时使用此值）
    */
    @ApiModelProperty("结束时间（仅当选择静态窗口时使用此值）")
    private Date F_END_DATE;
    /**
    * 最大返回子组数
    */
    @ApiModelProperty("最大返回子组数")
    private Integer F_MAX_ITEM;
    /**
    * 是否包含失效的子组 1=不包含，0=包含，默认值为1
    */
    @ApiModelProperty("是否包含失效的子组 1=不包含，0=包含，默认值为1")
    private Integer F_EXCLUDE_DISABLED_SGS;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录最后编辑用户ID
    */
    @ApiModelProperty("记录最后编辑用户ID")
    private Long F_EDUE;

    /**
     * 筛选条件 解析后每个条件对应的list
     */
    private Map<String, List<Long>> map;

    /**
     * 参数集可见范围
     */
    private List<PARAMETER_EMPL_LINK> parameterEmplLinkList;
}
