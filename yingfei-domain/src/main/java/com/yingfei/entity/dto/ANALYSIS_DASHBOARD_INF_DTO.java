package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 分析页面关联表
* @TableName ANALYSIS_DASHBOARD_INF
*/
@Data
public class ANALYSIS_DASHBOARD_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_DASH;
    /**
    * 菜单表id
    */
    @ApiModelProperty("菜单表id")
    private Long F_MENU;
    /**
    * 参数集表id
    */
    @ApiModelProperty("参数集表id")
    private Long F_PRST;
    /**
    * 分析图表模板id
    */
    @ApiModelProperty("分析图表模板id")
    private Long F_ADTI;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 参数集名称
     */
    private String parameterName;

    /**
     * 子组最大返回数量
     */
    private Integer maxNum;

    /**
     * 分析图表模板名称
     */
    private String templateName;

    /**
     * 分析图表配置
     */
    private String templateConfig;

    /**
     * 分析图表模板类型
     */
    private Integer templateType;

    /**
     * 标题描述
     */
    private String F_TITLE;
}
