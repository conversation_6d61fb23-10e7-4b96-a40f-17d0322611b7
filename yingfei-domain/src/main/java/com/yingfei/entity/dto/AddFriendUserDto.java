package com.yingfei.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddFriendUserDto {

    private static final long serialVersionUID = -5996721893253892290L;

    //用户id
    private Long userId;

    //用户姓名
    private String userName;

    //用户性别（0男 1女 2未知）
    private String sex;

    //个性签名
    private String signature;

    //头像
    private String avatar;

    //头像缩略图
    private String headImageThumb;

    //fixme licheng 部门名称 后续
    private String deptName;

    //fixme licheng 部门全名称。xx集团-xx分公司-xx部门 后续
    private String deptFullName;

    //fixme licheng 职位名称 后续
    private String postName;
}
