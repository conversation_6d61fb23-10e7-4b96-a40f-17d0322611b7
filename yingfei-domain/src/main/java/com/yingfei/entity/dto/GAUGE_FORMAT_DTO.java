package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 量具解析规则配置表
* @TableName GAUGE_FORMAT
*/
@Data
public class GAUGE_FORMAT_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_GAFO;
    /**
    * 解析规则名称
    */
    @ApiModelProperty("解析规则名称")
    private String F_NAME;
    /**
    * 记录长度
    */
    @ApiModelProperty("记录长度")
    private Integer F_LENGTH;
    /**
    * 记录启动符
    */
    @ApiModelProperty("记录启动符")
    private String F_START;
    /**
    * 记录终结符
    */
    @ApiModelProperty("记录终结符")
    private String F_END;
    /**
    * 记录分隔符
    */
    @ApiModelProperty("记录分隔符")
    private String F_SPLIT;
    /**
    * 读取数据配置json
    */
    @ApiModelProperty("读取数据配置json")
    private String F_DATA_CONFIG;

    /**
     * 测量值返回类型(0:A值  1:B值  2:差额(A-B)  3:均值(A,B)  4: 求和(A+B))
     */
    @ApiModelProperty("测量值返回类型(0:A值  1:B值  2:差额(A-B)  3:均值(A,B)  4: 求和(A+B))")
    private Integer F_RETURN_TYPE;

    /**
     * 高级配置
     */
    @ApiModelProperty("高级配置")
    private String F_ADVANCED;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
     * 解析配置类
     */
    @ApiModelProperty("解析配置类")
    private List<GAUGE_FORMAT_CONFIG_DTO> gaugeFormatConfigDtoList;

    /**
     * 高级配置
     */
    private GAUGE_FORMAT_ADVANCED_DTO gaugeFormatAdvancedDto;
}
