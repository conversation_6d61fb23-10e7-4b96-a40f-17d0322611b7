package com.yingfei.entity.dto.globalConfig;

import com.yingfei.common.core.constant.Constants;
import lombok.Data;

@Data
public class BasicConfig{
	/**
	 * 初始版本号
	 */
	private String partRevName;

	/**
	 * 日志保存天数
	 */
	private Integer msgLogSaveDay;

	/**
	 * 实时质量概览统计频率 间隔
	 */
	private Integer interval;

	/**
	 * 实时质量概览统计频率 单位
	 * 时间类型(0:秒 1:分 2:时 3:天)
	 * @see com.yingfei.entity.enums.TimeEnum
	 */
	private Integer timeType;

	public static BasicConfig init() {
		final BasicConfig basicConfig = new BasicConfig();
		basicConfig.setPartRevName(Constants.PREV_VERSION);
		basicConfig.setMsgLogSaveDay(Constants.MSG_LOG_SAVE_DAY);
//		basicConfig.setInterval();
//		basicConfig.setTimeType();
		return basicConfig;
	}
}
