package com.yingfei.entity.dto.chart;

import com.yingfei.entity.dto.DataSummaryDTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 能力仪表盘返回DTO
 */

@Data
@ApiModel
public class InstrumentBoardDTO {

    @ApiModelProperty("子组对应的产品id")
    private Long F_PART;

    @ApiModelProperty("子组对应的产品名称")
    private String F_PARTName;

    @ApiModelProperty("子组对应的产品版本id")
    private Long F_PTRV;

    @ApiModelProperty("子组对应的产品版本名称")
    private String F_PTRVName;

    @ApiModelProperty("子组对应的过程id")
    private Long F_PRCS;

    @ApiModelProperty("子组对应的过程名称")
    private String F_PRCSName;

    @ApiModelProperty("子组对应的测试id")
    private Long F_TEST;

    @ApiModelProperty("测试名称")
    private String F_TESTName;

    @ApiModelProperty("算出来的cp/目标cp")
    private Double cpRatio;

    @ApiModelProperty("算出来的cpk/目标cpk")
    private Double cpkRatio;

    @ApiModelProperty("pp")
    private Double pp;

    @ApiModelProperty("ppk")
    private Double ppk;

    @ApiModelProperty("基本概要")
    private DataSummaryDTO dataSummaryDTO;

    @ApiModelProperty("子组开始时间")
    private Date startTime;

    @ApiModelProperty("子组结束时间")
    private Date endTime;

    public InstrumentBoardDTO() {
    }

    public InstrumentBoardDTO(SubgroupDataDTO subgroupDataDTO) {
        this.F_PART = subgroupDataDTO.getF_PART();
        this.F_PARTName = subgroupDataDTO.getPartName();
        this.F_PRCS = subgroupDataDTO.getF_PRCS();
        this.F_PRCSName = subgroupDataDTO.getPrcsName();
        this.F_TEST = subgroupDataDTO.getF_TEST();
        this.F_TESTName = subgroupDataDTO.getTestName();
        this.F_PTRV = subgroupDataDTO.getF_REV();
        this.F_PTRVName = subgroupDataDTO.getPtrvName();
    }
}
