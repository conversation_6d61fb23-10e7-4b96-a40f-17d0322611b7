package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;

/**
* 储存改善措施信息表
* @TableName RESPONSE_ACTION_DAT
*/
@Data
public class RESPONSE_ACTION_DAT_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    private Long F_RSAT;
    /**
    * 改善措施所关联的改善措施组ID
    */
    @ApiModelProperty("改善措施所关联的改善措施组ID")
    private Long F_RAGP;
    /**
    * 改善措施名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("改善措施名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 改善措施因子，默认为1
    */
    @ApiModelProperty("改善措施因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

}
