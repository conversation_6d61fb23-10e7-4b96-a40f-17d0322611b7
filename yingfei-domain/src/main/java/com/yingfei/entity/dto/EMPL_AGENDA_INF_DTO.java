package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 首页自定义提醒表
* @TableName EMPL_WARN_INF
*/
@Data
public class EMPL_AGENDA_INF_DTO extends BaseEntity {

    private Long F_ID;
    /**
    * 提醒日期,到分钟
    */
    @ApiModelProperty("提醒日期,到分钟")
    private Date F_DATE;
    /**
    * 提醒内容
    */
    @ApiModelProperty("提醒内容")
    private String F_CONTENT;
    /**
    * 提醒通知(1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知)
    */
    @ApiModelProperty("提醒通知(1:系统消息通知 2:邮件通知 3:企业微信通知 4:钉钉通知)")
    private Integer F_TYPE;
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    private Long F_EDUE;

    /**
    * 提醒状态(0:未提醒 1:已提醒)
    */
    @ApiModelProperty("提醒状态(0:未提醒 1:已提醒)")
    private Integer F_STATUS;

    /**
     * 所选用户列表,逗号分割
     */
    private Long F_EMPL;

    private String email;

    private String weChat;

    private String dingDing;
}
