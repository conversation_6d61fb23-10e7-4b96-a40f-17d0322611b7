package com.yingfei.entity.dto;

import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import lombok.Data;

/**
 * 储存检验类型信息表
 * @TableName INSPECTION_TYPE_DAT
 */
@Data
public class INSPECTION_TYPE_DAT_DTO extends BaseEntity {
    /**
     * 记录主键
     */
    private Long F_ID;

    /**
     * 检验类型所关联的检验类型组ID(INSPECTION_TYPE_GRP)
     */
    private Long F_INGP;

    /**
     * 检验类型名称
     */
    private String F_NAME;

    /**
     * 因子，默认为1
     */
    private Double F_FACTOR = 1D;

    /**
     * 是否删除标记，默认值为0
     */
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    /**
     * 记录创建用户ID
     */
    private Long F_CRUE;

    /**
     * 记录编辑用户ID
     */
    private Long F_EDUE;

}
