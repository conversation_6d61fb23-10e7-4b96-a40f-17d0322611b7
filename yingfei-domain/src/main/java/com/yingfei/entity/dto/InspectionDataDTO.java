package com.yingfei.entity.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.utils.I18nUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InspectionDataDTO  implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 抽样任务配置ID
     */
    @ApiModelProperty("抽样任务配置ID")
    private Long samplingTaskConfigId;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String plantName;

    /**
     * 工艺流程名称
     */
    @ApiModelProperty("工艺流程名称")
    private String mfpsName;

    /**
     * 工艺节点名称
     */
    @ApiModelProperty("工艺节点名称")
    private String mfndName;

    /**
     * 检查计划名称
     */
    @ApiModelProperty("检查计划名称")
    private String planName;

    /**
     * 子计划名称
     */
    @ApiModelProperty("子计划名称")
    private String childPlanName;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String partName;

    /**
     * 产品版本名称
     */
    @ApiModelProperty("产品版本名称")
    private String ptrvName;

    /**
     * 过程名称
     */
    @ApiModelProperty("过程名称")
    private String prcsName;

    /**
     * 批次名称
     */
    @ApiModelProperty("批次名称")
    private String lotName;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String jobName;

    /**
     * 工单组名称
     */
    @ApiModelProperty("工单组名称")
    private String jobGrpName;

    /**
     * 班次名称
     */
    @ApiModelProperty("班次名称")
    private String shiftName;

    /**
     * 班次组名称
     */
    @ApiModelProperty("班次组名称")
    private String shiftGrpName;


    /**
     * 描述符
     */
    @ApiModelProperty("描述符")
    private List<Descriptor> descriptors;


    /**
     * 检验数据
     */
    @ApiModelProperty("检验数据")
    private List<Test> testData;

    /**
     * 推送时间
     */
    @ApiModelProperty("推送时间 格式：yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    @JsonFormat(pattern = DatePattern.UTC_MS_PATTERN, timezone = "UTC")
    private Date pushTime;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class Test implements Serializable {
        private static final long serialVersionUID = 1L;
        @NotBlank(message = "testName is not null")
        @ApiModelProperty("测试项名称")
        private String testName;
        /**
         * 测试类型，变量=1；1=变量；2=缺陷；3=不良，默认为变量
         */
        @NotNull(message = "testType is not null")
        @ApiModelProperty("测试类型")
        private Integer testType = 1;

        /**
         * 样本量，默认为1。如果是缺陷或不良类型，需要指定检验值对应的实际样本量
         */
        @ApiModelProperty("样本量")
        private Integer sampleSize = 1;

        @NotNull(message = "testVal is not null")
        @ApiModelProperty("测试值")
        private List<TestVal> testVal;

        @ApiModelProperty("缺陷代码组")
        private String defectGroupName;

        @ApiModelProperty("缺陷代码名称")
        private String defectName;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Valid
    public static class TestVal implements Serializable {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty("测试值")
        private Double val;
        @ApiModelProperty("测试序号")
        private Double no;
        @ApiModelProperty("测试图片")
        private String img;
        @ApiModelProperty("子测试数据")
        private List<SubTest> subTestData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTest implements Serializable {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty("子测试值")
        private Double subTestVal;

        @ApiModelProperty("子测试图片")
        private String subImg;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Descriptor implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("自定义描述符组")
        private String descGrpName;

        @ApiModelProperty("自定义描述符")
        private String descName;
    }
    /**
     * 组装通知消息
     * @param errorMsg
     * @return
     */
    public String buildMsg(String errorMsg) {
        StringBuilder messageBuilder = new StringBuilder();

        // 添加基本信息字段
        addInfoLine(messageBuilder, "PLANT_NAME", plantName);
        addInfoLine(messageBuilder, "MFPS_NAME", mfpsName);
        addInfoLine(messageBuilder, "MFPS_NODE_NAME", mfndName);
        addInfoLine(messageBuilder, "PLAN_NAME", planName);
        addInfoLine(messageBuilder, "CHILD_PLAN_NAME", childPlanName);
        addInfoLine(messageBuilder, "PART_NAME", partName);
        addInfoLine(messageBuilder, "PTRV_NAME", ptrvName);
        addInfoLine(messageBuilder, "PRCS_NAME", prcsName);

        // 添加测试数据
        if (CollectionUtils.isNotEmpty(this.testData)) {
            messageBuilder.append(I18nUtils.getMessage("TEST_DATA")).append("</br>");

            for (InspectionDataDTO.Test test : this.testData) {
                messageBuilder.append("  ").append(I18nUtils.getMessage("TEST_NAME")).append(":")
                        .append(test.getTestName()).append("&lt;/br&gt;");

                if (CollectionUtils.isNotEmpty(test.getTestVal())) {
                    for (InspectionDataDTO.TestVal testVal : test.getTestVal()) {
                        messageBuilder.append("    ").append(I18nUtils.getMessage("TEST_NO")).append(":")
                                .append(testVal.getNo()).append("&lt;/br&gt;");
                        messageBuilder.append("    ").append(I18nUtils.getMessage("TEST_VAL")).append(":")
                                .append(testVal.getVal()).append("&lt;/br&gt;");

                        if(CollectionUtils.isNotEmpty(testVal.getSubTestData())) {
                            for (InspectionDataDTO.SubTest subTestDatum : testVal.getSubTestData()) {
                                messageBuilder.append("      ").append(I18nUtils.getMessage("SUB_TEST_VAL")).append(":")
                                        .append(subTestDatum.getSubTestVal()).append("&lt;/br&gt;");
                            }
                        }
                    }
                }
            }
        }

        // 继续添加其他信息字段
        addInfoLine(messageBuilder, "LOT_NAME", lotName);
        addInfoLine(messageBuilder, "JOB_GRP_NAME", jobGrpName);
        addInfoLine(messageBuilder, "JOB_NAME", jobName);
        addInfoLine(messageBuilder, "SHIFT_GRP_NAME", shiftGrpName);
        addInfoLine(messageBuilder, "SHIFT_NAME", shiftName);
        addInfoLine(messageBuilder, "PUSH_TIME", pushTime);

        // 错误信息
        messageBuilder.append(I18nUtils.getMessage("ERROR_MSG")).append(":").append(errorMsg);

        return messageBuilder.toString();
    }

    /**
     * 辅助方法，用于添加信息行
     */
    private void addInfoLine(StringBuilder messageBuilder, String key, Object value) {
        messageBuilder.append(I18nUtils.getMessage(key)).append(":")
                .append(value != null ? value.toString() : "").append("&lt;/br&gt;");
    }
//    public String buildMsg(String errorMsg) {
//        String testData = "";
//        if (StringUtils.isNotBlank(testData)) {
//            final List<InspectionDataDTO.Test> tests = JSONArray.parseArray(testData, InspectionDataDTO.Test.class);
//            for (InspectionDataDTO.Test test : tests) {
//                testData +=  I18nUtils.getMessage("TEST_NAME") + ":" +test.getTestName() + "\n\t";
//                for (InspectionDataDTO.TestVal testVal : test.getTestVal()) {
//                    testData +=  I18nUtils.getMessage("TEST_NO") + ":" +testVal.getNo() + "\n\t" +
//                            I18nUtils.getMessage("TEST_VAL") + ":" +testVal.getVal() + "\n\t\t";
//                    if(CollectionUtils.isNotEmpty(testVal.getSubTestData())){
//                        for (InspectionDataDTO.SubTest subTestDatum : testVal.getSubTestData()) {
//                            testData +=  I18nUtils.getMessage("SUB_TEST_VAL") + ":" +subTestDatum.getSubTestVal() + "\n";
//                        }
//                    }
//                }
//            }
//        }
//        return I18nUtils.getMessage("PLANT_NAME") + ":" + plantName + "\n" +
//                I18nUtils.getMessage("MFPS_NAME") + ":" + mfpsName + "\n" +
//                I18nUtils.getMessage("MFPS_NODE_NAME") + ":" + mfndName + "\n" +
//                I18nUtils.getMessage("PLAN_NAME") + ":" + planName + "\n" +
//                I18nUtils.getMessage("CHILD_PLAN_NAME") + ":" + childPlanName + "\n" +
//                I18nUtils.getMessage("PART_NAME") + ":" + partName + "\n" +
//                I18nUtils.getMessage("PTRV_NAME") + ":" + ptrvName + "\n" +
//                I18nUtils.getMessage("PRCS_NAME") + ":" + prcsName + "\n" +
//                I18nUtils.getMessage("TEST_DATA") + ":" + testData + "\n" +
//                I18nUtils.getMessage("LOT_NAME") + ":" + lotName + "\n" +
//                I18nUtils.getMessage("JOB_GRP_NAME") + ":" + jobGrpName + "\n" +
//                I18nUtils.getMessage("JOB_NAME") + ":" + jobName + "\n" +
//                I18nUtils.getMessage("SHIFT_GRP_NAME") + ":" + shiftGrpName + "\n" +
//                I18nUtils.getMessage("SHIFT_NAME") + ":" + shiftName + "\n" +
//                I18nUtils.getMessage("PUSH_TIME") + ":" + pushTime + "\n" +
//                I18nUtils.getMessage("ERROR_MSG") + ":" + errorMsg + "\n";
//    }
}
