package com.yingfei.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InspectionDataDTO  implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 抽样任务配置ID
     */
    @ApiModelProperty("抽样任务配置ID")
    private Long samplingTaskConfigId;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String plantName;

    /**
     * 工艺流程名称
     */
    @ApiModelProperty("工艺流程名称")
    private String mfpsName;

    /**
     * 工艺节点名称
     */
    @ApiModelProperty("工艺节点名称")
    private String mfndName;

    /**
     * 检查计划名称
     */
    @ApiModelProperty("检查计划名称")
    private String planName;

    /**
     * 子计划名称
     */
    @ApiModelProperty("子计划名称")
    private String childPlanName;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String partName;

    /**
     * 产品版本名称
     */
    @ApiModelProperty("产品版本名称")
    private String ptrvName;

    /**
     * 过程名称
     */
    @ApiModelProperty("过程名称")
    private String prcsName;

    /**
     * 批次名称
     */
    @ApiModelProperty("批次名称")
    private String lotName;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String jobName;

    /**
     * 工单组名称
     */
    @ApiModelProperty("工单组名称")
    private String jobGrpName;

    /**
     * 班次名称
     */
    @ApiModelProperty("班次名称")
    private String shiftName;

    /**
     * 班次组名称
     */
    @ApiModelProperty("班次组名称")
    private String shiftGrpName;


    /**
     * 描述符
     */
    @ApiModelProperty("描述符")
    private List<Descriptor> descriptors;



    /**
     * 检验数据
     */
    @ApiModelProperty("检验数据")
    private List<Test> testData;

    /**
     * 推送时间
     */
    @ApiModelProperty("推送时间 格式：yyyy-MM-dd'T'HH:mm:ss.SSS'Z' ")
    private Date pushTime;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Test  implements Serializable {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty("测试项名称")
        private String testName;
        /**
         * 测试类型，变量=1；1=变量；2=缺陷；3=不良，默认为变量
         */
        @ApiModelProperty("测试类型")
        private Integer testType=1;

        /**
         * 样本量，默认为1。如果是缺陷或不良类型，需要指定检验值对应的实际样本量
         */
        @ApiModelProperty("样本量")
        private Integer sampleSize=1;

        @ApiModelProperty("测试值")
        private List<TestVal> testVal;

        @ApiModelProperty("缺陷代码组")
        private String defectGroupName;

        @ApiModelProperty("缺陷代码名称")
        private String defectName;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TestVal implements Serializable {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty("测试值")
        private Double val;
        @ApiModelProperty("测试序号")
        private Double no;
        @ApiModelProperty("测试图片")
        private String img;
        @ApiModelProperty("子测试数据")
        private List<SubTest> subTestData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTest implements Serializable {
        private static final long serialVersionUID = 1L;
        @ApiModelProperty("子测试值")
        private Double subTestVal;

        @ApiModelProperty("子测试图片")
        private String subImg;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Descriptor implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("自定义描述符组")
        private String descGrpName;

        @ApiModelProperty("自定义描述符")
        private String descName;
    }


}
