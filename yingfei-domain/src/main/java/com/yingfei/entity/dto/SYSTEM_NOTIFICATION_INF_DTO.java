package com.yingfei.entity.dto;

import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户系统消息表
 */
@Data
@ApiModel
public class SYSTEM_NOTIFICATION_INF_DTO extends BaseEntity {

    private Long F_SYNO;

    @ApiModelProperty("系统消息")
    private String F_DATA;

    @ApiModelProperty("消息状态(0:未读 1:已读)")
    private Integer F_STATUS;

    @ApiModelProperty("通知人")
    private Long F_EMPL;


    private Long F_CRUE;

    private Long F_EDUE;

    @ApiModelProperty("消息类型(1:普通消息 2:报警消息)")
    private Integer F_TYPE;

    @ApiModelProperty("报警通知")
    private SYSTEM_NOTIFICATION_ALARM_DTO systemNotificationAlarmDto;
}
