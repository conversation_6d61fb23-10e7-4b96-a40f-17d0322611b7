package com.yingfei.entity.util.warningRule;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yingfei.entity.domain.RULE_INF;
import com.yingfei.entity.dto.*;
import com.yingfei.entity.enums.ControlChartSingleEnum;
import com.yingfei.entity.enums.StatisticalViolationTypeEnum;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import com.yingfei.entity.util.MonitorWarningRuleUtil;
import com.yingfei.entity.util.WarningRuleService;
import com.yingfei.entity.vo.ControlLimitVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报警规则Service接口实现类
 */
@Slf4j
@Service
public class WarningRuleServiceImpl implements WarningRuleService {

    @Override
    public List<String> judgeWarningRule(List<RULE_INF> alrRuleList, List<ControlLimitVO> controlLimitVOList, DataSummaryDTO dataSummaryDTO, SPEC_INF_DTO specInfDto) {
        List<String> ruleList = new ArrayList<>();
        /*获取所有报警规则*/
//        List<RULE_DEF> ruleDefs = ruleDefMapper.selectList(new LambdaQueryWrapper<>());
//        if (CollectionUtils.isEmpty(ruleDefs)) {
//            return ruleList;
//        }
//        ruleDefs = ruleDefs.stream().filter(a -> !a.getF_NAME().toLowerCase().contains("pc")).collect(Collectors.toList());
//        Map<Integer, List<RULE_DEF>> map = ruleDefs.stream().collect(Collectors.groupingBy(RULE_DEF::getF_RULE));

        for (RULE_INF alrRule : alrRuleList) {
            StatisticalViolationTypeEnum ruleEnum = StatisticalViolationTypeEnum.getType(alrRule.getF_RULE_TYPE());
            if (Objects.isNull(ruleEnum)) {
                continue;
            }
            ruleList.add(alrRule.getF_ABBR());
            for (ControlLimitVO controlLimitVO : controlLimitVOList) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put(MonitorWarningRuleUtil.HITS, alrRule.getF_HITS());
                hashMap.put(MonitorWarningRuleUtil.CNT, alrRule.getF_COUNT());
                hashMap.put("ucl", controlLimitVO.getUcl());
                hashMap.put("lcl", controlLimitVO.getLcl());
                hashMap.put("cl", controlLimitVO.getCl());
                if (alrRule.getF_RULE_TYPE().equals(StatisticalViolationTypeEnum.CPK_IS_BELOW_THE_TARGET.getCode())) {
                    hashMap.put(MonitorWarningRuleUtil.DATA_SUMMARY_DTO, JSONArray.toJSONString(dataSummaryDTO));
                    hashMap.put(MonitorWarningRuleUtil.SPEC_INF_DTO, JSONObject.toJSONString(specInfDto));
                }
                hashMap.put(MonitorWarningRuleUtil.TYPE, ruleEnum.getType());
                StatisticalViolationTypeEnum.invoke(ruleEnum, controlLimitVO.getDataPointVOList(), hashMap);
            }


        }
        return ruleList;
    }

    @Override
    public List<AlarmMessageDTO> monitorJudgeWarningRule(List<RULE_INF> alrRuleList, List<SubgroupDataVO> subgroupDataVOList, ControlChartSingleEnum chartSingleEnum, SPEC_INF_DTO specInfDto, Integer num) {
        if (CollectionUtils.isEmpty(alrRuleList)) {
            return Collections.emptyList();
        }
        List<AlarmMessageDTO> array = new ArrayList<>();
        for (RULE_INF alrRule : alrRuleList) {
            StatisticalViolationTypeEnum ruleEnum = StatisticalViolationTypeEnum.getType(alrRule.getF_RULE_TYPE());
            if (Objects.isNull(ruleEnum)) {
                continue;
            }
            Integer fCount = alrRule.getF_COUNT();
            fCount = fCount + ruleEnum.getDataPointDispose();
            Integer fHits = alrRule.getF_HITS();
            List<SubgroupDataVO> list = new ArrayList<>();
            if (fCount == 1) {
                list = Collections.singletonList(subgroupDataVOList.get(subgroupDataVOList.size() - 1));
            } else if (fHits <= subgroupDataVOList.size()) {
                if (fCount <= subgroupDataVOList.size()) {
                    list = subgroupDataVOList.subList((subgroupDataVOList.size() - fCount) == 0 ? 0 : subgroupDataVOList.size() - fCount,
                            subgroupDataVOList.size());
                } else {
                    list = subgroupDataVOList.subList((subgroupDataVOList.size() - fHits) == 0 ? 0 : subgroupDataVOList.size() - fHits,
                            subgroupDataVOList.size());
                }
            } else {
                continue;
            }

            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(MonitorWarningRuleUtil.HITS, fHits);
            hashMap.put(MonitorWarningRuleUtil.CNT, fCount);
            hashMap.put(MonitorWarningRuleUtil.TYPE, ruleEnum.getType());
            hashMap.put(MonitorWarningRuleUtil.AREA, ruleEnum.getArea());
            AlarmMessageDTO alarmMessageDTO=null;

            if (alrRule.getF_RULE_TYPE().equals(StatisticalViolationTypeEnum.CPK_IS_BELOW_THE_TARGET.getCode()) && subgroupDataVOList.size() >= 1) {
                if(subgroupDataVOList.size()==1 && subgroupDataVOList.get(0).getSgrpValChildDto().getNum()==1) {
                  /*只有一组数据，且样本量为1，不判断报警*/
                    continue;
                }
                final SubgroupDataVO subgroupDataVO = subgroupDataVOList.get(0);
                final SGRP_VAL_CHILD_DTO sgrpValChildDto = subgroupDataVO.getSgrpValChildDto();
                if (ObjectUtils.isNotEmpty(sgrpValChildDto)) {
                    if (Integer.valueOf(TEST_INF_TYPEEnum.VARIABLE.getType()).equals(sgrpValChildDto.getTestType())) {
                        if (subgroupDataVOList.size() > fCount) {
                            list = subgroupDataVOList.subList(subgroupDataVOList.size() - fCount, subgroupDataVOList.size());
                        } else {
                            list = subgroupDataVOList;
                        }
                        DataSummaryDTO dataSummaryDTO = DataSummaryDTO.monitorGetCPAndCpk(list, specInfDto);
                        //log.info("dataSummaryDTO:{}", dataSummaryDTO);
                        hashMap.put(MonitorWarningRuleUtil.DATA_SUMMARY_DTO, JSONObject.toJSONString(dataSummaryDTO));
                        hashMap.put(MonitorWarningRuleUtil.SPEC_INF_DTO, JSONObject.toJSONString(specInfDto));
                        if (dataSummaryDTO.getCp() < specInfDto.getF_CP()) {
                            alarmMessageDTO = new AlarmMessageDTO();
                            alarmMessageDTO.setCompareValue(specInfDto.getF_CP());
                            alarmMessageDTO.setActualValue(dataSummaryDTO.getCp());
                            alarmMessageDTO.setConnector("<");
                        } else if (dataSummaryDTO.getCpk() < specInfDto.getF_CPK()) {
                            alarmMessageDTO = new AlarmMessageDTO();
                            alarmMessageDTO.setCompareValue(specInfDto.getF_CPK());
                            alarmMessageDTO.setActualValue(dataSummaryDTO.getCpk());
                            alarmMessageDTO.setConnector("<");
                        }
                    }
                }
            }
            if(Objects.isNull(alarmMessageDTO) && alrRule.getF_RULE_TYPE().equals(StatisticalViolationTypeEnum.CPK_IS_BELOW_THE_TARGET.getCode())) {continue;}
            alarmMessageDTO = new AlarmMessageDTO();
            List<CalculatedControlLimit> calculatedControlLimitList = list.stream()
                    .map(SubgroupDataVO::getCalculatedControlLimits).flatMap(List::stream).filter(s -> s.getChartSingleEnum().equals(chartSingleEnum)).collect(Collectors.toList());
            StatisticalViolationTypeEnum.monitorInvoke(ruleEnum, calculatedControlLimitList, hashMap);
            Object o = hashMap.get(MonitorWarningRuleUtil.details);
            if (o != null) {
                alarmMessageDTO.setName(alrRule.getF_NAME());
                alarmMessageDTO.setAlarmNameAbbr(alrRule.getF_ABBR());
                alarmMessageDTO.setId(alrRule.getF_ALR());
                alarmMessageDTO.setRuleType(ruleEnum.getType());
                alarmMessageDTO.setType(2);
                alarmMessageDTO.setChart(chartSingleEnum.getDescription());
                alarmMessageDTO.setChartAbbr(chartSingleEnum.getAbbr());
                alarmMessageDTO.setFlag(alrRule.getF_FLAG() == null ? 0 : alrRule.getF_FLAG());

                if (hashMap.get(MonitorWarningRuleUtil.UCL) != null) {
                    alarmMessageDTO.setCompareValue(Double.parseDouble(hashMap.get(MonitorWarningRuleUtil.UCL).toString()));
                    alarmMessageDTO.setActualValue(Double.parseDouble(hashMap.get(MonitorWarningRuleUtil.UCL_ACTUALVALUE).toString()));
                    alarmMessageDTO.setConnector(">");
                }
                if (hashMap.get(MonitorWarningRuleUtil.LCL) != null) {
                    alarmMessageDTO.setCompareValue(Double.parseDouble(hashMap.get(MonitorWarningRuleUtil.LCL).toString()));
                    alarmMessageDTO.setActualValue(Double.parseDouble(hashMap.get(MonitorWarningRuleUtil.LCL_ACTUALVALUE).toString()));
                    alarmMessageDTO.setConnector("<");
                }
                array.add(alarmMessageDTO);
            }
        }
        return array;
    }

    public static void main(String[] args) {
        String binary = Integer.toBinaryString(2040);
        binary = String.format("%32s", binary).replace(" ", "0");
        System.err.println(binary);
    }
}
