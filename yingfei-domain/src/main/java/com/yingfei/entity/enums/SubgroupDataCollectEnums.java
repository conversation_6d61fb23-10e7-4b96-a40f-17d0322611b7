package com.yingfei.entity.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * 新增子组数据采集方式
 */
@Getter
public enum SubgroupDataCollectEnums {

    HISTORY(1, "历史数据导入"),
    AUTO(2, "数据采集"),
    push(3, "推送"),
    ;

    private Integer code;

    private String desc;

    SubgroupDataCollectEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SubgroupDataCollectEnums get(Integer code) {
        return Arrays.stream(SubgroupDataCollectEnums.values())
                .filter(codeEnum -> Objects.equals(codeEnum.getCode(), code))
                .findFirst().orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(SubgroupDataCollectEnums.values()).filter(it -> it.getCode().equals(code))
                .findFirst().map(SubgroupDataCollectEnums::getDesc).orElse("");
    }

    public static Integer getCode(String desc) {
        return Arrays.stream(SubgroupDataCollectEnums.values()).filter(it -> it.getDesc().equals(desc))
                .findFirst().map(SubgroupDataCollectEnums::getCode).orElse(null);
    }
}
