package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抽样类型枚举
 */
@Getter
@AllArgsConstructor
public enum SamplingTypeEnum {

    /**
     * 时间抽样
     */
    TIME_BASED(1, "时间抽样"),

    /**
     * 生产节拍
     */
    PRODUCTION_BEAT(2, "生产节拍");

    private final Integer type;
    private final String description;

    public static SamplingTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SamplingTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
