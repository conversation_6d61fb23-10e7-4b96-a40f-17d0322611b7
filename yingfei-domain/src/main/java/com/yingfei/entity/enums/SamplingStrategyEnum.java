package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抽样策略枚举
 */
@Getter
@AllArgsConstructor
public enum SamplingStrategyEnum {

    /**
     * 基于产品
     */
    BY_PRODUCT(1, "基于产品"),

    /**
     * 基于过程
     */
    BY_PROCESS(2, "基于过程"),

    /**
     * 基于产品过程测试
     */
    BY_PRODUCT_PROCESS(3, "基于产品过程"),


    /**
     * 基于推送数量
     */
    BY_PUSH_COUNT(4, "基于推送数量");

    private final Integer type;
    private final String description;

    public static SamplingStrategyEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SamplingStrategyEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
