package com.yingfei.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 抽样方式枚举
 */
@Getter
@AllArgsConstructor
public enum SamplingMethodEnum {

    /**
     * 随机
     */
    RANDOM(1, "随机"),

    /**
     * 时间升序
     */
    TIME_ASC(2, "时间升序"),

    /**
     * 时间倒序
     */
    TIME_DESC(3, "时间倒序");

    private final Integer type;
    private final String description;

    public static SamplingMethodEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SamplingMethodEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
